form{
  ul{
    padding: 0;
    .sf-field-taxonomy-distributor_categories{
      width: 100% !important;
      border: 0 !important;
      padding: 0;
      > ul{
        display: flex;
        width: 100% !important;
        position: initial !important;
        visibility: unset !important;
        height: auto !important;
        border: 0 !important;
        background: transparent !important;
        padding: 0;
        @media screen and (max-width: 768px){
          display: block;
        }
        li{
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          position: relative;
          @media screen and (max-width: 768px){
            display: inline-block;
          }
          @media screen and (max-width: 485px){
            margin-right: 15px;
          }
          &.sf-item-0{
            &:first-child{
              font-weight: 700;
            }
          }
          &.sf-option-active{
            padding: 0;
            background: transparent;
            .sf-label-radio{
              font-weight: 700;
              &:after{
                transform: scaleX(1);
              }
            }
            &:after{
              transform: scaleX(1);
              &:hover{
                display: none;
              }
            }
          }
          .sf-input-radio{
            display: none;
          }
          .sf-label-radio{
            padding: 0;
            font-size: 14px;
            font-weight: 100;
            line-height: 1.2;
            color: #000;
            cursor: pointer;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-decoration: none;
            transition: all 0.2s ease;
            @media screen and (max-width: 485px){
              font-size: 20px;
            }
            &:before{
              content: attr(data-text);
              content: attr(data-text) / "";
              height: 0;
              visibility: hidden;
              overflow: hidden;
              user-select: none;
              pointer-events: none;
              font-weight: 700;
            }
            &:after {
              &:hover{
                font-weight: 700;
                text-decoration: none;
              } 
            }  
          }
          &:after {
            content: '';
          }
          &:hover{
            .sf-label-radio{
              color: #000;
              -webkit-text-fill-color: #000;
              -webkit-text-stroke-width: 1px;
              -webkit-text-stroke-color: #000;
              text-decoration: none;
            }
            &:after {
              transform: scaleX(1);
              transform-origin: bottom left;
            }
          }
        }
      }
      &:after{
        display: none;
      }      
    }
  }
}