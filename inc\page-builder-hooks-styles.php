<?php
function row_style_background_area( $fields ) {
    $fields['background_area'] = array(
        'name' => __( 'Add background area', 'radar_child_fd' ),
        'type' => 'select',
        'group' => 'design',
        'description' => __( 'Add more background ared left or right', 'radar_child_fd' ),
        'priority' => 6,
        'options' => array(
            '' => __('-', 'radar_child_fd'),
            'bg-left' => __('Left', 'radar_child_fd'),
            'bg-right' => __('right', 'radar_child_fd'),
            'bg-left-bg-right' => __('Left and right', 'radar_child_fd'),
        )
    );

    return $fields;
}
add_filter( 'siteorigin_panels_row_style_fields', 'row_style_background_area' );
add_filter( 'siteorigin_panels_cell_style_fields', 'row_style_background_area' );

function row_style_background_area_style_attributes( $attributes, $args ) {
    if ( ! empty( $args['background_area'] ) ) {
        array_push( $attributes['class'], $args['background_area'] );
    }
    return $attributes;
}
add_filter( 'siteorigin_panels_row_style_attributes', 'row_style_background_area_style_attributes', 10, 2 );
add_filter( 'siteorigin_panels_cell_style_attributes', 'row_style_background_area_style_attributes', 10, 2 );

function widget_style_font_bold( $fields ){
    $fields['font_bold'] = array(
        'name' => __( 'Font weight bold', 'radar_child_fd' ),
        'type' => 'checkbox',
        'group' => 'design',
        'description' => __( 'Font weight bold of text inside this widget.', 'radar_child_fd' ),
        'priority' => 16,
    );

    return $fields;
}
add_filter( 'siteorigin_panels_widget_style_fields', 'widget_style_font_bold' );

function widget_style_font_bold_attributes( $attributes, $style ){
    if ( ! empty( $style[ 'font_bold' ] ) ) {
        $attributes['class'][] = 'all-font-bold';
    }
    return $attributes;
}
add_filter( 'siteorigin_panels_widget_style_attributes' , 'widget_style_font_bold_attributes', 10, 2);
?>