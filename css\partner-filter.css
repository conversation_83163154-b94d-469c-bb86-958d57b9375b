form ul {
  padding: 0;
}
form ul .sf-field-taxonomy-partners_categories {
  width: 100% !important;
  border: 0 !important;
}
form ul .sf-field-taxonomy-partners_categories > ul {
  display: flex;
  width: 100% !important;
  position: initial !important;
  visibility: unset !important;
  height: auto !important;
  border: 0 !important;
  background: transparent !important;
  padding: 0;
}
@media screen and (max-width: 768px) {
  form ul .sf-field-taxonomy-partners_categories > ul {
    display: block;
  }
}
form ul .sf-field-taxonomy-partners_categories > ul li {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  position: relative;
  margin-right: 30px;
}
@media screen and (max-width: 768px) {
  form ul .sf-field-taxonomy-partners_categories > ul li {
    display: inline-block;
    /*padding-bottom: 15px;*/
  }
}
@media screen and (max-width: 485px) {
  form ul .sf-field-taxonomy-partners_categories > ul li {
    margin-right: 15px;
  }
}
form ul .sf-field-taxonomy-partners_categories > ul li.sf-option-active {
  padding: 0;
  background: transparent;
}
form ul .sf-field-taxonomy-partners_categories > ul li.sf-option-active .sf-label-radio {
  font-weight: 700;
}
form ul .sf-field-taxonomy-partners_categories > ul li.sf-option-active .sf-label-radio:after {
  transform: scaleX(1);
}
form ul .sf-field-taxonomy-partners_categories > ul li.sf-option-active:after {
  transform: scaleX(1);
}
form ul .sf-field-taxonomy-partners_categories > ul li.sf-option-active:after:hover {
  display: none;
}
form ul .sf-field-taxonomy-partners_categories > ul li .sf-input-radio {
  display: none;
}
form ul .sf-field-taxonomy-partners_categories > ul li .sf-label-radio {
  padding: 0;
  font-size: 24px;
  font-weight: 300;
  line-height: 1.2;
  color: #3D4763;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
}
@media screen and (max-width: 485px) {
  form ul .sf-field-taxonomy-partners_categories > ul li .sf-label-radio {
    font-size: 20px;
  }
}
form ul .sf-field-taxonomy-partners_categories > ul li .sf-label-radio:before {
  content: attr(data-text);
  content: attr(data-text)/"";
  height: 0;
  visibility: hidden;
  overflow: hidden;
  user-select: none;
  pointer-events: none;
  font-weight: 700;
}
form ul .sf-field-taxonomy-partners_categories > ul li .sf-label-radio:after:hover {
  font-weight: 700;
  text-decoration: none;
}
form ul .sf-field-taxonomy-partners_categories > ul li:after {
  content: '';
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 1px;
  bottom: 0;
  left: 0;
  background: linear-gradient(151deg, rgba(255, 255, 255, 0) 0%, #29c17a 100%);
  transform-origin: bottom left;
  transition: transform 0.5s ease-out;
}
@media screen and (max-width: 768px) {
  form ul .sf-field-taxonomy-partners_categories > ul li:after {
    bottom: -5px !important;
    transform-origin: unset;
    transition: none;
  }
}
form ul .sf-field-taxonomy-partners_categories > ul li:hover .sf-label-radio {
  font-weight: 700;
  text-decoration: none;
}
form ul .sf-field-taxonomy-partners_categories > ul li:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}
form ul .sf-field-taxonomy-partners_categories:after {
  display: none;
}

.loop-grid .card {
  border: 0;
  background-color: transparent;
  margin-bottom: 65px;
}
.loop-grid .card .image-wrapper {
  width: 100%;
  max-width: 375px;
  height: auto;
  max-height: 250px;
  overflow: hidden;
  background-position: center;
  background-size: cover;
}
@media screen and (max-width: 768px) {
  .loop-grid .card .image-wrapper {
    max-width: 100%;
  }
}
.loop-grid .card .image-wrapper .card-img-top, .loop-grid .card .image-wrapper .card-vdo-top {
  width: auto;
  min-height: 250px;
  object-fit: cover;
  object-position: center;
  border-radius: 0;
  transition: transform .5s ease;
}
.loop-grid .card .card-body .post-date {
  font-size: 14px;
  color: #949DA7;
}
.loop-grid .card .card-body .card-title {
  font-size: 20px;
  line-height: 1.2;
  margin: 10px 0 0 0;
  color: #3D4763;
  height: 48px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.loop-grid .card .card-body .card-text {
  margin: 10px 0 0 0;
}
.loop-grid .card .card-body .card-text p {
  font-size: 16px;
  line-height: 1.5;
  margin: 10px 0 0 0;
  height: 72px;
  color: #3D4763;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.loop-grid .card:hover .card-img-top {
  transform: scale(1.2);
}
.loop-grid .card:hover .card-body {
  background-color: #FFF;
}
.loop-grid .card:hover .card-body .card-title {
  font-weight: 700;
}
.loop-grid .card:hover .card-body .btn-link:after {
  margin-left: 15px;
  background-image: url(../images/arrow-right-green.svg);
}

.single-partners h1 {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: #77C2A1;
}
.single-partners h1::before {
  content: '';
  background: url("../images/menu-hover-left.svg") no-repeat left center;
  left: -10px;
}
.single-partners h1::after {
  background: url("../images/menu-hover-right.svg") no-repeat right center;
  right: -10px;
}
