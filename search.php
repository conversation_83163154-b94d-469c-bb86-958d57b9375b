<?php
get_header(); ?>
	<div class="container py-5">
		<h2 class="font-weight-bold mb-3"><?php printf( __( 'Search Results for: %s', 'radar_fd' ), '<span>' . get_search_query() . '</span>' ); ?></h2>
		<?php //get_template_part('loops/loop', 'list'); ?>
		<div class="">
		<?php
		if (have_posts()){
			while (have_posts()){
				the_post();
				
				if(get_post_type() == 'resource_library'){
					wp_enqueue_script('jquery-expander');
    				wp_enqueue_script('resource-library-expander');
					get_template_part('loops/loop-template/resource-library');
				}else{
					get_template_part('loops/loop-template/search-reslut');
				}
			}
			if(function_exists('wp_pagenavi')){
				echo wp_pagenavi();
			}else{
				if($wp_query->max_num_pages > 1){
					?>
					<div class="pager container">
						<div class="row">
							<?php next_posts_link(__('<div class="col-xs=6"><span class="meta-nav">&larr;</span> Older posts</div>', 'radar_fd' )); ?>
							<?php previous_posts_link(__('<div class="col-xs=6">Newer posts <span class="meta-nav">&rarr;</span></div>', 'radar_fd')); ?>
						</div>
					</div>
					<?php
				}
			}
		}else{
			?>
			<h2 class="font-weight-bold mb-3"><?php echo __( 'Nothing Found', 'radar_fd' ); ?></h2>
			<?php
				if ( is_search() ) : ?>

					<p><?php echo __('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'radar_fd'); ?></p>
					<?php get_search_form(); ?>

				<?php else : ?>

					<p><?php echo __('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'radar_fd'); ?></p>
					<?php get_search_form(); ?>

				<?php endif; ?>
			<?php
		}
		?>
		</div>
		<?php wp_reset_postdata(); ?>
	</div>
<?php get_footer(); ?>
