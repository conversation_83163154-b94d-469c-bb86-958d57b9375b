<?php
function theme_contact_form_enqueue_scripts() {
    wp_enqueue_script('theme-contact-form', get_stylesheet_directory_uri().'/js/contact-form.js',array('jquery'),'',true);
    wp_localize_script( 'theme-contact-form', 'theme_contact_form_data',
        array(
            'tray_evaluation_form_id' => array(1226,2283),
            'tray_evaluation_form_success_message' => sprintf(__('<div class="success-message">
<p><strong>Thank you.</strong></p>
<p><strong>Your contact request</strong> has been sent. We’ll get back to you as soon as possible</p>
<div class="green-line uk-scrollspy-inview uk-animation-slide-left"></div>
<p style="text-align: center;"><a class="btn-secondary close-popup">Close</a></p>
</div>', 'radar_fd_child')),
            'banner_form_id' => array(1838,1839,2301,2302,2509,2510),
            'banner_form_message' => sprintf(__('<div class="success-message">
<p><strong>Thank you.</strong></p>
<p><strong>Your contact request</strong> has been sent. We’ll get back to you as soon as possible</p>
<div class="green-line uk-scrollspy-inview uk-animation-slide-left"></div>
<p style="text-align: center;"><a class="btn-secondary close-popup">Close</a></p>
</div>', 'radar_fd_child')),
        )
    );
}
add_action( 'wp_enqueue_scripts', 'theme_contact_form_enqueue_scripts');

function add_empty_select_field_values($scanned_tag, $replace){
    if ( $scanned_tag['name'] == 'choose-contact' || $scanned_tag['name'] == 'choose-denester' || $scanned_tag['name'] == 'reason-for-shipment' ){
        $scanned_tag['raw_values'][0] = '';
        $pipes = new WPCF7_Pipes($scanned_tag['raw_values']);
        $scanned_tag['values'] = $pipes->collect_befores();
        $scanned_tag['pipes'] = $pipes;
    }
    return $scanned_tag;
}
add_filter( 'wpcf7_form_tag', 'add_empty_select_field_values', 10, 2);

function choosecontact_select_field_values ( $scanned_tag, $replace ) { 
    if ( $scanned_tag['name'] != 'choose-contact' )
        return $scanned_tag;

    $scanned_tag['raw_values'][0] = '';
    $pipes = new WPCF7_Pipes($scanned_tag['raw_values']);

    $scanned_tag['values'] = $pipes->collect_befores();
    //$scanned_tag['labels'] = $pipes->collect_afters();
    $scanned_tag['pipes'] = $pipes;
    // if($scanned_tag['values']){
    //     foreach ($scanned_tag['values'] as $key => $contact_name) {
    //         $contact = get_page_by_title($contact_name, 'OBJECT', 'employee');
    //         $email = get_field('email', $contact->ID);
    //         $scanned_tag['raw_values'][$key] = $email;
    //     }

    //     $pipes = new WPCF7_Pipes($scanned_tag['raw_values']);

    //     $scanned_tag['values'] = $pipes->collect_befores();
    //     //$scanned_tag['labels'] = $pipes->collect_afters();
    //     $scanned_tag['pipes'] = $pipes;
    // }
    
    return $scanned_tag;
    
}
//add_filter( 'wpcf7_form_tag', 'choosecontact_select_field_values', 10, 2);

function choose_different_tray_field_values($scanned_tag, $replace){
    if ( $scanned_tag['name'] != 'choose-different-tray' )
        return $scanned_tag;

    $scanned_tag['raw_values'][0] = '';
    $terms_args = array(
        'taxonomy' => 'product_type',
        'hide_empty' => false,
    );
    $terms = get_terms($terms_args);
    if($terms){
        foreach ($terms as $key => $term) {
            $scanned_tag['labels'][] = $term->name;
            $scanned_tag['raw_values'][] = $term->name;
        }

        $pipes = new WPCF7_Pipes($scanned_tag['raw_values']);
        $scanned_tag['values'] = $pipes->collect_befores();
        //$scanned_tag['labels'] = $pipes->collect_afters();
        $scanned_tag['pipes'] = $pipes;
    }

    return $scanned_tag;
}
//add_filter( 'wpcf7_form_tag', 'choose_different_tray_field_values', 10, 2);

function capacity_select_field_values($scanned_tag, $replace){
    if ( $scanned_tag['name'] != 'capacity' )
        return $scanned_tag;

    $scanned_tag['raw_values'][0] = '';
    $terms_args = array(
        'taxonomy' => 'capacity_online',
        'hide_empty' => false,
    );
    $terms = get_terms($terms_args);
    if($terms){
        foreach ($terms as $key => $term) {
            $scanned_tag['labels'][] = $term->name;
            $scanned_tag['raw_values'][] = $term->name;
        }

        $pipes = new WPCF7_Pipes($scanned_tag['raw_values']);
        $scanned_tag['values'] = $pipes->collect_befores();
        //$scanned_tag['labels'] = $pipes->collect_afters();
        $scanned_tag['pipes'] = $pipes;
    }

    return $scanned_tag;
}
add_filter( 'wpcf7_form_tag', 'capacity_select_field_values', 10, 2);

function wpcf7_send_a_copy($WPCF7_ContactForm){
    $currentformInstance  = WPCF7_ContactForm::get_current();
    $contactformsubmition = WPCF7_Submission::get_instance();

    if ($contactformsubmition) {
        if($_POST['send-copy']){
            $data = $contactformsubmition->get_posted_data();

            if (empty($data))
                return;
            
            $mail = $currentformInstance->prop('mail');

            if(!empty($_POST['email'])){
                $cc_email = $_POST['email'];
                $mail['additional_headers'] = "Cc: $cc_email";
            }

            $currentformInstance->set_properties(array(
                "mail" => $mail
            ));

            return $currentformInstance;
        }
    }
}
//add_action('wpcf7_before_send_mail','wpcf7_send_a_copy');
?>