<div class="row mx-0 resource-library-list">
    <div class="col-12 col-md-2 thumb">
        <?php 
            // if ( has_post_thumbnail() ) {
            //     the_post_thumbnail("full");
            // }
            if(get_field('video_url')){
                ?><img src="<?php echo get_stylesheet_directory_uri().'/images/image-thumb-video.svg'; ?>"><?php
            }elseif(get_field('external_url')){
                ?><img src="<?php echo get_stylesheet_directory_uri().'/images/image-thumb-link.svg'; ?>"><?php
            }elseif(get_field('file')){
                ?><img src="<?php echo get_stylesheet_directory_uri().'/images/image-thumb-pdf.svg'; ?>"><?php
            }else{
                ?><img src="<?php echo get_stylesheet_directory_uri().'/images/image-thumb-pdf.svg'; ?>"><?php
            }
        ?>
        <div class="text-after-thumb"><?php the_field('text_after_thumbnail'); ?></div>
    </div>
    <div class="col-12 col-md-10 content">
        <div class="title">
            <?php //echo mb_strimwidth(get_the_title(), 0, 120, '...'); ?>
            <?php echo get_the_title(); ?>
        </div>
        <div class="text-after-title">
            <?php //echo nl2br(mb_strimwidth(get_field('text_after_title'), 0, 120, '...')); ?>
            <?php echo nl2br(get_field('text_after_title')); ?>
        </div>
        <div class="short-description">
            <?php 
                // echo truncate_html(get_the_excerpt(), '370', '...'); 
                echo wpautop(get_the_content());
            ?>
        </div>
        <div class="d-none">
            <?php
            /*
            $taxonomies = get_object_taxonomies('resource_library');
            $taxonomy_names = wp_get_object_terms(get_the_ID(), $taxonomies,  array("fields" => "names"));
            if($taxonomy_names){
                echo implode(', ', $taxonomy_names);
            }
            */
            ?>                             
        </div>
        <div class="action">
            <?php
            if(get_field('file')){
                $file = get_field('file');
                $file_url = wp_get_attachment_url($file);
                if($file_url){
                    ?>
                    <div class="view"><a href="<?php echo $file_url.'?'.time(); ?>" target="_blank" rel="noopener noreferrer"><?php _e('Preview', 'radar_child_fd'); ?></a></div>
                    <div class="download" rel="noopener noreferrer"><a href="<?php echo $file_url.'?'.time(); ?>" download><?php _e('Download', 'radar_child_fd'); ?></a></div>
                    <?php
                }
            }
            if(get_field('video_url')){
                ?>
                <div class="view"><a href="<?php echo get_field('video_url'); ?>" data-fancybox><?php _e('View', 'radar_child_fd'); ?></a></div>
                <?php
            }
            if(get_field('external_url')){
                ?>
                <!-- <div class="external-url"><a href="<?php echo get_field('external_url'); ?>" target="_blank" rel="noopener noreferrer"><?php _e('External Link', 'radar_child_fd'); ?></a></div> -->
                <div class="view"><a href="<?php echo get_field('external_url'); ?>" target="_blank" rel="noopener noreferrer"><?php _e('View', 'radar_child_fd'); ?></a></div>
                <?php
            }
            ?>
        </div>
    </div>
</div>