.simple-header-section-404{
	max-height: 420px;
	min-height: 420px;
	display: flex;
	align-items: center;
	position: relative;
	@media screen and (max-width: 600px) {
		min-height: 280px;
	}
	.bf-text {
		width: 36px;
		height: 168px;
		fill: #77c2a1;
	}
	.at-text{
		width: 36px;
		height: 168px;
		fill: #77c2a1;
	}
	.title-box{
		padding: 0px 42px 0px 42px;
		position: relative;
	}
	.titlewidget-section {
		display: flex;
		align-items: center;
		.texttitle{
			h1{
				font-weight: 700;
				margin-bottom: 20px;
			}
			h2{
				font-weight: 700;
				margin-bottom: 10px;
			}
		}
		.titledescription{
			width: 500px;
			max-width: 100%;
			font-size: 18px;
			font-weight: 400;
			line-height: 28px;
			overflow: hidden;
			margin-bottom: 0px;
		}
		a{
			font-weight: bold;
			text-decoration: none;
			&:hover{
				text-decoration: underline;
			}
		}
	}
	@media screen and (max-width: 1199px) and (min-width: 768px) {
		.bf-text {
			width: 56px;
		}
		.at-text{
			width: 56px;
		}
	}
	
	@media screen and (max-width: 991px) and (min-width: 768px) {
		.title-box{
			padding: 0px 30px 0px 30px !important;
			max-width: 600px;
		}
	}
	@media screen and (max-width: 767px) {
		.titlewidget-section .texttitle h1{
			/*font-size: 32px;*/
			margin-bottom: 0;
		}
	
		.titlewidget-section .texttitle h2{
			/*font-size: 28px;*/
			margin-bottom: 0;
		}
		.titlewidget-section .titledescription{
			height: auto;
			width: 352px;
			max-width: 100%;
		}
	}
	
	@media screen and (max-width: 581px) {
		.titlewidget-section .texttitle h1{
			/*font-size: 32px;*/
			margin-bottom: 0;
		}
		.titlewidget-section .texttitle h2{
			/*font-size: 28px;*/
			margin-bottom: 0;
		}
		.titlewidget-section .titledescription{
			font-size: 14px;
			padding-top: 12px;
			width: unset;
		}
		.titledescription br {
			display: none;
		}
		.texttitle br {
			display: none;
		}
	}
	@media screen and (min-width: 576px) {
		.bf-text-mobile{
			display: none;
		}
		.at-text-mobile{
			display: none;
		}
	}
	
	
	@media screen and (max-width: 575px) {
		.titlewidget-section{
			padding-left: 27px;
			padding-right: 27px;
			.bf-text-mobile{
				position: absolute;
				left: 20px;
			}
			.at-text-mobile{
				position: absolute;
				right: 20px;
			}
		}
		.titlewidget-section{
			margin: auto;
			align-items: flex-start;
		}
		.title-box{
			/*padding: @padding-mobile !important;*/
			padding: 0px 40px !important;
		}
		.texttitle{
			min-height: 120px;
			display: flex;
			align-items: center;
		}
		.bf-text-mobile{
			display: block;
			fill: #77c2a1;
			/*width: 52px;*/
			width: 27px;
			height: 120px;
		}
		.at-text-mobile{
			display: block;
			fill: #77c2a1;
			/*width: 52px;*/
			width: 27px;
			height: 120px;
		}
		.bf-text{
			display: none;
		}
		.at-text{
			display: none;
		}
	} 
	
	@media screen and (max-width: 767px) {
		.title-box{
			padding: 0px 40px;
		}
	}
}