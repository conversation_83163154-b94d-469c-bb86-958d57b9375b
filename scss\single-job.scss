@import "styles_variables.scss";

.single-jobpage{
    padding-top: 87px;

    .header-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 54px;
        &::before{
          content: '';
          display: inline-block;
          background: url('../images/subscribe-vector-left.svg') no-repeat;
          background-position: left;
          background-size: contain;
          width: 15px;
          height: 35px;
          left: -10px;
        }
        &::after{
          content: '';
          display: inline-block;
          background: url('../images/subscribe-vector-right.svg') no-repeat;
          background-position: right;
          background-size: contain;
          width: 15px;
          height: 35px;
          right: -10px;
        }
        h3{
          display: inline-block;
          font-size: 32px;
          font-weight: 700;
          line-height: 1.2;
          color: #77C2A1;
          margin: 0;
        }
      }

    .entry-title{
        /*
        font-size: 40px;
        line-height: 48px;
        */
        font-weight: 700;
        margin-bottom: 54px;
        /*padding-bottom: 54px;*/
        @media screen and (max-width: 991px){
            /*font-size: 18px;*/
        }
        @media screen and (max-width: 428px){
            word-break: break-word;
        }  
        span {
            font-weight: 400;
        }
    }
    .section-headline{
        color: $nanion-font-green;
        /*
        font-size: 18px;
        line-height: 28px;
        */
        font-weight: 700;
        @media screen and (max-width: 991px){
            /*font-size: 14px;*/
        }
        @media screen and (max-width: 767px){
            br{
                display: none;
            }
        }
    }
    .pd-left-col{
        padding-left: 35%;
        @media screen and (max-width: 991px){
            padding-left: 0;
        }
    }
    .toppage{
        h3{
            color: $nanion-font-green;
            font-size: 32px;
            line-height: 42px;
            font-weight: 700;
        }
        &.last{
            margin-top: 36px;
            margin-bottom: 100px;
            @media screen and (max-width: 768px){
                margin-bottom: 0px;
            }
        }
    }
    
    .entry-content{
        ul {
            list-style: none;
            line-height: 2;
    
            li {
                &::before {
                    content: "\2022";
                    color: #77C2A1;
                    font-weight: bold;
                    display: inline-block; 
                    width: 1em;
                    margin-left: -1em;
                }
            }
        }
    }

    .postcontent{
        &.job-response {
            padding-top: 30px;
            ul{
                padding-left: 15px;
            }
            /*
            ul {
                list-style: none;
                line-height: 2;
            }
              
            ul li::before {
                content: "\2022";
                color: #77C2A1;
                font-weight: bold;
                display: inline-block; 
                width: 1em;
                margin-left: -1em;
            }
            */
        }
    }
    
    .jobdownload{
        margin-top: 30px;
        margin-bottom: 170px;
        @media screen and (max-width: 767px){
            margin-bottom: 80px;
        }

        .download-content {
            display: flex;
            flex-direction: row;
            @media screen and (max-width: 810px){
                flex-direction: column;
            }

            .download-list{
                box-sizing: border-box;
                flex: 0 0 50%;
                margin-right: 5%;
                flex-grow: inherit;
                @media screen and (max-width: 575px){
                    margin-right: 3%;
                }
            }

            .download-item{
                position: relative;
                margin-bottom: -1px;
                
                a{
                    display: block;
                    padding: 19px 0px;
                    border-top: 1px solid #878787;
                    border-bottom: 1px solid #878787;
                    font-size: 14px;
                    font-weight: 400;
                    text-decoration: none !important;
                    color:#000000;

                    &::after{
                        content: "";
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        display: block;
                        width: 33px;
                        height: 32px;
                        background-image: url(../images/dw-icon.svg);
                        background-repeat: no-repeat;
                        transition: all 0.2s ease;
                        background-position: center;
                        transform: translateY(-50%);
                    }
                }

                &:hover{
                    
                    a {
                        color: #77C2A1;
                        font-weight: 700;
                        &::after{
                            background-image: url(../images/dw-icon-hover.svg);
                        }
                    }

                    .no-file {
                        color: #77C2A1;
                        font-weight: 700;
                        &::after{
                            background-image: url(../images/dw-icon-hover.svg);
                        }
                    }
                }

                .no-file{
                    display: block;
                    padding: 19px 0px;
                    border-top: 1px solid #878787;
                    border-bottom: 1px solid #878787;
                    font-size: 14px;
                    font-weight: 400;
                    text-decoration: none !important;
                    color:#000000;

                    &::after{
                        content: "";
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        display: block;
                        width: 33px;
                        height: 32px;
                        background-image: url(../images/dw-icon.svg);
                        background-repeat: no-repeat;
                        transition: all 0.2s ease;
                        background-position: center;
                        transform: translateY(-50%);
                    }
                }
            }
        }
    }

    .gobacktoblog{
        a.btn-back{
            justify-content: left;
            margin-left: -5px;
        }
    }
}

@media screen and (max-width: 770px) {
    .site-content{
        .container{
            &.single-jobpage{
                padding: 60px 20px 0px 20px;
            }
        }
    }
}


