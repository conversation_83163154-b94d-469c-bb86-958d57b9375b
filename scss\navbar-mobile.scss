@import "compass";

@media screen and (max-width: 992px) {
    .site-header{
        @include transition-property(all);
        @include transition-duration(0.2s);
        @include transition-timing-function(ease-in-out);
        &.active-navbar{
            background-color: #1D418E;
            z-index: 9999999999;
            .navbar-brand{
                img{
                    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
                }
            }
            .navbar-toggler-icon{
                background-image: url('../images/navbar-toggler-icon-close.svg') !important;
            }
        }
    }
    #mobile-menu.show{
        position: relative;
        &::before{
            content: '';
            width: 100%;
            height: 1px;
            position: absolute;
            box-shadow: 0px 2px 5px 2px rgba(0, 0, 0, 0.06);
        }
    }
    .navbar-mobile{
		padding-top: 43px;
		padding-bottom: 45px;
        background-color: #1D418E;
        ul.primary-menu{
            margin-bottom: 22px;
            li{
                margin-bottom: 10px;
                position: relative;
                a{
                    font-size: 14px;
                    line-height: 26px;
                    color: #ffffff;
                    display: inline-block;
                    width: 100%;
                    text-decoration: none;
                }
                &::after{
                    right: unset;
                    margin-left: 2px;
                }
                &.dropdown .dropdown-toggle{
                    position: relative;
                    padding-right: 30px !important;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    
                    &::after{
                        content: "\f078";
                        font-family: "Font Awesome 6 Free";
                        font-weight: 900;
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        transform: translateY(-50%);
                        transition: transform 0.3s ease;
                        color: #ffffff;
                        font-size: 12px;
                        border-top: unset;
                    }
                    
                    &.active::after{
                        transform: translateY(-50%) rotate(180deg);
                    }
                }
                &.dropdown .dropdown-menu{
                    background-color: #1D418E;
                    border: none;
                    border-radius: 0;
                    box-shadow: none;
                    display: none;
                    margin-top: 5px;
                    padding: 0px 10px;
                    position: static;
                    width: 100%;
                    &.show{
                        display: block;
                    }
                    .dropdown-item{
                        color: #ffffff;
                        font-size: 13px;
                        line-height: 24px;
                        padding: 8px 20px;
                        background: transparent;
                        border: none;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                        display: block;
                        width: 100%;
                        &:last-child{
                            border-bottom: none;
                        }
                        &:hover,
                        &:focus{
                            background-color: rgba(255, 255, 255, 0.1);
                            color: #ffffff;
                            text-decoration: none;
                        }
                    }
                }
            }
        }
		ul.top-menu{
            padding-top: 11px;
            padding-bottom: 11px;
            margin-bottom: 43px;
            border-top: 1px solid #ffffff;
            border-bottom: 1px solid #ffffff;
            display: block;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
			li{
				a{
					font-size: 14px;
    				line-height: 26px;
    				color: #ffffff;
					padding: 10px 0px;
					display: block;
				}
				&.wpml-ls-item{
					display: none;
				}
			}
		}
        .wpml-ls-legacy-dropdown-click{
            width: auto;
            a{
                font-size: 14px;
                color: #ffffff;
                padding-left: 0px;
                padding-right: 25px;
                background:none;
                border: 0px;
                display: inline-block;
            }
            .wpml-ls-sub-menu{
                max-width: 38px;
                border: 0px;
                position: relative;
                > li{
                    border-bottom: 1px solid #E4F3EC;
                    &:last-child{
                        border-bottom: 0px;
                    }
                    a{
                        line-height: 26px;
                        padding: 4px 0px;
                    }
                }
            }
        }
        .wpml-ls-legacy-dropdown-click a.wpml-ls-item-toggle:after{
            width: 10px;
            height: 5px;
            border: 0px;
            background: url('../images/language-switch-arrow-down.svg');
            filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
            background-size: cover;
            background-repeat: no-repeat;
        }
        .wpml-ls-legacy-dropdown-click .wpml-ls-current-language:hover>a, 
        .wpml-ls-legacy-dropdown-click a:focus, 
        .wpml-ls-legacy-dropdown-click a:hover{
            background: none;
            color: #ffffff;
            text-decoration: none;
        }
        .dropdown{
            .dropdown-toggle{
                position: relative;
                padding-right: 30px !important;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                
                &::after{
                    content: "\f078";
                    font-family: "Font Awesome 6 Free";
                    font-weight: 900;
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    transition: transform 0.3s ease;
                    color: #ffffff;
                    font-size: 12px;
                }
                
                &.active::after{
                    transform: translateY(-50%) rotate(180deg);
                }
            }
            
            .dropdown-menu{
                background-color: #1D418E;
                border: none;
                border-radius: 0;
                box-shadow: none;
                display: none;
                margin-top: 5px;
                padding: 20px 10px;
                position: static;
                width: 100%;
                
                &.show{
                    display: block;
                }
                
                .dropdown-item{
                    color: #ffffff;
                    font-size: 13px;
                    line-height: 24px;
                    padding: 8px 20px;
                    background: transparent;
                    border: none;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    display: block;
                    width: 100%;
                    
                    &:last-child{
                        border-bottom: none;
                    }
                    
                    &:hover,
                    &:focus{
                        background-color: rgba(255, 255, 255, 0.1);
                        color: #ffffff;
                        text-decoration: none;
                    }
                }
                .dropdown{
                    .dropdown-toggle{
                        position: relative;
                        padding-right: 30px !important;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        padding-left: 30px;
                        &::after{
                            content: "\f078";
                            font-family: "Font Awesome 6 Free";
                            font-weight: 900;
                            position: absolute;
                            right: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                            transition: transform 0.3s ease;
                            color: #ffffff;
                            font-size: 10px; 
                        }
                    
                        &.active::after{
                            transform: translateY(-50%) rotate(180deg);
                        }
                    }
                    
                    .dropdown-menu{
                        background-color: #1D418E;
                        margin-left: 10px; 
                        
                        .dropdown-item{
                            font-size: 12px;
                            padding: 20px 30px; 
                        }
                    }
                }
            }
        }
        
        li.menu-item-has-children{
            > a{
                position: relative;
                padding: 10px 0px !important;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                
                &::after{
                    content: "\f078";
                    font-family: "Font Awesome 6 Free";
                    font-weight: 900;
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    transition: transform 0.3s ease;
                    color: #ffffff;
                    font-size: 12px;
                }
                
                &.active::after{
                    transform: translateY(-50%) rotate(180deg);
                }
            }
            
            .sub-menu{
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 0;
                box-shadow: none;
                display: none;
                margin-top: 5px;
                padding: 0;
                position: static;
                width: 100%;
                
                &.show{
                    display: block;
                }
                
                li{
                    a{
                        color: #ffffff;
                        font-size: 13px;
                        line-height: 24px;
                        padding: 8px 20px;
                        background: transparent;
                        border: none;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                        display: block;
                        width: 100%;
                        
                        &:hover,
                        &:focus{
                            background-color: rgba(255, 255, 255, 0.1);
                            color: #ffffff;
                            text-decoration: none;
                        }
                    }
                    
                    &:last-child a{
                        border-bottom: none;
                    }
                    
                    &.menu-item-has-children{
                        > a{
                            position: relative;
                            padding: 10px 0px !important;
                            
                            &::after{
                                content: "\f078";
                                font-family: "Font Awesome 6 Free";
                                font-weight: 900;
                                position: absolute;
                                right: 10px;
                                top: 50%;
                                transform: translateY(-50%);
                                transition: transform 0.3s ease;
                                color: #ffffff;
                                font-size: 10px; 
                            }
                            
                            &.active::after{
                                transform: translateY(-50%) rotate(180deg);
                            }
                        }
                        
                        .sub-menu{
                            background-color: rgba(255, 255, 255, 0.05);
                            margin-left: 10px;
                            
                            li a{
                                font-size: 12px;
                                padding: 6px 40px; 
                            }
                        }
                    }
                }
            }
        }
		.header-search{
            line-height: 100%;
            position: relative;
            .header-search-form-mobile-icon{
                font-size: 12px;
                line-height: 100%;
                display: inline-block;
                vertical-align: top;
                margin-left: 15px;
                color: #ffffff;
                img{
                    width: 32px;
                    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
                }
            }
            .header-search-form{
                width: auto;
                max-width: 100%;
                opacity: 0;
                visibility: hidden;
                background: unset;
                border: 0px;
                position: relative;
                bottom: 0;
                @include transition-property(all);
                @include transition-duration(0.2s);
                @include transition-timing-function(ease-in-out);
                form{
                    padding: 0px;
                }
                .form-group{
                    width: 100%;
                    margin-bottom: 0px;
                }
                input[type="submit"]{
                    display: none;
                }
                input[type="search"]{
                    width: 100%;
                    border: 0px;
                    background-color: #ffffff;
                    border: 1px solid #77c2a1;
                    @include border-radius(0px, 0px);
                }
            }
            &.active{
                .header-search-form{
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
	}
}