document.addEventListener( 'wpcf7mailsent', function( event ) {
    const tray_evaluation_form_isInArray = theme_contact_form_data.tray_evaluation_form_id.includes(event.detail.contactFormId);
    if ( tray_evaluation_form_isInArray ){
        jQuery('.tray-evaluation-form .use-floating-validation-tip').hide();
        jQuery('.tray-evaluation-form .wpcf7-response-output').hide();
        jQuery('.tray-evaluation-form').append(theme_contact_form_data.tray_evaluation_form_success_message);
        jQuery('html, body').animate({
            scrollTop: jQuery(".tray-evaluation-form .success-message").offset().top - 100
        }, 1000);
    }

    const banner_form_isInArray = theme_contact_form_data.banner_form_id.includes(event.detail.contactFormId);
    if ( banner_form_isInArray ){
        jQuery('.banner-form .use-floating-validation-tip').hide();
        jQuery('.banner-form .wpcf7-response-output').hide();
        jQuery('.banner-form').append(theme_contact_form_data.banner_form_message);
        jQuery('html, body').animate({
            scrollTop: jQuery(".banner-form .success-message").offset().top - 100
        }, 1000);
    }

    jQuery('.success-message .btn-secondary.close-popup').on('click',function(){
        jQuery(this).closest('.collapse').find('.close-popup').click();
        setTimeout(function(){
            jQuery('.wpcf7-form .wpcf7-text,.wpcf7-form .wpcf7-textarea,.wpcf7-select').removeClass('wpcf7-valid');
            jQuery('.use-floating-validation-tip ').show();
            jQuery('.success-message').hide();
        },300);
    });

    jQuery('.popup-top-wrapper .close-popup').on('click',function(){
        setTimeout(function(){
            jQuery('.wpcf7-form .wpcf7-text,.wpcf7-form .wpcf7-textarea,.wpcf7-select').removeClass('wpcf7-valid');
            jQuery('.use-floating-validation-tip ').show();
            jQuery('.success-message').hide();
        },300);
    });
}, false );

document.addEventListener( 'wpcf7invalid', function( event ) {
    const tray_evaluation_form_isInArray = theme_contact_form_data.tray_evaluation_form_id.includes(event.detail.contactFormId);
    if ( tray_evaluation_form_isInArray ){
        jQuery('html, body').animate({
            scrollTop: jQuery(".tray-evaluation-form").offset().top - 120
        }, 1000);
    }

    const banner_form_isInArray = theme_contact_form_data.banner_form_id.includes(event.detail.contactFormId);
    if ( banner_form_isInArray ){
        jQuery('html, body').animate({
            scrollTop: jQuery(".banner-form").offset().top - 120
        }, 1000);
    }
}, false );



// Contact Form 7 all event control
jQuery(document).ready(function($){
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
    var urlReg = /^(http|https|ftp):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;

    // Contact Form 7
    // Input text event
    jQuery('.wpcf7-form .wpcf7-text,.wpcf7-form .wpcf7-textarea').on('keyup',function(){
        if(jQuery(this).hasClass('wpcf7-not-valid')){
            jQuery(this).removeClass('wpcf7-not-valid');
            jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').remove();
            jQuery(this).closest('.validate').find('strong').removeClass('wpcf7-not-valid');
        }
        if(jQuery(this).attr('type') === 'email' && jQuery(this).val() !== '' && !emailReg.test(jQuery(this).val())) {
            jQuery(this).removeClass('wpcf7-valid').addClass('wpcf7-not-valid');
            if (jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').length === 0) {
                jQuery(this).closest('.validate').append('<span class="wpcf7-not-valid-tip">Incorrect</span>');
            }
        }else if(jQuery(this).attr('type') === 'url' && jQuery(this).val() !== '' && !urlReg.test(jQuery(this).val())) {
            jQuery(this).removeClass('wpcf7-valid').addClass('wpcf7-not-valid');
            if (jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').length === 0) {
                jQuery(this).closest('.validate').append('<span class="wpcf7-not-valid-tip">Incorrect</span>');
            }
        }else if(jQuery(this).val() === ''){
            jQuery(this).removeClass('wpcf7-not-valid').removeClass('wpcf7-valid');
            jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').remove();

        }else{
            jQuery(this).removeClass('wpcf7-not-valid').addClass('wpcf7-valid');
            jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').remove();
        }
    });

    // Select box event
    jQuery('.wpcf7-form .validate select').on('change',function(){
        if(jQuery(this).val() === ''){
            jQuery(this).closest('.validate').find('.select2-selection').removeClass('wpcf7-valid');
        }else{
            jQuery(this).closest('.validate').find('.select2-selection').addClass('wpcf7-valid');
        }
    });

    // Submit event
    jQuery('.wpcf7-form input[type="submit"]').on('click',function(){
        let checkValidate = 0;
        jQuery(this).closest('.wpcf7-form').find('.wpcf7-validates-as-required').each(function(){
            if(jQuery(this).val() === '' || jQuery(this).hasClass('wpcf7-not-valid')){
                $(this).addClass('wpcf7-not-valid');
                jQuery(this).closest('.validate').find('strong').addClass('wpcf7-not-valid');
                if (jQuery(this).closest('.validate').find('.wpcf7-not-valid-tip').length === 0) {
                    jQuery(this).closest('.validate').append('<span class="wpcf7-not-valid-tip">Please fill out this field.</span>');
                }
                checkValidate++;
            }
        });

        if(checkValidate === 0){
            return true;
        }else{
            return false;
        }
    })
})