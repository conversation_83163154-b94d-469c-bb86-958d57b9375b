.single-partners, .single-synchropatch{
  .gobacktoblog{
    margin: 90px 0 40px 0;
    a.btn-back{
      margin-left: -5px;
      @media screen and (max-width:767px) {
        justify-content: left;
      }
    }
  }
  .header-wrapper {
    // display: flex;
    display: table;
    align-items: center;
    position: relative;
    &::before{
      content: '';
      // display: inline-block;
      display: table-cell;
      background: url('../images/subscribe-vector-left.svg') no-repeat;
      background-position: left;
      background-size: contain;
      width: 15px;
      // height: 35px;
      height: 100%;
      left: -10px;
    }
    &::after{
      content: '';
      // display: inline-block;
      display: table-cell;
      background: url('../images/subscribe-vector-right.svg') no-repeat;
      background-position: right;
      background-size: contain;
      width: 15px;
      // height: 35px;
      height: 100%;
      right: -10px;
    }
    h1,h3{
      display: inline-block;
      font-size: 32px;
      font-weight: 700;
      line-height: 42px;
      color: #77C2A1;
      margin: 0;
      padding-left: 5px;
      padding-right: 5px;
      @media screen and (max-width:767px) {
        padding: 0 10px;
      }
    }
  }
  .entry-content{
    margin-top: 105px;
    padding-left: 100px;
    @media screen and (max-width:991px) {
      padding-left: 0px;
    }
    @media screen and (max-width:768px) {
      margin-top: 50px;
    }

    h4{
      font-weight: 700;
      line-height: 1.2;
      color: #77C2A1;
    }
  }
}