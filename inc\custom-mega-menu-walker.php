<?php
/**
 * Custom Mega Menu Walker
 * 
 * This file contains a custom walker class that extends the Mega_Menu_Walker
 * to wrap menu item titles with a span element for better CSS targeting.
 * 
 * @package RadarSoft_Bootstrap_Child
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Custom Mega Menu Walker to wrap menu titles with span
class Custom_Mega_Menu_Walker extends Mega_Menu_Walker {

	function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {

		$this->currentItem = $item;

		// Convert args to object if it's an array (WordPress standard)
		if ( is_array( $args ) ) {
			$args = (object) $args;
		}

		if ( property_exists( $item, 'megamenu_settings' ) ) {
			$settings = $item->megamenu_settings;
		} else {
			$settings = Mega_Menu_Nav_Menus::get_menu_item_defaults();
		}

		// Item Class
		$classes = empty( $item->classes ) ? array() : (array) $item->classes;
		$styles = empty( $item->styles ) ? array() : (array) $item->styles;

		if ( is_array( $classes ) && ! in_array( 'menu-column', $classes ) && ! in_array( 'menu-row', $classes ) ) {
			$classes[] = 'menu-item-' . $item->ID;
		}

		// remove style attribute from rows
		if ( is_array( $classes ) && in_array( 'menu-row', $classes ) ) {
			$styles = array();
		}

		$class = join( ' ', apply_filters( 'megamenu_nav_menu_css_class', array_filter( $classes ), $item, $args ) );
		$style = join( '; ', apply_filters( 'megamenu_nav_menu_css_style', array_filter( $styles ), $item, $args ) );

		// these classes are prepended with 'mega-'
		$mega_classes = explode( ' ', $class );

		// strip widget classes back to how they're intended to be output
		$class = str_replace( 'mega-menu-widget-class-', '', $class );

		// Item ID
		if ( is_array( $classes ) && ! in_array( 'menu-column', $classes ) && ! in_array( 'menu-row', $classes ) ) {
			$id = "mega-menu-item-{$item->ID}";
		} else {
			$id = "mega-menu-{$item->ID}";
		}

		$id = esc_attr( apply_filters( 'megamenu_nav_menu_item_id', $id, $item, $args ) );

		$list_item_attributes = array(
			'class' => $class,
			'style' => $style,
			'id' => $id
		);

		$attributes = '';

		foreach ( $list_item_attributes as $attr => $value ) {
			if ( strlen( $value ) ) {
				$attributes .= ' ' . $attr . '="' . esc_attr($value) . '"';
			}
		}

		$output .= '<li' . $attributes . '>';

		// output the widgets
		if ( $item->type == 'widget' ) {

			if ( $item->content ) {
				$item_output = $item->content;
			} else {
				$item_output = "<!-- widget is empty -->";
			}

		//} else if ( 'block' === $item->type ) {
		//  /** This filter is documented in wp-includes/post-template.php */
		//  $item_output = apply_filters( 'the_content', $item->content );
		} else {

			$atts = array();

			$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
			$atts['target'] = ! empty( $item->target ) ? $item->target : '';
			$atts['class']  = '';
			$atts['rel']    = ! empty( $item->xfn ) ? $item->xfn : '';

			if ( isset( $settings['disable_link'] ) && $settings['disable_link'] != 'true' ) {
				$atts['href'] = ! empty( $item->url ) ? $item->url : '';
			} else {
				$atts['tabindex'] = 0;
			}

			if ( isset( $settings['icon'] ) && $settings['icon'] != 'disabled' && $settings['icon'] != 'custom' ) {
				$atts['class'] = $settings['icon'];
			}

			if ( isset( $settings['icon'] ) && $settings['icon'] == 'custom' ) {
				$atts['class'] = 'mega-custom-icon';
			}

			if ( is_array( $classes ) && in_array( 'menu-item-has-children', $classes ) && $item->parent_submenu_type == 'flyout' ) {

				$atts['aria-expanded'] = 'false';

				if ( is_array( $mega_classes ) && in_array( 'mega-toggle-on', $mega_classes ) ) {
					$atts['aria-expanded'] = 'true';
				}

				if ( isset( $settings['disable_link'] ) && $settings['disable_link'] == 'true' ) {
					$atts['role'] = 'button';
					//$atts['aria-controls'] = 'mega-sub-menu-' . $item->ID;
				}
			}

			if ( is_array( $classes ) && in_array( 'current-menu-item', $classes ) ) {
				$atts['aria-current'] = 'page';
			}

			if ( $depth == 0 ) {
				$atts['tabindex'] = '0';
			}

			if ( isset( $settings['hide_text'] ) && $settings['hide_text'] == 'true' ) {
				$atts['aria-label'] = $item->title;
			}

			$atts = apply_filters( 'megamenu_nav_menu_link_attributes', $atts, $item, $args );

			if ( isset( $atts['class'] ) && strlen( $atts['class'] ) ) {
				$atts['class'] = $atts['class'] . ' mega-menu-link';
			} else {
				$atts['class'] = 'mega-menu-link';
			}

			$attributes = '';

			foreach ( $atts as $attr => $value ) {
				if ( strlen( $value ) ) {
					$value       = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
					$attributes .= ' ' . $attr . '="' . $value . '"';
				}
			}

			$item_output  = $args->before;
			$item_output .= '<a' . $attributes . '>';

			if ( is_array( $classes ) && in_array( 'icon-top', $classes ) ) {
				$item_output .= "<span class='mega-title-below'>";
			}

			if ( isset( $settings['hide_text'] ) && $settings['hide_text'] == 'true' ) {
							/**
			 * This filter is documented in wp-includes/post-template.php
*/
			} elseif ( property_exists( $item, 'mega_description' ) && strlen( $item->mega_description ) ) {
				$item_output .= '<span class="mega-description-group"><span class="mega-menu-title">' . $args->link_before . apply_filters( 'megamenu_the_title', $item->title, $item->ID ) . $args->link_after . '</span><span class="mega-menu-description">' . $item->description . '</span></span>';
			} else {
				// CUSTOM MODIFICATION: Wrap the title with span.link-title
				$item_output .= $args->link_before . '<span class="link-title">' . apply_filters( 'megamenu_the_title', $item->title, $item->ID ) .'</span>'. $args->link_after;
			}

			if ( is_array( $classes ) && in_array( 'icon-top', $classes ) ) {
				$item_output .= '</span>';
			}

			if ( is_array( $classes ) && in_array( 'menu-item-has-children', $classes ) ) {

				$item_output .= '<span class="mega-indicator"';

				$indicator_atts = array();
				$indicator_atts['aria-hidden'] = 'true';
				$indicator_atts = apply_filters( 'megamenu_indicator_atts', $indicator_atts, $item, $args, $mega_classes );

				foreach ( $indicator_atts as $attr => $value ) {
					if ( strlen( $value ) ) {
						$item_output .= ' ' . $attr . '="' . esc_attr( $value ) . '"';
					}
				}

				$item_output .= "></span>";
			}

			$item_output .= '</a>';

			$item_output .= $args->after;

			if ( is_array( $classes ) && ( in_array( 'menu-column', $classes ) || in_array( 'menu-row', $classes ) ) ) {
				$item_output = '';
			}
		}

		$output .= apply_filters( 'megamenu_walker_nav_menu_start_el', $item_output, $item, $depth, $args );
	}
}

// Hook to replace the default Mega Menu Walker with our custom one
function use_custom_mega_menu_walker( $args, $menu_id, $current_theme_location ) {
	if ( class_exists( 'Custom_Mega_Menu_Walker' ) ) {
		$args['walker'] = new Custom_Mega_Menu_Walker();
	}
	return $args;
}
add_filter( 'megamenu_nav_menu_args', 'use_custom_mega_menu_walker', 10, 3 );
