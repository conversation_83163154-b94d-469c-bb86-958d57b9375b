<?php
get_header();

wp_enqueue_script('product-page');
wp_enqueue_script('resource-library-search');
?>
	<?php while ( have_posts() ) : the_post(); ?>

	<?php
	$current_ID = $post->ID;
	$parent = $post->post_parent;
	
	if(get_field('product_content_head_bg')){
		$style = 'background-color:'.get_field('product_content_head_bg').';';
	}else{
		$style = '';
	}
	?>
	<div class="product-content">
			<?php
			if($parent!=0){
				$subpage_title_color = get_field('subpage_title_color', $parent);
				if($subpage_title_color){
					?>
					<style>
						.single-products .product-content .product-subtitle{
							color: <?php echo $subpage_title_color; ?>;
						}
						.single-products .product-content .product-subtitle span::before{
							background-color: <?php echo $subpage_title_color; ?>;
						}
						.single-products .product-content .product-subtitle span::after{
							background-color: <?php echo $subpage_title_color; ?>;
						}
					</style>
					<?php
				}
				?>
				<div class="product-content-head" style="<?php echo $style; ?>">
                    <div class="container">
                        <div class="breadcrumbs mt-3 mb-3" typeof="BreadcrumbList" vocab="http://schema.org/">
                            <?php if(function_exists('bcn_display')){
                                bcn_display();
                            }?>
                        </div>
                    </div>
					<div class="add_space"></div>
					<div class="container">
						<a class="btn btn-link back-overview" href="<?php the_permalink($parent); ?>"><?php _e('Back to overview', 'radar_child_fd') ?></a>

						<h3 class="product-subtitle"><span><?php echo get_the_title($parent); ?></span></h3>
					</div>
				</div>
				<?php
			}
			?>
		<div class="container">
            <?php if($parent==0): ?>
                <div class="breadcrumbs mt-3 mb-3" typeof="BreadcrumbList" vocab="http://schema.org/">
                    <?php if(function_exists('bcn_display')){
                        bcn_display();
                    }?>
                </div>
            <?php endif; ?>
			<?php the_content(); ?>
		</div>
	</div>

	<?php endwhile; ?>
<?php get_footer(); ?>
