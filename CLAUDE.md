# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WordPress child theme called "Radarsofthouse Bootstrap Child Theme" that extends a parent theme `radarsoft_bootstrap`. It's designed for a scientific/biotech company dealing with products like patch clamp systems, membrane biophysics, and cell monitoring equipment.

## Build System & Development Workflow

### SCSS Compilation
The theme uses SCSS with Compass for CSS compilation:
- **Source**: `scss/` directory contains all SCSS files
- **Output**: Compiled CSS files are placed in `css/` directory
- **Configuration**: `config.rb` contains Compass configuration
- **Main stylesheet**: `scss/styles.scss` is the primary stylesheet
- **Build command**: Use Compass to compile SCSS (compass watch or compass compile)

The theme loads compiled CSS via `functions.php:19` with `wp_enqueue_style('parent-style', get_stylesheet_directory_uri() .'/css/styles.css')`

## Architecture & Key Components

### WordPress Theme Structure
- **Main templates**: Standard WordPress template files (single-*.php, page.php, etc.)
- **Custom post types**: Handles products, news, job-positions, partners, synchropatch
- **Template parts**: `contents/` directory contains reusable content templates
- **Loops**: `loops/` directory contains custom loop templates

### JavaScript Architecture
JavaScript is organized in `js/` directory with specific modules:
- **Carousel/Sliders**: Uses both Owl Carousel and Slick Slider
  - Owl Carousel for general post lists (`owl-post-list.js`)
  - Slick Slider for two-cards-row component (`two-card-slider.js`)
- **Search/Filter**: Custom AJAX-powered filtering (`search-filter.js`, `resource-library-search.js`)
- **Mobile Navigation**: Custom mobile menu handling (`navbar-mobile.js`)
- **Product Pages**: Product-specific functionality (`product-page.js`)

### Key Features & Functionality

#### Custom Page Builder Integration
- Uses SiteOrigin Panels page builder
- Custom styling hooks in `inc/page-builder-hooks-styles.php`
- Adds custom background area options for rows/cells

#### Search & Filter System
- Custom search functionality for resource library
- AJAX-powered filtering with load more functionality
- Search filter templates in `search-filter/` directory
- Multiple search forms (IDs: 362, 461, 472, 644, 695, 728)

#### Product System
- Hierarchical product structure (parent/child pages)
- Custom styling per product category
- Dynamic breadcrumb navigation
- Product-specific contact widgets

### Asset Management
All assets are enqueued through `functions.php` with proper WordPress practices:
- **Fonts**: Century Gothic (local) and Font Awesome (CDN)
- **Libraries**: jQuery, Owl Carousel, Slick Slider, jQuery Expander
- **Custom Scripts**: All custom JS files are properly registered and localized

### Custom Functionality
- **Custom Mega Menu Walker**: Enhanced navigation in `inc/custom-mega-menu-walker.php`
- **HTML Truncation**: Custom function `truncate_html()` for content excerpts
- **PDF File Management**: Custom handling for PDF file replacements
- **Contact Form 7 Integration**: Custom styling and functionality
- **YouTube Integration**: Custom YouTube video ID extraction

## File Structure Patterns
- **Single templates**: `single-{post-type}.php` for custom post types
- **Content templates**: `contents/content-{type}.php` for reusable content
- **Loop templates**: `loops/loop-{type}.php` for custom loops
- **Include files**: `inc/{feature}.php` for modular functionality

## Styling System
- **SCSS Variables**: `scss/styles_variables.scss` for global variables
- **Component-based**: Separate SCSS files for different components/pages
- **Responsive**: Mobile-first approach with comprehensive responsive styles
- **Custom Properties**: Uses CSS custom properties for dynamic styling

## WordPress Integrations
- **Custom Post Types**: Products, news, job positions, partners, synchropatch
- **Custom Fields**: Extensive use of Advanced Custom Fields (ACF) for content management
- **Navigation**: Multiple custom menu locations (top-menu, footer-menu-2, mobile menus)
- **Widgets**: Custom sidebar areas (footer-subscribe-newsletter, product-contact-widget)

## Development Notes
- The theme disables the WordPress admin bar for all users
- Uses custom body classes for page-specific styling
- Implements custom navigation highlighting for different post types
- All JavaScript is properly localized for internationalization