jQuery(function($){
    $('.resource-filter .searchandfilter li[data-sf-field-input-type=checkbox], .resource-filter .searchandfilter li[data-sf-field-input-type=radio]').on('click', function(e){
        if ($(this).hasClass('active')){
            $(this).removeClass('active');
        }else {
            $('.resource-filter .searchandfilter li[data-sf-field-input-type=checkbox], .resource-filter .searchandfilter li[data-sf-field-input-type=radio]').removeClass('active');
            $(this).addClass('active');
        }
    });

    size_li = jQuery(".resource-library-list").size();
    x=5;
    hiddenItem(size_li, x);

    $('#resource-search-button').on('click', function(e){
        e.preventDefault();
        var text = $('#resource-search-text').val();
        var count = 0;
        $('.resource-library-list').removeClass('is-visible is-open');
        var filteredDivs = $('.resource-library-list').filter(function () {
            var reg = new RegExp(text, "ig");
            if(reg.test($(this).text()) === true){
                //console.log($(this).index());
                count++;
                $(this).addClass('is-visible is-open');
                /*
                setTimeout(function () {
                    $(this).addClass('is-open');
                }, 20);
                */
                jQuery('.resource-library-result .button-area').html('');
            }
        });
        $('.resource-library-result-count .count').text(count);
        if(count == 0){
            jQuery('.resource-library-result .button-area').html('');
        }
    });

    jQuery('.search-filter-results').on('click', '.button-area .read-more', function(e){
        e.preventDefault();
        x= (x+5 <= size_li) ? x+5 : size_li;
        //console.log(x);
        jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
        }, 200);
        if(x == size_li){
            jQuery(this).css('display', 'none');
        }
    });

    function hiddenItem(size_li, x){
        if(size_li > 5){
            jQuery('.resource-library-result .button-area').html('<a class="btn-primary read-more" href="#">'+ search_filter_val.loadmore_text +'</a>');
    
            jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
            }, 200);
        }else{
            jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
            }, 200);
        }
    }

});

(function ( $ ) {
    var target_categories_val_old = null;
    var target_families_val_old = null;
    var target_val_old = null;
    var spinner = '<div class="spinner-warp"><div class="spinner">'+
        '<div class="spinner-inner">'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
            '<span></span>'+
        '</div>'+
    '</div></div>';

    $(document).on("sf:init", ".searchandfilter", function(e, data){
        // jQuery('.resource-filter .searchandfilter > ul > li > ul').addClass('scrollbar-inner');
        // jQuery('.scrollbar-inner').scrollbar();
        if(data.sfid == 2368){
            // jQuery(this).append(spinner);
            // jQuery(this).css({
            //     'pointer-events':'none',
            // });
        }
    });
    $(document).on("sf:ajaxstart", ".searchandfilter", function(e, data){
        if(data.sfid == 2368){
            jQuery(this).append(spinner);
            jQuery(this).css({
                'pointer-events':'none',
            });
        }
    });
    $(document).on("sf:ajaxfinish", ".searchandfilter", function(e, data){
        // jQuery('.resource-filter .searchandfilter > ul > li > ul').addClass('scrollbar-inner');
        // jQuery('.scrollbar-inner').scrollbar();
        if(data.object.ajax_action == 'submit' && data.object.current_paged == 1){
            $('#resource-search-text').val('');
            size_li = jQuery(".resource-library-list").size();
            x=5;
            hiddenItem_sf(size_li,x);
        }

        if(data.sfid == 2368){
            
            var target_categories_select = [];
            var target_families_select = [];
            var target_select = [];

            var target_categories = jQuery('.sf-field-taxonomy-target_categories .sf-option-active').not('.sf-item-0');
            var target_families = jQuery('.sf-field-taxonomy-target_families .sf-option-active').not('.sf-item-0');
            var target = jQuery('.sf-field-taxonomy-targets .sf-option-active').not('.sf-item-0');

            var target_categories_val = jQuery('.sf-field-taxonomy-target_categories .sf-option-active').find('.sf-input-radio').val();
            var target_families_val = jQuery('.sf-field-taxonomy-target_families .sf-option-active').find('.sf-input-radio').val();
            var target_val = jQuery('.sf-field-taxonomy-targets .sf-option-active').find('.sf-input-radio').val();

            if(target_categories_val != target_categories_val_old && target_categories_val != ''){
                if(target_categories_val != ''){
                    target_categories_select = search_filter_val.target_categories_tax[target_categories_val];
                    target_categories_val_old = target_categories_val;
                }
                
                if(target_categories_select.length != 0){
                    //jQuery('.sf-field-taxonomy-target_families li').removeClass('sf-option-active');
                    jQuery('.sf-field-taxonomy-target_families li').hide();
                    var target_families_input = jQuery('.sf-field-taxonomy-target_families li input');
                    target_families_input.each(function(){
                        if(jQuery.inArray(jQuery(this).val(), target_categories_select) != -1) {
                            jQuery(this).parent().show();
                            target_families_select.push(jQuery(this).val());
                        }
                    });
                    //target_families_val = null;
                }

                if(jQuery.inArray(target_families_val, target_categories_select) != -1 && target_families_val != '') {
                    if(target_families.length != 0){
                        target_families_select = search_filter_val.target_families_tax[target_families_val];
                        target_families_val_old = target_families_val;
                    }
                    if(target_families_select.length != 0){
                        //jQuery('.sf-field-taxonomy-targets li').removeClass('sf-option-active');
                        jQuery('.sf-field-taxonomy-targets li').hide();
                        var target_input = jQuery('.sf-field-taxonomy-targets li input');
                        target_input.each(function(){
                            if(jQuery.inArray(jQuery(this).val(), target_families_select) != -1) {
                                jQuery(this).parent().show();
                            }
                        });
                    }
                    target_families_val = null;
                    target_val = null;
                }else{
                    if(target_families_select.length != 0){
                        jQuery.each(target_families_select, function( index, select_families ) {
                            jQuery.each(search_filter_val.target_families_tax[select_families], function( index, select_target ) {
                                target_select.push(select_target);
                            });
                        });
                        //jQuery('.sf-field-taxonomy-targets li').removeClass('sf-option-active');
                        jQuery('.sf-field-taxonomy-targets li').hide();
                        var target_input = jQuery('.sf-field-taxonomy-targets li input');
                        target_input.each(function(){
                            if(jQuery.inArray(jQuery(this).val(), target_select) != -1) {
                                jQuery(this).parent().show();
                            }
                        });
                        target_families_val = null;
                        target_val = null;
                    }
                }
            }

            if(target_families_val != target_families_val_old && target_families_val != null){
                if(target_families.length != 0){
                    target_families_select = search_filter_val.target_families_tax[target_families_val];
                    target_families_val_old = target_families_val;
                }
                if(target_families_select.length != 0){
                    //jQuery('.sf-field-taxonomy-targets li').removeClass('sf-option-active');
                    jQuery('.sf-field-taxonomy-targets li').hide();
                    var target_input = jQuery('.sf-field-taxonomy-targets li input');
                    target_input.each(function(){
                        if(jQuery.inArray(jQuery(this).val(), target_families_select) != -1) {
                            jQuery(this).parent().show();
                        }
                    });
                    target_val = null;
                }
            }

            if(target_val != target_val_old && target_val != null){
                target_val_old = target_val;
                Object.keys(search_filter_val.target_families_tax).forEach(function(value){
                    if(jQuery.inArray(target_val, search_filter_val.target_families_tax[value]) != -1) {
                        target_families_select.push(value);
                    }
                });
                if(target_families_select.length != 0){
                    var target_categories_select = [];
                    jQuery.each(target_families_select, function( index, select_value ) {
                        Object.keys(search_filter_val.target_categories_tax).forEach(function(value){
                            if(jQuery.inArray(select_value, search_filter_val.target_categories_tax[value]) != -1) {
                                target_categories_select.push(value);
                            }
                        });
                    });
    
                    var new_target_categories_select = target_categories_select.filter(function(element,index,self){
                        return index === self.indexOf(element); 
                    });
                    
                    var target_family_filter = search_filter_val.target_categories_tax[new_target_categories_select[0]];
                    if(target_family_filter.length != 0){
                        jQuery('.sf-field-taxonomy-target_families li').hide();
                        var target_families_input = jQuery('.sf-field-taxonomy-target_families li input');
                        target_families_input.each(function(){
                            if(jQuery.inArray(jQuery(this).val(), target_family_filter) != -1) {
                                jQuery(this).parent().show();
                            }
                        });
                    }
                }
            }

            if(target_categories_val == '' && target_families_val == '' && target_val == ''){
                jQuery('.sf-field-taxonomy-target_families li').show();
                jQuery('.sf-field-taxonomy-targets li').show();
            }

            jQuery(this).find('.spinner-warp').remove();
            jQuery(this).css({
                'pointer-events':'auto',
            });
        }
    });
}(jQuery));

function hiddenItem_sf(size_li,x){
    if(size_li > 5){
        jQuery('.resource-library-result .button-area').html('<a class="btn-primary read-more" href="#">'+ search_filter_val.loadmore_text +'</a>');

        jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
        }, 200);

        jQuery('.resource-library-result .button-area').on('click', '.read-more', function(e){
            x= (x+5 <= size_li) ? x+5 : size_li;
            //console.log(x);
            jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
            }, 200);
            if(x == size_li){
                jQuery(this).css('display', 'none');;
            }
        });
    }else{
        jQuery('.resource-library-list:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.resource-library-list:lt('+x+')').addClass('is-open');
        }, 200);
    }
}