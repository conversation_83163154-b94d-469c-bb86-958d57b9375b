@import "styles_variables.scss";
.single-news{
    .top-menu{
        .news-menu{
            a{
                transition-timing-function: ease-in-out;
                color: #77C2A1;
                // -webkit-text-fill-color: #77C2A1;
                // -webkit-text-stroke-width: 1px;
                // -webkit-text-stroke-color: #77C2A1;
            }
        }
    }
}
.news-singlepage{
    padding-top: 87px;
    margin-bottom: 210px;
    @media screen and (max-width:767px) {
        margin-bottom: 60px;
    }
    .entry-title{
        font-weight: bold;
        margin-bottom: 63px;
    }
    .section-headline{
        color: $nanion-font-green;
        font-weight: bold;
    }
    .pd-left-col{
        padding-left: 35%;
        @media screen and (max-width:991px) {
            padding-left: 0%;
        }
    }
    .caption{
        color: $nanion-font-green;
        font-size: 14px;
        line-height: 26px;
        font-weight: 700;
    }
    .toppage{
        .backbutton{
            a{
            font-size: 14px;
            color: $nanion-font-gray;
            text-decoration: none;
            margin-left: -5px;
            justify-content: left;
            }
            @media screen and (max-width: 600px) {
                margin-top: 15px;
            }
        }
        h3{
            color: $nanion-font-green;
            font-weight: bold;
        }
        &.last{
            margin-top: 36px;
            margin-bottom: 100px;
            @media screen and (max-width: 600px) {
                margin-top: 65px;
                margin-bottom: 80px;
            }
        }
    }

    .postcontent{
        
    }
    .postthumbnail{
        margin-top: 67px;
        @media screen and (max-width:768px) {
            img{
                margin-bottom: 15px;
            }
        }
    }
    .posttestinomy{
        margin-top: 103px;
        margin-bottom: 103px;
        .entry-testinomy{
            display: flex;
            flex-direction: row;
            flex-grow: 0;
            @media screen and (max-width:991px) {
                margin-bottom: 20px;
            }
            .boxtestinomy{
                align-items: center;
                justify-content: center;
                &.testinomy-bar{
                    height: 160px;
                    flex: 0 0 40px;
                    &.left{ background: url(../images/testinomy_bar.svg) no-repeat;}
                    &.right{ background: url(../images/testinomy_bar.svg) no-repeat; transform: rotate(180deg);}
                    @media screen and (max-width:991px) {
                        height: 148px;
                        background-size: contain !important;
                    }
                }
                &.quote{
                    display: flex;
                    flex: 0 0 475px;
                    align-items: center;
                    justify-content: center;
                    padding: 5px 31px;
                    font-size: 18px;
                    line-height: 28px;
                    font-weight: 700;
                    @media screen and (max-width:1200px) {
                        flex: unset;
                    }
                    @media screen and (max-width:991px) {
                        font-size: 11px;
                        line-height: 16px;
                        padding-left: 5px;
                        padding-right: 5px;
                    }
                }
            }
        }
        div.lastcol{
            display: flex;
            align-items: end;
        }
        .entry-testinomy-name{
            @media screen and (max-width:991px) {
                font-size: 11px;
                line-height: 16px;
                padding-left: 45px;
                padding-right: 45px;
            }
        }
    }
    .postvideo{
        h1.entry-title{
            margin-bottom: 44px;
        }
        @media screen and (max-width:768px) {
            .videocontainer{
                margin-bottom: 15px;
            }
        }
    }
    .postpodcast{
        margin-top: 105px;
        @import "audioplayer.scss";
    }
    .postdownload{
        margin-top: 105px;
            .download-items{
                display: flex;
                flex-wrap: wrap;
                .download-item{
                    /*
                    flex: 0 0 47%;
                    margin-right: 3%;
                    */
                    width: 380px;
                    max-width: 100%;
                    margin-right: 30px;
                    @media screen and (max-width:991px) {
                        flex: 1 0 100%;
                        margin-right: 0;
                    }
                    .itembox{
                        width: 100%;
                        display: block;
                        padding: 17.5px 53px 17.5px 10px;
                        font-size: 14px;
                        line-height: 28px;
                        border-bottom: 1px solid #878787;
                        color: #000;
                        text-decoration: none;
                        background-image: url('../images/download-icon-back.svg');
					    background-repeat: no-repeat;
					    background-position: right 10px center;
                        &:hover{
                            color:#77C2A1;
                            background-image: url('../images/download-icon-green.svg');
                        }
                    }
                }
                .download-item{
                    &:nth-child(1),&:nth-child(2){
                        .itembox{
                            border-top: 1px solid #878787;
                        }
                    }
                    @media screen and (max-width:1199px) {
                        &:nth-child(2){
                            .itembox{
                                border-top:none;
                            }
                        }
                    }

                }
            }
    }

    #video-overlay{
        display: inline-block;
        position: relative;
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        max-width: 600px;
        height: auto;
        min-height:371px;
        @media (max-width:1200px) {
            min-height: 254px;
        }
        @media (max-width:767px) {
            -webkit-appearance: none;
            min-height: 290px;
            display: block;
        }
        @media (max-width:470px) {
            -webkit-appearance: none;
            min-height: 240px;
            display: block;
        }
        &::before{
            content: "";
            width: 45px;
            height: 46px;
            position: absolute;
            display: block;
            background-image: url(../images/videoplay-icon.png);
            background-repeat: no-repeat;
            background-size: cover;
            border-radius: 50px;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            transition: 200ms;
            box-shadow: 0 0 5px gray;
        }
        &:hover{
            &::before{
                transform: scale(1.05);
            }
        }
    }
    #VideoModal{
        iframe{
            @media (min-width:1024px){
                width:800px !important;
                height:450px !important;
            }
        }
        
    }
    .modal{
        .modal-content{
            border: none;
            border-radius : none;
            background: none;
            
        }
        .modal-body{
            padding: 0 !important;
        }
    }
}