@import "styles_variables.scss";

@media (max-width: 1199px) and (min-width: 992px){
    .panel-grid-cell{
        -webkit-flex: unset !important;
        flex:unset !important;
    }
}

@media screen and (min-width: 992px){
    ul.primary-menu{
        .dropdown-toggle::after{
            display: none;
        }
    }
    .addreadmore{
        .readmore,
        .readless {
            display: none;
        }
    }
}

@media screen and (max-width: 991px){
    // html{
    //     margin-top: 0px !important;
    // }
    // #wpadminbar{
    //     display: none;
    //     visibility: hidden;
    // }
    .site-header{
        //position: fixed;
        ul.primary-menu{
            > li{
                > a{
                    padding: 10px 0px;
                }
            }
        }
    }
    .navbar-light .navbar-toggler{
        border: 0px;
        color: $rd-theme-head-menu-font-hover;
        padding-left: 0px;
        padding-right: 0px;
        margin-right: -3px;
    }
    .navbar-toggler-icon {
        width: 32px;
        height: 32px;
        background-image: url('../images/navbar-toggler-icon.svg') !important;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        margin-right: -3px;
    }
    .site-footer{
        .site-footer-info{
            padding-top: 84px;
            padding-bottom: 24px;
            padding-left: 0px;
            padding-right: 0px;
        }
        .widget-title{
            &.toggle{
                &:before{
                    font-family: 'FontAwesome';
                    margin-right: 10px;
                }
                &.collapsed{
                    &:before{
                        content: '\f105';
                    }
                }
                &:not(.collapsed){
                    &:before{
                        content: '\f107';
                    }
                }
            }
        }
        .footer-sitemap{
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            display: -webkit-inline-box !important;
            .panel-grid-cell:last-child{
                padding-top: 38px !important;
            }
        }
        .lsow-icon-list .lsow-icon-list-item{
            margin-left: 22px;
        }
        .footer-subscribe{
            padding-top: 42px;
            padding-bottom: 42px;
            border-top: 0px;
            .widget_mc4wp_form_widget{
                position: relative;
                &::before{
                    content: '';
                    height: 1px;
                    width: 100%;
                    background-color: #FFFFFF;
                    position: absolute;
                    top: -42px;
                }
            }
            h3{
                line-height: 28px;
                position: relative;
                margin-bottom: 22px;
                padding-left: 37px;
                padding-right: 37px;
                display: inline-block;
                &::before,
                &::after{
                    content: '';
                    width: 15px;
                    height: 66px;
                    position: absolute;
                    top: -6px;
                }
                &::before{
                    background-image: url('../images/footer-subscribe-vector-left.svg');
                    left: 0;
                }
                &::after{
                    background-image: url('../images/footer-subscribe-vector-right.svg');
                    right: 0;
                }
            }
            .widget{
                padding-left: 0px;
                padding-right: 0px;
            }
            .form-wrap{
                padding-left: 0px;
                padding-right: 0px;
                > div:first-child::before,
                > div:last-child::after{
                    display: none;
                    visibility: hidden;
                }
            }
        }
        .site-footer-copyright{
            border-top: 0px;
            ul{
                position: relative;
                padding: 0;
                &::before{
                    content: '';
                    height: 1px;
                    width: 100%;
                    background-color: #FFFFFF;
                    position: absolute;
                    top: -15px;
                    left: 0;
                }
            }
        }
        .totop{
            display: none;
            visibility: hidden;
        }
    }

    .addreadmore{
        .secsec{
            margin-bottom: 15px;
        }
        &.showlesscontent{
            .secsec,
            .readless {
                display: none;
            }
        }
        &.showmorecontent{
            .readmore {
                display: none;
            }
        }
        &.button1{
            .readmore,
            .readless {
                width: 159px;
                height: 40px;
                font-weight: bold;
                color: #ffffff;
                text-align: center;
                cursor: pointer;
                background: #77C2A1;
                border: 2px solid #77C2A1;
                margin-top: 20px;
                &:hover{
                    color: #77C2A1;
                    background: #ffffff;
                }
            }
        }
        &.button2{
            .readmore,
            .readless {
                color: #000;
                text-decoration: none;
                position: relative;
                transition: all 0.2s linear;
                background: none;
                border: none;
                padding-left: 0px;
                padding-right: 0px;
                &::before,
                &::after{
                    content: '';
                    width: 7px;
                    height: 23px;
                    transition: all 0.2s linear;
                    display: inline-block;
                    vertical-align: middle;
                }
                &:visited{
                    color: #000 !important;
                }
                &::before{
                    content: '';
                    background: url('../images/menu-hover-left-black-thin.svg') no-repeat left center;
                    margin-right: 10px;
                }
                &::after{
                    background: url('../images/menu-hover-right-black-thin.svg') no-repeat right center;
                    margin-left: 10px;
                }
                &:hover{
                    color: #000 !important;
                    -webkit-text-fill-color: #000;
                    -webkit-text-stroke-width: 1px;
                    -webkit-text-stroke-color: #000;
                    &::before{
                        background: url('../images/menu-hover-left-black.svg') no-repeat left center;
                    }
                    &::after{
                        background: url('../images/menu-hover-right-black.svg') no-repeat right center;
                    }
                }
            }
        }
    }
    .addreadmorewraptext{
        &.showmorecontent{
            .secsec,
            .readless {
                display: block;
            }
        }
    }
    /*
    .addreadmore.showlesscontent .secsec,
    .addreadmore.showlesscontent .readless {
        display: none;
    }

    .addreadmore.showmorecontent .readmore {
        display: none;
    }

    .addreadmore .readmore,
    .addreadmore .readless {
        font-weight: bold;
        margin-left: 2px;
        color: blue;
        cursor: pointer;
    }

    .addreadmorewraptext.showmorecontent .secsec,
    .addreadmorewraptext.showmorecontent .readless {
        display: block;
    }
    */
}

@media screen and (max-width: 600px){
    #wpadminbar{
        position: fixed;
    }
}

@media screen and (max-width: 576px){
    .totop{
        right: 15px;
        bottom: 45px;
        font-size: 0px;
    }
    .site-content .testimony-single{
		.content-warp{
            padding: 0 55px;
			font-size: 16px;
			&::before{
				left: 0;
			}
			&::after{
				right: 0;
			}
			.content{
			}
		}
		.person{

			bottom: -70px;
            right: unset;
            left: 50px;
		}
	}
}
.testimony-slider{
    @media screen and (min-width: 1024px) and (max-width: 1230px){
        .testimony-name{
            display: none;
            visibility: hidden;
        }
    }
}