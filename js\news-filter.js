jQuery(function($){
    size_li = jQuery(".news-result .card").size();
    x=9;
    hiddenItem(size_li, x);

    $('.list-filter').on('click', '.sf-field-taxonomy-news_categories h4, .sf-field-taxonomy-team_categories h4, .sf-field-taxonomy-distributor_categories h4, .sf-field-taxonomy-partners_categories h4', function(e){
        e.preventDefault();
        $(this).closest('li').toggleClass('active');
    });

    jQuery('.news-result .button-area').on('click', '.read-more', function(e){
        e.preventDefault();
        x= (x+9 <= size_li) ? x+9 : size_li;
        //console.log(x);
        jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.news-result .card:lt('+x+')').addClass('is-open');
        }, 200);
        if(x == size_li){
            jQuery(this).css('display', 'none');
            jQuery(this).css('padding-top', 'null');
        }
    });

    function hiddenItem(size_li, x){
        if(size_li > 9){
            jQuery('.news-result .button-area').html('<a class="btn-primary read-more" href="#">'+ new_filter_val.loadmore_text +'</a>');
    
            jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.news-result .card:lt('+x+')').addClass('is-open');
            }, 200);
        }else{
            jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.news-result .card:lt('+x+')').addClass('is-open');
            }, 200);
        }

        if(x == size_li){
            jQuery('.news-result .button-area').css('display', 'none');
            jQuery('.news-result .button-area').css('padding-top', 'null');
        }
    }

    var id;
    $(window).resize(function() {
        //if ($(window).width() > 767){
            clearTimeout(id);
            id = setTimeout(hiddenItem(size_li, x), 500);
        //}
    });

});

(function ( $ ) {
    $(document).on("sf:ajaxfinish", ".searchandfilter", function(){
        size_li = jQuery(".news-result .card").size();
        x=9;
        hiddenItem_sf(size_li,x);
    });
}(jQuery));

function hiddenItem_sf(size_li,x){
    if(size_li > 9){
        jQuery('.news-result .button-area').html('<a class="btn-primary read-more" href="#">'+ new_filter_val.loadmore_text +'</a>');

        jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.news-result .card:lt('+x+')').addClass('is-open');
        }, 200);

        jQuery('.news-result .button-area').on('click', '.read-more', function(e){
            e.preventDefault();
            x= (x+9 <= size_li) ? x+9 : size_li;
            //console.log(x);
            jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
            setTimeout(function () {
                jQuery('.news-result .card:lt('+x+')').addClass('is-open');
            }, 200);
            if(x == size_li){
                jQuery(this).css('display', 'none');
                jQuery(this).css('padding-top', 'null');
            }
        });
    }else{
        jQuery('.news-result .card:lt('+x+')').addClass('is-visible');
        setTimeout(function () {
            jQuery('.news-result .card:lt('+x+')').addClass('is-open');
        }, 200);
    }

    if(x == size_li){
        jQuery('.news-result .button-area').css('display', 'none');
        jQuery('.news-result .button-area').css('padding-top', 'null');
    }
}