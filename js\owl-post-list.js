jQuery(function($){
    
    convert_post_list_owl();
    // init_two_cards_carousel(); // Disabled - Now using Slick Slider

    var id;
    $(window).resize(function() {
        clearTimeout(id);
        id = setTimeout(convert_post_list_owl, 500);
        // id = setTimeout(init_two_cards_carousel, 500); // Disabled - Now using Slick Slider
    });

});

jQuery(document).on("sf:ajaxfinish", ".searchandfilter", function(){
    jQuery('*[id*=search-filter-results-695]:visible').each(function() {
        convert_post_list_owl();
    });
});

function convert_post_list_owl(){
    var $window = jQuery(window);
    var owl = jQuery('.post-list-owl');
    if($window.width() < 768){
        owl.addClass('owl-theme');
        var owlConfig = {
            rtl: false,
            items: 3,
            loop: true,
            //navigation: false,
            dots: false,
            margin: 30,
            autoplay: true,
            autoplayTimeout:4500,
            autoplaySpeed: 1500,
            navSpeed: 1500,
            autoplayHoverPause:true,
            responsiveClass: true,
            responsive: {
                0: {
                    items:1,
                    autoplay: false,
                    stagePadding: 40,
                },
                600: {
                    items:2,
                    autoplay: false,
                    stagePadding: 40,
                },
                991.5: {
                    items:3,
                    autoplay: true,
                    stagePadding: 0,
                }
            }

        }
        owl.owlCarousel(owlConfig);
    }else{
        owl.removeClass('owl-theme');
        owl.trigger('destroy.owl.carousel');
        owl.addClass('off');
    }
}

function init_two_cards_carousel(){
    var $window = jQuery(window);
    var owl = jQuery('.two-cards-row');
    
    // Destroy existing carousel if it exists
    if (owl.hasClass('owl-loaded')) {
        owl.trigger('destroy.owl.carousel');
        owl.removeClass('owl-loaded');
    }
    
    owl.addClass('owl-carousel owl-theme');
    
    var owlConfig = {
        rtl: false,
        items: 1,
        loop: true,
        nav: true,
        navText: ['<span class="owl-nav-prev"></span>', '<span class="owl-nav-next"></span>'],
        dots: true,
        dotsEach: false,
        margin: 30,
        autoplay: true,
        autoplayTimeout: 4500,
        autoplaySpeed: 1500,
        navSpeed: 1500,
        autoplayHoverPause: true,
        responsiveClass: true,
        responsive: {
            0: {
                items: 1,
                nav: true,
                dots: true,
                autoplay: false,
                stagePadding: 20,
            },
            600: {
                items: 1,
                nav: true,
                dots: true,
                autoplay: false,
                stagePadding: 40,
            },
            991.5: {
                items: 2,
                nav: true,
                dots: true,
                autoplay: true,
                stagePadding: 0,
            }
        }
    }
    
    // owl.owlCarousel(owlConfig);
}