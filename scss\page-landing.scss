body.modal-open{
    padding-right: 0px !important;
}
body.modal-open .site-header{
    z-index: 99;
}
body.page-template-page-landing{
    padding-top: 0px !important;
    .site-footer{
        .site-footer-info{
            padding-top: 44px;
            padding-bottom: 44px;
        }
        .footer-logo{
            margin-bottom: 0px;
        }
        .site-footer-copyright{
            border-top: 0px;
            padding-top: 0;
            padding-bottom: 0;
            ul{
                padding-left: 0;
                padding-right: 0;
                li{
                    &:last-child{
                        margin-right: 0px;
                    }
                }
            }
        }
    }

    @media screen and (max-width: 991px){
        .site-footer{
            .site-footer-copyright{
                ul{
                    &::before{
                        display: none;
                    }
                }
            }
        }
    }
    @media screen and (max-width: 767px){
        .site-footer{
            .site-footer-copyright{
                ul{
                    margin-top: 15px;
                    li{
                        margin-right: 0px;
                        margin-bottom: 20px;
                        display: block;
                    }
                }
            }
        }
    }
}
.modal-sf-form{
    .modal-dialog{
        width: 810px;
        max-width: 100%;
        @media screen and (max-width: 991px){
            width: 600px;
        }
        .modal-content{
            border-radius: 0px;
            box-shadow: 0px 2.700000047683716px 6.75px 2.700000047683716px rgba(0, 0, 0, 0.06);
            background-color: #fff;
            .modal-body{
                padding: 48px 45px 45px 45px !important;
                @media screen and (max-width: 576px){
                    padding-top: 55px;
                }
                .close{
                    position: absolute;
                    top: 16px;
                    right: 10px;
                    z-index: 9;
                    opacity: 1;
                    span{
                        content: '';
                        width: 34px;
                        height: 33px;
                        display: block;
                        background-image: url('../images/close-modal.png');
                        background-repeat: no-repeat;
                        background-position: center;
                    }
                }
                .headline-group{
                    margin-bottom: 41px;
                }
            }
        }
    }
}

.modal-thankyou-popup{
    .modal-dialog{
        width: 810px;
        max-width: 100%;
        @media screen and (max-width: 991px){
            width: 600px;
        }
        .modal-content{
            border-radius: 0px;
            box-shadow: 0px 2.700000047683716px 6.75px 2.700000047683716px rgba(0, 0, 0, 0.06);
            .modal-body{
                padding: 48px 45px 45px 45px;
                @media screen and (max-width: 576px){
                    padding-top: 55px;
                }
                .close{
                    position: absolute;
                    top: 16px;
                    right: 10px;
                    z-index: 9;
                    opacity: 1;
                    span{
                        content: '';
                        width: 34px;
                        height: 33px;
                        display: block;
                        background-image: url('../images/close-modal.png');
                        background-repeat: no-repeat;
                        background-position: center;
                    }
                }
                .headline-group{
                    margin-bottom: 41px;
                }
                .content-group{
                    margin-bottom: 41px;
                    padding-right: 150px;
                }
                .button-group{
                    .panel-grid-cell{
                        .panel-cell-style{
                            > div{
                                display: inline-block;
                                margin-bottom: 0px !important;
                                &:first-child{
                                    margin-right: 50px;
                                }
                            }
                        }
                    }
                    @media screen and (max-width: 576px){
                        display: block;
                        .panel-grid-cell{
                            .panel-cell-style{
                                > div{
                                    display: block;
                                    &:first-child{
                                        margin-right: 0px;
                                    }
                                    a{
                                        &.btn-primary{
                                            width: 100%;
                                            display: block;
                                            margin-bottom: 48px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.so-widget-saleforce-form-theme-widget{
    .modal-content{
        h2{
            @media screen and (max-width: 991.9px){
                font-size: 40px;
            }
            @media screen and (max-width: 501px){
                font-size: 28px;
            }
        }
    }
    .salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]{
        height: auto;
        width: auto;
    }
    .salesforce-form .wpcf7-acceptance .wpcf7-list-item label .wpcf7-list-item-label a{
        font-weight: 700;
    }
    .salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:after{
        top: 2px;
    }
}
.img-full-width{
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
    max-width: 1000%;
    width: auto;
    .so-widget-image-out-container-theme-widget{
        .img-out-container-warp{
            width: 100%;
            img{
                width: 100%;
            }
        }
    }
}