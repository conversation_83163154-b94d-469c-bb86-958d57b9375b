form.searchandfilter ul {
  padding: 0;
}
form.searchandfilter ul > li {
  border: 0 !important;
  padding: 0;
}
form.searchandfilter ul > li > ul {
  display: flex;
  width: 100% !important;
  position: initial !important;
  visibility: unset !important;
  height: auto !important;
  border: 0 !important;
  background: transparent !important;
  padding: 0;
}
@media screen and (max-width: 768px) {
  form.searchandfilter ul > li > ul {
    display: block;
  }
}
form.searchandfilter ul > li > ul li {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  position: relative;
  margin-right: 30px;
}
@media screen and (max-width: 768px) {
  form.searchandfilter ul > li > ul li {
    display: inline-block;
  }
}
@media screen and (max-width: 485px) {
  form.searchandfilter ul > li > ul li {
    margin-right: 15px;
  }
}
form.searchandfilter ul > li > ul li.sf-item-0:first-child {
  font-weight: 700;
}
form.searchandfilter ul > li > ul li.sf-option-active {
  padding: 0;
  background: transparent;
}
form.searchandfilter ul > li > ul li.sf-option-active .sf-label-radio {
  font-weight: 700;
}
form.searchandfilter ul > li > ul li.sf-option-active .sf-label-radio:after {
  transform: scaleX(1);
}
form.searchandfilter ul > li > ul li.sf-option-active:after {
  transform: scaleX(1);
}
form.searchandfilter ul > li > ul li.sf-option-active:after:hover {
  display: none;
}
form.searchandfilter ul > li > ul li .sf-input-radio {
  display: none;
}
form.searchandfilter ul > li > ul li .sf-label-radio {
  padding: 0;
  font-size: 14px;
  font-weight: 100;
  line-height: 1.2;
  color: #000;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  transition: all 0.2s ease;
}
@media screen and (max-width: 485px) {
  form.searchandfilter ul > li > ul li .sf-label-radio {
    font-size: 20px;
  }
}
form.searchandfilter ul > li > ul li .sf-label-radio:before {
  content: attr(data-text);
  content: attr(data-text)/"";
  height: 0;
  visibility: hidden;
  overflow: hidden;
  user-select: none;
  pointer-events: none;
  font-weight: 700;
}
form.searchandfilter ul > li > ul li .sf-label-radio:after:hover {
  font-weight: 700;
  text-decoration: none;
}
form.searchandfilter ul > li > ul li:after {
  content: '';
}
form.searchandfilter ul > li > ul li:hover .sf-label-radio {
  color: #000;
  -webkit-text-fill-color: #000;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #000;
  text-decoration: none;
}
form.searchandfilter ul > li > ul li:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}
form.searchandfilter ul > li:after {
  display: none;
}

.partners-items {
  margin-top: 95px;
}
.partners-items .partners-item {
  display: inline-flex;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
  flex: 0 0 calc(25% - 30px);
  margin: 0 15px -1px 15px;
  padding: 30px 25px;
  background-color: #FFF;
  transition: all 0.3s linear;
}
.partners-items .partners-item .card {
  border: 0;
  background-color: transparent;
  padding: 0;
}
.partners-items .partners-item .card .card-photo {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}
.partners-items .partners-item .card .card-photo img {
  max-height: 70px;
}
.partners-items .partners-item .card .card-body {
  padding: 0;
  font-size: 14px;
}
.partners-items .partners-item .card .card-body h3 {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 0;
}
.partners-items .partners-item .card .card-body p {
  font-size: 11px;
}
.partners-items .partners-item .card .card-body a {
  color: #000;
  text-decoration: none;
  transition: all 0.3s linear;
}
.partners-items .partners-item .card .card-body a:hover {
  color: #77C2A1;
}
.partners-items .partners-item:hover .card .card-body a.text-link {
  color: #77C2A1;
}
