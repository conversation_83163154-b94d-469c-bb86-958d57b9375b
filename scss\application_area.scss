@import "compass";

.card-icon-wrapper{
  -webkit-align-items: initial !important;
  align-items: initial !important;
  &.bt{
    .card-icon{
      border-top: 1px solid rgba(135,135,135,0.2);
    }
  }
  &.bb{
    .card-icon{
      border-bottom: 1px solid rgba(135,135,135,0.2);
    }
  }
  .card-icon{
    /*
    border-top: 1px solid rgba(135,135,135,0.2);
    border-bottom: 1px solid rgba(135,135,135,0.2);
    */
    padding: 14px 25px 20px 25px;
    transition: all 0.3s linear;
    position: relative;
    @media screen and (max-width: 992px) {
      padding: 20px 0px 40px 0px;
    }
    .panel-first-child{
      margin-bottom: 0 !important;
    }
    a.absolute-link{
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 2;
      top: 0;
      left: 0;
    }
    .text-body{
      h4{
        color:#000;
        font-size: 18px;
        font-weight: 700;
        line-height: 1.6;
        margin-bottom: 13px;
      }
      a.arrow-link{
        font-size: 0;
        background: transparent;
        border: 0;
        @media screen and (max-width: 992px) {
          float: left;
        }
        &:after{
          content: '';
          display: inline-flex;
          margin-left: 0;
          width: 36px;
          height: 36px;
          background: transparent;
          background-image: url(../images/arrow-right-white.svg);
          background-repeat: no-repeat;
          background-size: contain;
          background-position: center;
          transition: background-image 0.3s linear;
          @media screen and (max-width: 991px) {
            background-image: url(../images/arrow-right-black.svg);
            transition: background-image 0s linear;
          }
        }
      }
    }
    &:hover{
      background-color: #E4F3EC;
      // @media screen and (max-width: 991px) {
      //   background: none;
      // }
      .text-body{
        .arrow-link{
          background: transparent !important;
          border: 0;
          &::after {
            background-image: url(../images/arrow-right-green.svg);
            @media screen and (max-width: 991px) {
              // background-image: url(../images/arrow-right-black.svg);
              transition: background-image 0s linear;
            }
          }
        }
      }
    }
  }
}

.childTabMenu{
  background-color: #77C2A1;
  position: relative;
  opacity: 1;
  @include transition-property(all);
  @include transition-duration(0.5s);
  @include transition-timing-function(ease);
  .nav{
    border: 0px;
    align-items: center;
    .nav-item{
      margin-bottom: 0px;
      padding: 0 15px;
      max-width: 245px;
      text-align: center;
      line-height: 1.4;
      .nav-link{
        font-size: 14px;
        font-weight: 400;
        color: #FFF;
        line-height: 1.4 !important;
        text-decoration: none;
        padding: 17px 0px;
        background: none;
        border: 0px;
        position: relative;
        display: block;
        transition: all 0.3s ease;
        span{
          display: block;
          &::before{
            display: block;
            content: attr(data-text);
            font-weight: bold;
            height: 0px;
            color:transparent;
            overflow: hidden;
            visibility: hidden;
            letter-spacing: 0.6px;
          }
        }
        &:hover,
        &.active{
          &::before,
          &::after{
            opacity: 1;
          }
          span{
            font-weight: bold;
          }
        }
        &::before,
        &::after{
          content: '';
          width: 8px;
          height: 23px;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          opacity: 0;
          @include transition-property(all);
          @include transition-duration(0.3s);
          @include transition-timing-function(ease-in-out);
        }
        &::before{
          background: url('../images/menu-hover-left-white.svg') no-repeat left center;
          left: -10px;
        }
        &::after{
          background: url('../images/menu-hover-right-white.svg') no-repeat right center;
          right: -10px;
        }
      }
    }
    &.application-areas{
      .nav-item{
        max-width: 198px;
      }
    }
  }
  .nav-mobile{
    display: none;
  }
  .nav-mobile-button{
    display: none;
  }
  @media screen and (max-width: 992px) {
    position: relative;
    width: 100%;
    z-index: 15;
    margin-top: -2px;
    transition: all 0.3s ease;
    &.show{
      position: fixed;
    }
    .container {
      position: relative;
      margin: auto;
      padding: 0;
      .nav-mobile-button{
        display: block;
        width: 100%;
        height: 16px;
        background: url(../images/arrow-down-white.svg) no-repeat;
        background-size: auto;
        background-position: center right;
        border: 0;
        position: absolute;
        right: 15px;
        top: 19px;
        &.up{
          background: url(../images/arrow-up-white.svg) no-repeat;
          background-size: auto;
          background-position: center right;
        }
      }
      ul.nav{
        display: none;
      }
      ul.nav-mobile{
        display: flex;
        flex-wrap: wrap;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
        li.nav-item{
          margin-bottom: 0px;
          // padding: 0 15px;
          text-align: left;
          line-height: 1.4;
          transition: all 0.3s ease;
          .nav-link {
            display: inline-block;
            font-weight: 400;
            color: #FFF;
            text-decoration: none;
            background: none;
            border: 0px;
            position: relative;
            letter-spacing: 1px;
            transition: all 0.3s ease;
          }
        }
      }
    }
    ul.nav-mobile li.nav-item:first-child {
      height: 53px;
      .nav-link{
        font-size: 14px;
        font-weight: 700;
        line-height: 1.4;
        padding: 17px 15px;
      }
    }
    ul.nav-mobile li.nav-item {
      height: 0;
      .nav-link{
        font-size: 0;
        line-height: 0;
      }
    }
    ul.nav-mobile.show li.nav-item {
      height: 53px;
      border-bottom: 1px solid #FFF;
      .nav-link{
        font-size: 14px;
        line-height: 1.4;
        padding: 17px 15px;
        display: block;
      }
    }
  }
  @media screen and (max-width: 330px) {
    ul.nav-mobile li.nav-item:first-child {
      .nav-link{
        font-size: 11px;
      }
    }
    ul.nav-mobile.show li.nav-item {
      .nav-link{
        font-size: 11px;
      }
    }
  }
}