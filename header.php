<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
<meta charset="<?php bloginfo( 'charset' ); ?>">
<meta http-equiv="x-ua-compatible" content="ie=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<link rel="pingback" href="<?php bloginfo( 'pingback_url' ); ?>">

<?php wp_head(); ?>

<?php
	global $radar_fd_option;
	if($radar_fd_option['favicon']['url']){
		echo '<link rel="shortcut icon" type="image/x-icon" href="'.$radar_fd_option['favicon']['url'].'">';
	}
?>
<script id="usercentrics-cmp" src="https://app.usercentrics.eu/browser-ui/latest/loader.js" data-settings-id="iw-jgs3WICH8QL" async></script>
</head>

<body <?php body_class(); ?>>
	<?php
	if (!is_page_template('page-landing.php')) {
		?>
	<div class="site-header" itemscope itemtype="http://schema.org/WPHeader">
		<div class="site-header-bg"></div>
		<div class="container">
			<div class="nav-primary">
					<nav class="navbar navbar-expand-lg navbar-light align-items-center">
						<?php
						if($radar_fd_option['logo']['url']){
							echo '
								<a href="'.home_url().'" class="navbar-brand">
									<img src="'.$radar_fd_option['logo']['url'].'">
								</a>
							';
						}
						?>
						<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#mobile-menu" aria-controls="menu" aria-expanded="false" aria-label="Toggle navigation">
							<span class="navbar-toggler-icon"></span>
						</button>

						<div class="collapse navbar-collapse" id="menu" itemscope itemtype="http://schema.org/SiteNavigationElement">

							<div class="navbar-desktop d-none d-lg-block">
								<div id="top-menu" class="d-flex justify-content-end align-items-center">
									<?php
									wp_nav_menu(array(
										'theme_location' => 'top-menu',
										'container' => 'ul',
										'menu_class'	=> 'navbar-nav-desktop navbar-nav ml-auto justify-content-end top-menu',
										'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
										'depth' => 2
									));
									?>
									<?php
									if($radar_fd_option['enabled-top-search-icon']==1){
										?>
										<div class="header-search d-none d-lg-block">
											<a class="search-icon collapsed" data-toggle="collapse" href="#header-search-form" aria-expanded="false">
												<img src="<?php echo get_stylesheet_directory_uri(); ?>/images/search-icon.svg">
											</a>
											<div id="header-search-form" class="header-search-form collapse">
												<?php get_search_form(); ?>
											</div>
										</div>
										<?php
									}
									?>
								</div>
								<div id="primary-menu">
									<?php
									wp_nav_menu(array(
										'theme_location' => 'primary',
										'container' => 'ul',
										'menu_class'	=> 'navbar-nav-desktop navbar-nav ml-auto justify-content-end primary-menu',
										'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
										'depth' => 2
									));
									?>
								</div>
							</div>
						</div>
					</nav>
				</div>
		</div>

		<div class="collapse navbar-collapse" id="mobile-menu" itemscope itemtype="http://schema.org/SiteNavigationElement">
			<div class="navbar-mobile d-block d-lg-none">
				<div class="container">
					<?php
					wp_nav_menu(array(
						'theme_location' => 'mobile-top-menu',
						'container' => 'ul',
						'menu_class'	=> 'navbar-nav-mobile navbar-nav ml-auto justify-content-end primary-menu',
						'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
						'depth' => 0,
						'fallback_cb' => 'wp_bootstrap_navwalker::fallback',
						'walker' => new wp_bootstrap_navwalker()
					));
					?>
					<?php
					wp_nav_menu(array(
						'theme_location' => 'mobile-top-menu-2',
						'container' => 'ul',
						'menu_class'	=> 'navbar-nav-desktop navbar-nav ml-auto justify-content-end top-menu',
						'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
						'depth' => 0
					));
					?>
					<div class="d-flex justify-content-between align-items-top">
						<?php echo do_shortcode('[wpml_language_switcher type="custom"]'); ?>
						<?php
							if($radar_fd_option['enabled-top-search-icon']==1){
								?>
								<div class="header-search d-flex justify-content-between align-items-top">
									<div id="header-search-form-mobile" class="header-search-form">
										<?php get_search_form(); ?>
									</div>
									<a class="header-search-form-mobile-icon" href="#">
										<img src="<?php echo get_stylesheet_directory_uri(); ?>/images/search-icon.svg">
									</a>
								</div>
								<?php
							}
						?>
					</div>
				</div>
			</div>
		</div>

		<?php
		$currentPost = get_post(get_the_ID());
		$current_ID = $currentPost->ID;
		$parent = $currentPost->post_parent;
		if($parent!=0){
			$parent_ID = $currentPost->post_parent;
		}else{
			$parent_ID = $currentPost->ID;
		}
		$show_sub_page = get_field('show_sub_page', $current_ID);
		//if($parent_ID != $current_ID && $currentPost->post_type == 'page' && in_array($parent_ID,['63','98','2228','2269'])):
		if($parent_ID != $current_ID && $currentPost->post_type == 'page' && $show_sub_page == true):
		?>
			<div class="childTabMenu tabmenu">
				<div class="container">
					<?php
					$args_child_pages = array(
						'post_parent' =>  $parent_ID,
						'posts_per_page' => -1,
						'post_type' => 'page',
						'orderby' => 'menu_order',
						'order' => 'ASC'
					);
					$slug = get_post_field( 'post_name', $parent_ID );
					$child_pages = new WP_Query( $args_child_pages );
					if ( $child_pages->have_posts() ){
						?>
						<ul class="nav justify-content-around flex-column flex-lg-row <?php echo $slug; ?>">
							<?php
							$mobileSort = 1; $mobileMenuList = [];
							while ( $child_pages->have_posts() ){
								$child_pages->the_post();
								if($current_ID == get_the_ID()){
									$mobileMenuList[0] = ['link'=>get_permalink(),'title'=>get_the_title()];
								}else{
									$mobileMenuList[$mobileSort] = ['link'=>get_permalink(),'title'=>get_the_title()];
									$mobileSort++;
								}
								?>
								<li class="nav-item">
									<a href="<?php the_permalink(); ?>" class="nav-link <?php echo ($current_ID == get_the_ID() ? 'active' : ''); ?>"><span data-text="<?php echo get_the_title(); ?>"><?php the_title(); ?></span></a>
								</li>
								<?php
							}
							ksort($mobileMenuList);
							//echo "<pre>"; print_r($mobileMenuList); echo "</pre>";
							?>
						</ul>
						<ul class="nav-mobile flex-column flex-lg-row">
							<?php  foreach($mobileMenuList as $menuItem): ?>
								<li class="nav-item">
									<a href="<?=$menuItem['link']?>" class="nav-link"><?=$menuItem['title']?></a>
								</li>
							<?php endforeach; ?>
						</ul>
						<input type="button" class="nav-mobile-button">
						<script>
							jQuery(document).ready(function($){
								$('.childTabMenu input.nav-mobile-button:not(.up)').click(function(){
									if($(document).width() <= 991) {
										$(this).closest('.childTabMenu').toggleClass('show');
										$(this).closest('.childTabMenu').find('.nav-mobile').toggleClass('show');
										$(this).toggleClass('up');
									}
								});
							});

						</script>
						<?php
						wp_reset_postdata();
					}
					?>
				</div>
			</div>
		<?php endif; ?>
		
		<?php if ( is_singular( 'products' ) ) { ?>
			<?php
			$current_ID = $post->ID;
			$parent = $post->post_parent;
			if($post->post_parent!=0){
				$parent_ID = $post->post_parent;
				$parent_post = get_post($parent_ID);
			}else{
				$parent_ID = $post->ID;
				$parent_post = get_post($parent_ID);
			}
			if(get_field('submenu_color', $parent_ID)){
				$product_tabmenu_style = 'background-color:'.get_field('submenu_color', $parent_ID).';';
			}
			?>
		<div class="productTabMenu tabmenu" style="<?php echo $product_tabmenu_style; ?>">
			<div class="container">
				<?php
				$args_child_pages = array(
					'post_parent' =>  $parent_ID,
					'posts_per_page' => -1,
					'post_type' => 'products',
					'orderby' => 'menu_order',
					'order' => 'ASC'
				);
				$child_pages = new WP_Query( $args_child_pages );
				if ( $child_pages->have_posts() ){
					?>
					<ul class="nav justify-content-between flex-column flex-lg-row">
						<?php
						if($parent!=0){
							?>
							<li class="nav-item d-block d-lg-none d-xl-none ">
								<a href="<?php echo $url; ?>" class="nav-link active"><span data-text="<?php echo get_the_title(); ?>"><?php the_title(); ?></span></a>
							</li>
							<?php
						}
						?>
						<li class="nav-item">
							<a href="<?php the_permalink($parent_ID); ?>" class="nav-link <?php echo ($parent==0 ? 'active' : ''); ?>"><span data-text="<?php _e('Overview', 'radar_child_fd'); ?>"><?php _e('Overview', 'radar_child_fd'); ?></span></a>
						</li>
						<?php
						while ( $child_pages->have_posts() ){
							$child_pages->the_post();
							$page_for_resources = get_field('page_for_resources');
							$url = get_the_permalink();
							if($page_for_resources == true){
								$url = add_query_arg( '_sft_products_tags', $parent_post->post_name, $url );
							}
							if($current_ID == $post->ID){
								$add_class = 'active';
								$mobile_class = 'd-none d-lg-block d-xl-block';
							}else{
								$add_class = '';
								$mobile_class = '';
							}
							?>
							<li class="nav-item">
								<a href="<?php echo $url; ?>" class="nav-link <?php echo $add_class.' '.$mobile_class; ?>"><span data-text="<?php echo get_the_title(); ?>"><?php the_title(); ?></span></a>
							</li>
							<?php
						}
						?>
					</ul>
					<?php
					wp_reset_postdata();
				}
				?>
			</div>
		</div>
		<?php } ?>
	</div>
	<?php
	}
	?>

	<div class="site-content" itemscope itemtype="http://schema.org/WebPage">