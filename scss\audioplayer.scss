.audio-player {
  background: white;
  border: 1px solid lighten(#acacac, 20%);
  width: 50vw;
  max-width: 420px;
  text-align: left;
  display: flex;
  flex-flow: row;
  @media screen and (max-width:767px) {
    width: 100%;
    max-width: unset;
  }
  .album-image {
    height: 106.5px;
    width: 106.5px;
    background-size: cover;
  }
  .player-controls {
    align-items: center;
    justify-content: center;
    flex: 3;
    position: relative;
    p{
        line-height: 16px;
        padding-top: 15px;
        padding-left: 20px;
        margin-bottom: 0;
    }
    .audiotitle{
        font-size: 14px;
        font-weight: 700;
        display: block;
        max-height: 34.2px;
        overflow: hidden;
    }
    .audiosubtitle{
        font-size: 11px;
        display: block;
        padding-top: 3px;
    }
    progress {
      width: 100%;
      position: absolute;
      bottom: 0;
    }
    progress[value] {
      -webkit-appearance: none;
      appearance: none;
      background-color: #ADDAC7;
      color: #6ead93;
      height: 10px;
    }
    progress[value]::-webkit-progress-bar {
      background-color: #ADDAC7;
      border-radius: 2px;
      //border: 1px solid lighten(#acacac, 20%);
      color: #6ead93;
    }
    progress::-webkit-progress-value {
      background-color: #6ead93;
    }
    p {
      font-size: 1.6rem;
    }
  }
  #play-btn {
    background-image: url('../images/audio-playicon.png');
    background-size: cover;
    width: 50px;
    height: 50px;
    position: absolute;
    left: 43px;
    top: 30px;
    cursor: pointer;
    transition: 200ms;
    box-shadow: 0 0 5px gray;
    border-radius: 50px;
    background-color: #77C2A1;
    &.pause {
      background-image: url('../images/audio-pauseicon.png');
    }
    &:hover{
        transform: scale(1.05);
    }
  }
}