<?php
function get_target_categories_tax(){
    $target_categories = get_terms('target_categories');
    $target_categorie_arr = array();
    if($target_categories){
        foreach ($target_categories as $target_categories_key => $target_categories_value) {
            //========================= target families
            $target_families_relation = get_field('target_families_relation', $target_categories_value);
            if($target_families_relation){
                foreach ($target_families_relation as $target_families_key => $target_families_value) {
                    $target_categorie_arr[$target_categories_value->slug][] = $target_families_value->slug;
                }
            }
        }
    }
    
    return $target_categorie_arr;
}

function get_target_families_tax(){
    $target_families = get_terms('target_families');
    $target_families_arr = array();
    if($target_families){
        foreach ($target_families as $target_families_key => $target_families_value) {
            $target_relation = get_field('target_relation', $target_families_value);
            if($target_relation){
                foreach ($target_relation as $target_relation_key => $target_relation_value) {
                    $target_families_arr[$target_families_value->slug][] = $target_relation_value->slug;
                }
            }
        }
    }
    
    return $target_families_arr;
}

// ========================================== test preview value relation
function get_target(){
    ob_start();
    echo '<pre>';
    print_r(get_target_categories_tax());
    print_r(get_target_families_tax());
    echo '</pre>';
    return ob_get_clean();
}
add_shortcode('get_target', 'get_target');

function search_array_return_key($data_array, $data_value){
    foreach ($data_array as $key => $value) {
        if (in_array($data_value, $value)){
            $data_return = $key;
        }
    }
    return $data_return;
}

// ========================================== override input obect fileter only sfid 2368
//add_filter( 'sf_input_object_pre', 'search_filter_ajax_object_all_targetdatabase_setglobal', 10, 2 );
function search_filter_ajax_object_all_targetdatabase_setglobal($input_args, $sfid){
    error_log(print_r($input_args, true));
    if($sfid == '2368'){
        global $target_categories_select, $target_families_select, $target_select;

        // ======================================================= set global value
        if($input_args['name'] == '_sft_target_categories'){
            if($target_categories_select == ''){
                $target_categories_select = $input_args['defaults'];
            }
        }
        if($input_args['name'] == '_sft_target_families'){
            if($target_families_select == ''){
                $target_families_select = $input_args['defaults'];
            }
        }
        if($input_args['name'] == '_sft_targets'){
            if($target_select == ''){
                $target_select = $input_args['defaults'];
            }
        }
        // ======================================================= set global value
        /*
        if(count($target_families_select) > 0){
            $defaults_target_categories = array();
            $get_target_categories_tax = get_target_categories_tax();
            foreach ($target_families_select as $key => $value) {
                if($value != ''){
                    $defaults_target_categories[] = search_array_return_key($get_target_categories_tax, $value);
                }
            }
            if(count($defaults_target_categories) > 0){
                $target_categories_select = $defaults_target_categories;
            }
        }
        */
        if(count($target_select) > 0){
            $defaults_target_families = array();
            $get_target_families_tax = get_target_families_tax();
            foreach ($target_select as $key => $value) {
                if($value != ''){
                    $defaults_target_families[] = search_array_return_key($get_target_families_tax, $value);
                }
            }
            if(count($defaults_target_families) > 0){
                $defaults_target_categories = array();
                $get_target_categories_tax = get_target_categories_tax();
                foreach ($defaults_target_families as $key => $value) {
                    if($value != ''){
                        $defaults_target_categories[] = search_array_return_key($get_target_categories_tax, $value);
                    }
                }
                if(count($defaults_target_categories) > 0){
                    $target_categories_select = $defaults_target_categories;
                }
            }
        }
        // ======================================================= get check value defaults

        // ======================================================= get check value defaults

        // ======================================================= get object filter
        // if($input_args['name'] == '_sft_target_categories'){
        //     $input_args['options'] = object_pre_target_categories($input_args['options']);
        // }
        if($input_args['name'] == '_sft_target_families'){
            $input_args['options'] = object_pre_target_families($input_args['options']);
        }
        if($input_args['name'] == '_sft_targets'){
            $input_args['options'] = object_pre_target($input_args['options']);
        }
        // ======================================================= get object filter

    }
    return $input_args;
}

function object_pre_target_categories($input_args_options){
    return $input_args_options;
}

function object_pre_target_families($input_args_options){
    global $target_categories_select, $target_families_select, $target_select;
    $new_input_args = array();
    if($target_categories_select){
        $get_target_categories_tax = get_target_categories_tax();
        foreach($target_categories_select as $tc_select){
            if($tc_select){
                foreach ($input_args_options as $key => $value) {
                    if (in_array($value->value, $get_target_categories_tax[$tc_select])){
                        $check_column_value = array_column($new_input_args, 'value');
                        if(!in_array($value->value, $check_column_value)){
                            $new_input_args[] = $value;
                        }
                    }
                }
            }
        }
        if(count($new_input_args) > 0){
            $column_value = array_column($new_input_args, 'value');
            array_multisort($column_value, SORT_ASC, $new_input_args);
            $input_args_options = $new_input_args;
        }
    }

    // ========================= check target families select first
    if($target_families_select){
        $target_categories_select_arr = array();
        foreach($target_families_select as $tf_select){
            if($tf_select != ''){
                $get_target_categories_tax = get_target_categories_tax();
                foreach ($get_target_categories_tax as $key => $value) {
                    if (in_array($tf_select, $value)){
                        if(!in_array($key, $target_categories_select_arr)){
                            $target_categories_select_arr[] = $key;
                        }
                    }
                }
            }
        }
        if(count($target_categories_select_arr) > 0){
            $target_categories_select = $target_categories_select_arr;
            foreach($target_categories_select as $tc_select){
                if($tc_select){
                    foreach ($input_args_options as $key => $value) {
                        if (in_array($value->value, $get_target_categories_tax[$tc_select])){
                            $check_column_value = array_column($new_input_args, 'value');
                            if(!in_array($value->value, $check_column_value)){
                                $new_input_args[] = $value;
                            }
                        }
                    }
                }
            }
            if(count($new_input_args) > 0){
                $column_value = array_column($new_input_args, 'value');
                array_multisort($column_value, SORT_ASC, $new_input_args);
                $input_args_options = $new_input_args;
            }
        }
    }
    // ========================= check target families select first

    return $input_args_options;
}

function object_pre_target($input_args_options){
    global $target_categories_select, $target_families_select, $target_select;

    $get_target_categories_tax = get_target_categories_tax();
    $get_target_families_tax = get_target_families_tax();

    if($target_categories_select){
        $target_for_categories = array();
        foreach($target_categories_select as $tc_select){
            foreach($get_target_categories_tax[$tc_select] as $tf_select){
                foreach ($input_args_options as $key => $value) {
                    if (in_array($value->value, $get_target_families_tax[$tf_select])){
                        $check_column_value = array_column($target_for_categories, 'value');
                        if(!in_array($value->value, $check_column_value)){
                            $target_for_categories[] = $value;
                        }
                    }
                }
            }
        }
    }
    if(count($target_for_categories) > 0){
        $column_value = array_column($target_for_categories, 'value');
        array_multisort($column_value, SORT_ASC, $target_for_categories);
        $input_args_options = $target_for_categories;
    }
    
    if($target_families_select){
        $target_for_families = array();
        foreach($target_families_select as $tf_select){
            if($tf_select){
                foreach ($input_args_options as $key => $value) {
                    if (in_array($value->value, $get_target_families_tax[$tf_select])){
                        $check_column_value = array_column($target_for_families, 'value');
                        if(!in_array($value->value, $check_column_value)){
                            $target_for_families[] = $value;
                        }
                    }
                }
            }
        }
    }
    if(count($target_for_families) > 0){
        $column_value = array_column($target_for_families, 'value');
        array_multisort($column_value, SORT_ASC, $target_for_families);
        $input_args_options = $target_for_families;
    }

    return $input_args_options;
}

add_filter('terms_clauses', 'target_categories_apply_order_filter', 10, 3);
function target_categories_apply_order_filter($clauses, $taxonomies, $args){
    if($taxonomies[0] == 'target_categories' || $taxonomies[0] == 'products_tags'){
        $clauses['orderby'] =   'ORDER BY t.term_order';
    }
    return $clauses;
}
?>