@import "compass";

body.resource-library,
body.single-products{
    .childTabMenu{
        background-color: #1D418E;
        .nav{
            justify-content: center !important;
            .nav-item{
                width: auto;
                max-width: unset;
                margin-left: 110px;
                margin-right: 110px;
            }
        }
    }

    .resource-search{
        align-items: flex-start;
        .form-group{
            max-width: 100%;
            margin-bottom: 15px;
            position: relative;
        }
        input[type=text]{
            /*width: 1028px;*/
            height: 40px;
            max-width: 100%;
            margin-right: 23px;
            background: transparent;
            border: 1px solid #878787;
            border-radius: 0px;
            &::-webkit-input-placeholder { /* Edge */
                color: #C4BFC0;
                opacity: 1;
            }
            &:-ms-input-placeholder { /* Internet Explorer 10-11 */
                color: #C4BFC0;
                opacity: 1;
            }
            &::placeholder {
                color: #C4BFC0;
                opacity: 1;
            }
        }
        .icon-search{
            &::after{
                content: '';
                width: 16px;
                height: 16px;
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                background-image: url('../images/search-icon.svg');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
            }
        }
        button{
            border-radius: 0px;
        }
    }

    .searchandfilter{
        position: relative;
        ul{
            width: 100%;
            margin: 0;
            padding: 0;
            vertical-align: middle;
            display: flex;
            flex-flow: wrap;
            align-items: flex-start;
            justify-content: flex-start;
            @media screen and (max-width: 991px){
                display: block;
            }
            li{
                padding: 0;
                margin: 0;
                &::before{
                    display: none;
                }
                @media screen and (max-width: 991px){
                    width: 100%;
                }
            }
            li.sf-field-taxonomy-capacity_online{
                ul li label::after{
                    // content: "trays/min";
                    // padding-left: 5px;
                }
            }
            li[data-sf-field-input-type=checkbox] , 
            li[data-sf-field-input-type=radio], 
            li[data-sf-field-input-type=range-radio], 
            li[data-sf-field-input-type=range-checkbox]{
                flex: 1 1 0px;
                margin-right: 30px;
                position: relative;
                cursor: pointer;
                @media screen and (max-width: 991px){
                    // border-bottom: 1px solid #878787 !important;
                    /*
                    border-left: 1px solid #878787 !important;
                    border-right: 1px solid #878787 !important;
                    */
                    /*
                    &:first-child{
                        border-top: 1px solid #878787 !important;;
                    }
                    */
                    position: relative;
                    &::after{
                        content: '\f107';
                        font-family: "Font Awesome 5 Free";
                        font-weight: 900;
                        color: #77C2A1;
                        position: absolute;
                        // top: 14px;
                        right: 0px;
                        top: 38px;
                        // line-height: 100%;
                        display: block;
                        z-index: 99;
                        width: 40px;
                        height: 40px;
                        text-align: center;
                        line-height: 40px;
                    }
                }
                h4{
                    font-size: 14px;
                    font-weight: bold;
                    color: #77C2A1;
                    margin: 0;
                    padding-bottom: 10px !important;
                    padding: 0;
                    @media screen and (max-width: 991px){
                        padding-top: 10px;
                        padding-bottom: 0px !important;
                    }
                }
                ul{
                    max-height: 160px;
                    //========================= make css scroll
                    overflow-y: scroll; //ff support
                    scrollbar-color: #68C29F transparent; //ff support
                    scrollbar-width: thin; //ff support
                    //========================= make css scroll
                    width: 100%;
                    margin: 0;
                    padding: 0px;
                    padding-top: 1px;
                    border-radius: 0px;
                    display: block;
                    @media screen and (max-width: 991px){
                        /*
                        height: 0 !important;
                        visibility: hidden !important;
                        display: none !important;
                        */
                        width: 100% !important;
                        height: 41px !important;
                        padding-top: 0px;
                        border: 1px solid #878787 !important;
                        overflow: hidden;
                    }
                    //========================= make css scroll
                    &::-webkit-scrollbar {
                        width: 8px;
                    }
                    &::-webkit-scrollbar-track {
                        background-color: transparent;
                    }
                    &::-webkit-scrollbar-thumb {
                        background-color: #68C29F;
                        border-radius: 2000px;
                        height: 21px;
                    }
                    //========================= make css scroll
                    li{
                        line-height: 24px !important;
                        color: #000000;
                        position: relative;
                        padding: 7px 10px !important;
                        font-weight: 500;
                        display: block;
                        border-top: 1px solid #878787 !important;
                        border-bottom: 1px solid #878787 !important;
                        margin-top: -1px;
                        margin-right: 0px;
                        background: #f2f2f2;
                        @media screen and (max-width: 991px){
                            /*
                            border-top: 1px solid #878787 !important;
                            border-left: 1px solid #878787 !important;
                            border-right: 1px solid #878787 !important;
                            border-bottom: 0px !important;
                            */
                            margin-top: 0px;
                            line-height: 40px !important;
                            border: 0px !important;
                            width: 100%;
                            display: none;
                            &:first-child{
                                display: list-item;
                            }
                        }
                        &::before{
                            display: none;
                        }
                        &:first-child{
                            visibility: visible;
                            /*border-top: 1px solid #878787 !important;*/
                            @media screen and (max-width: 991px){
                                //border: 0px !important;
                            }
                        }
                        input[type="radio"], 
                        input[type="checkbox"]{
                            width: 0px;
                            position: absolute;
                            top: 10px;
                            visibility: hidden;
                        }
                        label{
                            color: #000000;
                            -webkit-text-fill-color: unset;
                            -webkit-text-stroke-width: 0px !important;
                            -webkit-text-stroke-color: unset;
                            display: block;
                            padding-left: 0px;
                            position: relative;
                            cursor: pointer;
                            line-height: 24px !important;
                            @media screen and (max-width: 991px){
                                color: #C4BFC0;
                            }
                        }
                        &:hover,
                        &.sf-option-active{
                            background-color: #E4F3EC;
                            @media screen and (max-width: 991px){
                                background-color: transparent;
                            }
                            label{
                                &::after{
                                    content: '';
                                    width: 13px;
                                    height: 13px;
                                    display: inline-block;
                                    vertical-align: middle;
                                    margin-left: 8px;
                                    background-image: url('../images/checked.svg');
                                    background-repeat: no-repeat;
                                    background-size: contain;
                                    background-position: center;
                                    -webkit-text-stroke-width: 0px;
                                }
                            }
                        }
                    }
                }
                &.active{
                    h4{
                        color: #77C2A1;
                    }
                    &::after{
                        content: '\f106';
                        color: #77C2A1;
                    }
                    ul{
                        visibility: visible !important;
                        height: auto !important;
                        display: block !important;
                        max-height: unset;
                        overflow: unset;
                        @media screen and (max-width: 991px){
                            position: relative;
                            li{
                                display: list-item;
                            }
                        }
                    }
                }
            }
            li[data-sf-field-input-type=select]{
                label{
                    width: 100%;
                }
                select{
                    width: 100%;
                    border-radius: 0;
                    border: 1px solid #636363;
                }
            }
            /*
            li.sf-field-taxonomy-targets{
                ul li label{
                    font-size: 11px;
                    &::after{
                        width: 10px !important;
                        height: 10px !important;
                    }
                }
            }
            */
            li.sf-field-reset{
                width: 100%;
                margin-top: 10px;
                a{
                    font-weight: 700;
                    color: #000000;
                    position: relative;
                    text-decoration: none;
                    &::after{
                        content: '';
                        width: 15px;
                        height: 15px;
                        margin-left: 8px;
                        display: inline-block;
                        vertical-align: middle;
                        background-image: url('../images/close-icon.svg');
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                    }
                }
            }
            li:nth-child(4){
                margin-right: 0px;
            }
        }
        .spinner-warp{
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1;
            background: rgba(255,255,255,0.5);
            .spinner{
                width:40px;
                height:40px;
                position: absolute;
                top: 50%; right: 50%;
                transform: translate(50%,-50%);
                .spinner-inner{
                    width:100%;
                    height:100%;
                    display:inline-block;
                    -webkit-animation:rs-revealer-6 1.4s linear infinite;
                    animation:rs-revealer-6 1.4s linear infinite;
                    span{
                        position:absolute;
                        vertical-align:top;
                        border-radius:100%;
                        display:inline-block;
                        width:8px;
                        height:8px;
                        margin-left:16px;
                        transform-origin:center 20px;
                        -webkit-transform-origin:center 20px;
                        background:#77c2a1;
                        &:nth-child(2){transform:rotate(36deg);-webkit-transform:rotate(36deg);opacity:.1}
                        &:nth-child(3){transform:rotate(72deg);-webkit-transform:rotate(72deg);opacity:.2}
                        &:nth-child(4){transform:rotate(108deg);-webkit-transform:rotate(108deg);opacity:.3}
                        &:nth-child(5){transform:rotate(144deg);-webkit-transform:rotate(144deg);opacity:.4}
                        &:nth-child(6){transform:rotate(180deg);-webkit-transform:rotate(180deg);opacity:.5}
                        &:nth-child(7){transform:rotate(216deg);-webkit-transform:rotate(216deg);opacity:.6}
                        &:nth-child(8){transform:rotate(252deg);-webkit-transform:rotate(252deg);opacity:.7}
                        &:nth-child(9){transform:rotate(288deg);-webkit-transform:rotate(288deg);opacity:.8}
                        &:nth-child(10){transform:rotate(324deg);-webkit-transform:rotate(324deg);opacity:.9}
                    }
                }
                @keyframes rs-revealer-6{from{transform:rotate(0)}to{transform:rotate(360deg)}}
                @-webkit-keyframes rs-revealer-6{from{-webkit-transform:rotate(0)}to{-webkit-transform:rotate(360deg)}}
            }
        }
    }
}

.resource-library-result-count{
    font-size: 32px;
    line-height: 42px;
    font-weight: bold;
    margin-bottom: 36px;
    span{
        font-size: 18px;
        line-height: 28px;
        color: #ADDAC7;
        margin-left: 28px;
        vertical-align: middle;
        display: inline-block;
        position: relative;
        &::before,
        &::after{
            content: '';
            width: 5px;
            height: 20px;   
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            top: 50%;
			transform: translateY(-50%);
        }
        &::before{
            background-image: url('../images/resource-library-search-left.svg');
            left: -7px;
        }
        &::after{
            background-image: url('../images/resource-library-search-right.svg');
            right: -7px;
        }
    }
}

.resource-library-result{
    .button-area{
        padding-top: 80px;
        text-align: center;
    }
}

.resource-library-list{
    padding-top: 39px;
    padding-bottom: 42px;
    border-bottom: 1px solid #878787;
    display: none;
    margin-top: 30px;
    opacity: 0;
    @include transition-property(all);
    @include transition-duration(0.2s);
    @include transition-timing-function(ease-in-out);
    &.is-visible{
        display: flex;
    }
    &.is-open{
        margin-top: 0;
        opacity: 1;
    }
    &:first-child{
        border-top: 1px solid #878787;
    }
    &:hover{
        background-color: #E4F3EC;
    }
    .thumb{
        max-width: 140px;
        text-align: center;
        @media screen and (max-width: 767px){
            margin-bottom: 30px;
        }
        .text-after-thumb{
            font-size: 11px;
            padding: 0px 10px;
            @media screen and (max-width: 1199px){
                padding: 0px;
            }
            @media screen and (max-width: 767px){
                font-size: 14px;
            }
        }
    }
    .content{
        @media screen and (max-width: 767px){
            padding-left: 0;
            padding-right: 0;
        }
        .title{
            font-weight: bold;
            color: #77C2A1;
            line-height: 28px;
        }
        .text-after-title{
            font-style: italic;
            line-height: 26px;
        }
        .short-description{
            margin-top: 30px;
            margin-bottom: 24px;
            padding-right: 95px;
            line-height: 26px;
            @media screen and (max-width: 1199px){
                padding-right: 0px;
            }
            .more-link,
            .less-link{
                display: block;
                text-decoration: none;
            }
            .more-link{
                margin-top: 1rem;
            }
        }
        .action{
            display: flex;
            > div{
                margin-right: 66px;
                &:last-child{
                    margin-right: 0px;
                }
            }
            a{
                color: #000000;
                text-decoration: none;
                display: inline-block;
                vertical-align: middle;
            }
            .view{
                a{
                    &::before{
                        content: '';
                        width: 32px;
                        height: 32px;
                        display: inline-block;
                        vertical-align: middle;
                        margin-right: 13px;
                        background-image: url('../images/preview-icon.svg');
                        background-repeat: no-repeat;
                        background-size: contain;
                    }
                    &:hover{
                        color: #77C2A1;
                        -webkit-text-fill-color: #77C2A1;
                        -webkit-text-stroke-width: 1px;
                        -webkit-text-stroke-color: #77C2A1;
                        &::before{
                            background-image: url('../images/preview-icon-hover.svg');
                        }
                    }
                }
            }
            .download{
                a{
                    &::before{
                        content: '';
                        width: 32px;
                        height: 32px;
                        display: inline-block;
                        vertical-align: middle;
                        margin-right: 13px;
                        background-image: url('../images/dw-icon.svg');
                        background-repeat: no-repeat;
                        background-size: contain;
                    }
                    &:hover{
                        color: #77C2A1;
                        -webkit-text-fill-color: #77C2A1;
                        -webkit-text-stroke-width: 1px;
                        -webkit-text-stroke-color: #77C2A1;
                        &::before{
                            background-image: url('../images/dw-icon-hover.svg');
                        }
                    }
                }
            }
            .external-url{
                a{
                    &::before{
                        content: '';
                        width: 32px;
                        height: 32px;
                        display: inline-block;
                        vertical-align: middle;
                        margin-right: 13px;
                        background-image: url('../images/arrow-right-black.svg');
                        background-repeat: no-repeat;
                        background-size: contain;
                    }
                    &:hover{
                        color: #77C2A1;
                        -webkit-text-fill-color: #77C2A1;
                        -webkit-text-stroke-width: 1px;
                        -webkit-text-stroke-color: #77C2A1;
                        &::before{
                            background-image: url('../images/arrow-right-green.svg');
                        }
                    }
                }
            }
        }
    }
}