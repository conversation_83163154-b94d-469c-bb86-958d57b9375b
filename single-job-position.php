<?php
get_header();
?>
	<div class="container single-jobpage">
		<?php while ( have_posts() ) : the_post(); ?>

			<div class="gobacktoblog">
                <div class="bk-active">
                    <?php echo '<a href="'.home_url('career').'" class="btn-back">'.__('Back', 'radar_fd_child').'</a>';?>
                </div>
            </div>

			
			<div class="row toppage last">
				<div class="col-12 header-wrapper">
					<h3><?php _e('Open Position', 'radar_child_fd'); ?></h3>
				</div>
			</div>

			<!-- Job title & Description -->
			<div class="row postcontent">
				<div class="col-xs-12 col-md-6 col-lg-3">
					<h4 class='section-headline pd-left-col'><?php _e('Job <br> Description', 'radar_child_fd'); ?></h4>
				</div>
				<div class="col-xs-12 col-md-6 col-lg-6">
					<div id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
						<h2 class="entry-title">
							<?php if(get_field('job_title')): ?>
                        		<?php echo get_field('job_title') ?>,
                    		<?php else: ?>
                        		<?php the_title(); ?>,
                    		<?php endif; ?>
							<?php if(get_field('city_country')): ?>
								<br><span><?php echo get_field('city_country') ?></span>
							<?php endif; ?>
						</h2>
					</div>	

					<div class="entry-content">
						<?php if(get_field('job_description')): ?>
							<?php echo get_field('job_description') ?>
						<?php endif; ?>
					</div>
				</div>
			</div>


			<!-- Job Responsibilities  -->
			<?php if(get_field('job_responsibilities')): ?>
			<div class="row postcontent job-response">
				<div class="col-xs-12 col-md-6 col-lg-3">
					<h4 class='section-headline pd-left-col'><?php _e('Job <br> Responsibilities', 'radar_child_fd'); ?></h4>
				</div>
				<div class="col-xs-12 col-md-6 col-lg-6">
					<div class="entry-content">
							<?php echo get_field('job_responsibilities') ?>
					</div>
				</div>
			</div>
			<?php endif; ?>


			<!-- Job Qualifications  -->
			<?php if(get_field('qualifications')): ?>
			<div class="row postcontent job-response">
				<div class="col-xs-12 col-md-6 col-lg-3">
					<h4 class='section-headline pd-left-col'><?php _e('Qualifications', 'radar_child_fd'); ?></h4>
				</div>
				<div class="col-xs-12 col-md-6 col-lg-6">
					<div class="entry-content">
							<?php echo get_field('qualifications') ?>
					</div>
				</div>
			</div>
			<?php endif; ?>

			<!-- Job Qualifications  -->
			<?php if(get_field('text_before_download_section')): ?>
			<div class="row postcontent job-response">
				<div class="col-xs-12 col-md-6 col-lg-3">
				</div>
				<div class="col-xs-12 col-md-6 col-lg-6">
					<div class="entry-content">
							<?php echo get_field('text_before_download_section') ?>
					</div>
				</div>
			</div>
			<?php endif; ?>


			<!-- Job Download -->
			<div class="row jobdownload">
				<div class="col-xs-12 col-md-6 col-lg-3">
					<?php /*<h4 class="section-headline pd-left-col"><?php _e('Downloads', 'radar_child_fd'); ?></h4>*/ ?>
				</div>
				<div class="col-xs-12 col-md-6 col-lg-8">
					
					<div class="download-content">
						<?php
						$download = get_field('download_file');
							if($download){
								foreach ($download as $download_val) {
									?>
									<div class="download-list">

											<?php if($download_val['file_download']['url']){
												//echo 'url';
												echo "<div class='download-item'><a href='".$download_val['file_download']['url'] ."'><span>".$download_val['file_name']."</span></a></div>";
											}
											else{
												//echo 'no url';
												echo "<div class='download-item'><div class='no-file'><span>".$download_val['file_name']."</span></div></div>";
											}
											?>
											<?php /*<a href="<?php echo $download_val['file_download']['url']; ?>">
												<span><?php echo $download_val['file_name']; ?></span>
											</a>*/?>

									</div>
									<?php 
								}
							}
						?>
					</div>
				</div>
			</div>

			
            <?php get_template_part('contents/content-job'); ?>
        
		<?php endwhile; ?>

	</div>
<?php get_footer(); ?>

