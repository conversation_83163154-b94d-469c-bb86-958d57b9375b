jQuery(function($) {
    
    // // Initialize Slick Slider for two-cards-row
    // initTwoCardSlickSlider();

    // // Reinitialize on window resize
    // var resizeTimer;
    // $(window).resize(function() {
    //     clearTimeout(resizeTimer);
    //     resizeTimer = setTimeout(function() {
    //         initTwoCardSlickSlider();
    //     }, 500);
    // });

    $('.two-cards-row').slick({
        slidesToShow: 2,
        slidesToScroll: 1,
        infinite: true,
            arrows: true,
            dots: true,
            autoplay: true,
            autoplaySpeed: 4500,
            speed: 1500,
            pauseOnHover: true,
        responsive: [{
            breakpoint: 991,
            settings: {
                slidesToShow: 1,
            }

        }, {
            breakpoint: 480,
            settings: {
                slidesToShow: 1,
            }
            // settings: "unslick" // destroys slick
        }],
        prevArrow: '<button class="slick-prev custom-arrow custom-arrow-prev"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>',
            nextArrow: '<button class="slick-next custom-arrow custom-arrow-next"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>'
    })

});

// // Initialize after AJAX content loads (search filters)
// jQuery(document).on("sf:ajaxfinish", ".searchandfilter", function(){
//     jQuery('*[id*=search-filter-results-695]:visible').each(function() {
//         setTimeout(function() {
//             initTwoCardSlickSlider();
//         }, 100);
//     });
// });

// function initTwoCardSlickSlider() {
//     var $slider = jQuery('.two-cards-row');
    
//     // Check if element exists
//     if ($slider.length === 0) {
//         return;
//     }

//     // Destroy existing slider if it exists
//     if ($slider.hasClass('slick-initialized')) {
//         $slider.slick('unslick');
//     }

//     // Get window width for responsive behavior
//     var windowWidth = jQuery(window).width();

//     // Configure Slick Slider based on screen size
//     if (windowWidth < 600) {
//         // Mobile configuration
//         $slider.slick({
//             slidesToShow: 1,
//             slidesToScroll: 1,
//             autoplay: true,
//             adaptiveHeight: false,
//             prevArrow: '<button class="slick-prev custom-arrow custom-arrow-prev"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>',
//             nextArrow: '<button class="slick-next custom-arrow custom-arrow-next"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>'
//         });
//     } else if (windowWidth < 992) {
//         // Tablet configuration
//         $slider.slick({
//             slidesToShow: 1,
//             slidesToScroll: 1,
//             infinite: true,
//             arrows: true,
//             dots: true,
//             autoplay: false,
//             adaptiveHeight: true,
//           prevArrow: '<button class="slick-prev custom-arrow custom-arrow-prev"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>',
//             nextArrow: '<button class="slick-next custom-arrow custom-arrow-next"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>'
//         });
//     } else {
//         // Desktop configuration
//         $slider.slick({
//             slidesToShow: 2,
//             slidesToScroll: 1,
//             infinite: true,
//             arrows: true,
//             dots: true, // Hide dots on desktop as per your design
//             autoplay: true,
//             autoplaySpeed: 4500,
//             speed: 1500,
//             pauseOnHover: true,
//            prevArrow: '<button class="slick-prev custom-arrow custom-arrow-prev"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>',
//             nextArrow: '<button class="slick-next custom-arrow custom-arrow-next"><img src="https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg"></span></button>'
//         });
//     }
// }