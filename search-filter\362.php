<?php
/**
 * Search & Filter Pro 
 *
 * Sample Results Template
 * 
 * @package   Search_Filter
 * <AUTHOR>
 * @link      https://searchandfilter.com
 * @copyright 2018 Search & Filter
 * 
 * Note: these templates are not full page templates, rather 
 * just an encaspulation of the your results loop which should
 * be inserted in to other pages by using a shortcode - think 
 * of it as a template part
 * 
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs 
 * and using template tags - 
 * 
 * http://codex.wordpress.org/Template_Tags
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $query->have_posts() )
{
	wp_enqueue_script('search-filter');
	?>
	<div class="loop-grid partners-items row hide-element-mobile">
	<?php
	while ($query->have_posts())
	{
		$query->the_post();
		?>
		<div class="col-12 col-sm-6 col-md-3 partners-item item">
			<div class="card">
                <?php
                $logo = get_field('logo');
                $external_url = get_field('external_url');
                if($external_url){
                    $href = $external_url;
                    $target = 'target="_blank"';
                }else{
                    $href = get_the_permalink();
                    $target = '';
                }
                ?>
				<div class="card-photo">
                    <a href="<?php echo $href; ?>" <?php echo $target; ?>><img src="<?=$logo['url']?>" /></a>
				</div>
				<div class="card-body">
					<?php if(get_field('company_name')): ?>
                        <a href="<?php echo $href; ?>" <?php echo $target; ?> class="text-link"><h3><?php echo get_field('company_name') ?></h3></a>
                    <?php else: ?>
                        <a href="<?php echo $href; ?>" <?php echo $target; ?> class="text-link"><h3><?php the_title(); ?></h3></a>
                    <?php endif; ?>
				</div>
			</div>
		</div>
		<?php
	}
	?>
    <div class="button-area text-center mt-5 d-md-none w-100"></div>
	</div>

	
	<?php
}
else
{
	//echo "No Results Found";
	echo "<div class='no-results-text'>";
	_e('Ingen resultater', 'radar_fd_child');
	echo "</div>";
}
?>