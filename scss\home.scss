.sub-segment {
  .sow-image-container{
    width: 100%;
    overflow: hidden;
    @media screen and (max-width: 810px){
      max-width: unset;
      max-height: unset;
    }
    .so-widget-image{
      width: auto;
      object-fit: cover;
      object-position: top center;
      border-radius: 0;
      transition: transform .5s ease;
    }
  }
  .segment-body{
    padding: 45px;
    color: #000;
    background-color: #FFF;
    transition: background-color 0.3s ease;
    h3{
      color: #000;
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 15px;
    }
    p {
      font-size: 14px;
      line-height: 1.6;
    }
    a.arrow-link{
      font-size: 0;
      background: transparent;
      border: 0;
      &:after{
        content: '';
        display: inline-flex;
        margin-left: 0;
        width: 36px;
        height: 36px;
        background: transparent;
        background-image: url(../images/arrow-right-white.svg);
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        transition: background-image 0.3s linear;
      }
    }
  }
  @media screen and (max-width: 600px){
    .segment-body{
      padding: 22px 19px 26px 19px;
      background-color: #F2F2F2;
      h3{
        font-size: 18px;
        font-weight: 700 !important;
        margin-bottom: 10px;
      }
      p{
        font-size: 11px;
      }
      .arrow-link{
        background: transparent !important;
        border: 0;

        &::after {
          background-image: url(../images/arrow-right-green.svg) !important;
        }
      }
    }
  }
  &:hover{
    .sow-image-container{
      .so-widget-image{
        transform: scale(1.05);
      }
    }
    .segment-body{
      background-color: #F2F2F2;
      h3{
        font-weight: 700 !important;
      }
      .arrow-link{
        background: transparent !important;
        border: 0;
        &::after {
          background-image: url(../images/arrow-right-green.svg);
        }
      }
    }
  }
}
.img-background{
  position: relative;
  .sow-image-container{
    img{
      width: auto;
    }
  }
  &.parallax-image{
    overflow: hidden;
    .sow-image-container{
      img{
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
        @media screen and (max-width: 625px){
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
  }
}