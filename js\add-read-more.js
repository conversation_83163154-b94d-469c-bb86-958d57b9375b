function AddReadMore() {
    //This limit you can set after how much characters you want to show Read More.
    //var carLmt = 280;
    var carLmt = 0;
    // Text to show when text is collapsed
    var readMoreTxt = readmore.readmore_text;
    // Text to show when text is expanded
    var readLessTxt = readmore.readless_text;


    //Traverse all selectors with this class and manupulate HTML part to show Read More
    jQuery(".addreadmore").each(function() {
        if (jQuery(this).find(".firstSec").length)
            return;

        var allstr = jQuery(this).text();
        if (allstr.length > carLmt) {
            var firstSet = allstr.substring(0, carLmt);
            var secdHalf = allstr.substring(carLmt, allstr.length);
            var strtoadd = firstSet + "<div class='secsec'>" + secdHalf + "</div><button class='readmore'>" + readMoreTxt + "</button><button class='readless'>" + readLessTxt + "</button>";
            jQuery(this).html(strtoadd);
        }

    });
    //Read More and Read Less Click Event binding
    jQuery(document).on("click", ".readmore,.readless", function() {
        jQuery(this).closest(".addreadmore").toggleClass("showlesscontent showmorecontent");
    });
}

jQuery(function($){
    //Calling function after Page Load
    AddReadMore();
});