<?php
/**
 * Search & Filter Pro 
 *
 * Sample Results Template
 * 
 * @package   Search_Filter
 * <AUTHOR>
 * @link      https://searchandfilter.com
 * @copyright 2018 Search & Filter
 * 
 * Note: these templates are not full page templates, rather 
 * just an encaspulation of the your results loop which should
 * be inserted in to other pages by using a shortcode - think 
 * of it as a template part
 * 
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs 
 * and using template tags - 
 * 
 * http://codex.wordpress.org/Template_Tags
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/*
$sf_id = $query->query['search_filter_id'];
echo $sf_id;
*/

$post_type = $query->query['post_type'];
if($post_type == 'resource_library' || $post_type == 'target_database'){
	?>
		<div class="resource-library-result-count">
			<?php _e('Search results', 'radar_child_fd'); ?><span class="count"><?php echo $query->found_posts; ?></span>
		</div>
		<div class="resource-library-result">
			<?php
			include( get_stylesheet_directory() . '/loops/loop-resource-library.php' );
			?>
		</div>
	<?php
}elseif($post_type == 'partners'){
	include( get_stylesheet_directory() . '/search-filter/362.php' );
}elseif($post_type == 'news'){
	include( get_stylesheet_directory() . '/search-filter/695.php' );
}elseif($post_type == 'team_member'){
	include( get_stylesheet_directory() . '/search-filter/461.php' );
}elseif($post_type == 'distributor'){
	include( get_stylesheet_directory() . '/search-filter/472.php' );
}elseif($post_type == 'distributor'){
	include( get_stylesheet_directory() . '/search-filter/472.php' );
}


/*
if($post_type == 'products'){
	$num_cal = 4;
}else{
	$num_cal = 4;
}

if ( $query->have_posts() )
{
	?>
	<div class="post-list post-list-<?php echo $post_type; ?> row row-cols-1 row-cols-md-2 row-cols-lg-<?php echo $num_cal; ?>">
		<?php
		while ($query->have_posts())
		{
			$query->the_post();
			if($post_type == 'products'){
				include( get_stylesheet_directory() . '/loops/loop-template/product.php' );
			}else{
				include( get_stylesheet_directory() . '/loops/loop-template/gride.php' );
			}
		}
		?>
	</div>
	<?php
}
else
{
	// echo '<div class="text-center">'.__('No Results Found', 'radar_child_fd').'</div>';
}
*/
?>