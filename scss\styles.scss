@import "compass";

html,body{
	margin:0;
	padding:0;
}
body{
	//font-family: 'Poppins', sans-serif;
	font-family: 'Century Gothic', sans-serif;
	letter-spacing: 0.1px;
	color: #000000;
	padding-top: 80px;
	@media screen and (max-width: 991px) {
		//padding-top: 0px !important;
	}
	&.active-navbar{
		width: 100%;
	}
}

@media (min-width: 1200px){
	.container, .container-sm, .container-md, .container-lg, .container-xl {
		max-width: 1260px;
	}
}

a,
button{
	outline: 0 !important;

	&:hover,
	&:visited,
	&:active{
		outline: 0 !important;
	}
}

.form-control:focus{
	box-shadow: 0px 0px 0px;
}

.site-header{
	width: 100%;
	height: auto;
	position:fixed;
	z-index:9999999;
	opacity: 1;
	top: 0;
	@include transition-property(all);
	@include transition-duration(0.5s);
	@include transition-timing-function(ease);
	@media screen and (max-width: 991px) {
		margin-top: -1px;
		height: auto !important;
		min-height: unset;
		background-color: #ffffff;
		box-shadow: 0px 1px 3px 2px rgba(0, 0, 0, 0.06);
		&.hide{
			background: unset;
			box-shadow: unset;
		}
	}
	.site-header-bg{
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		background-color: #ffffff;
		box-shadow: 0px 1px 3px 2px rgba(0, 0, 0, 0.06);
		@include transition-property(all);
		@include transition-duration(0.5s);
		@include transition-timing-function(ease);
		@media screen and (max-width: 991px) {
			display: none;
		}
	}
	&.fixed {
		position: fixed;
		width: 100%;
		top: 0;
		opacity: 1;
		z-index: 999999;
	}
	&.hide{
		height: 0;
		overflow: hidden;
		visibility: hidden;
		.site-header-bg{
			height: 0px;
		}
		.navbar{
			opacity: 0;
		}
		.childTabMenu{
			opacity: 0;
		}
		.productTabMenu{
			opacity: 0;
		}
	}
	.header-logo{
		a{
			display: inline-block;
		}
		img{
			max-width: 100%;
			max-height: 43px;
		}
	}
	.navbar{
		padding: 15px 0px 0px;

		opacity: 1;
		@include transition-property(all);
		@include transition-duration(0.5s);
		@include transition-timing-function(ease);
		@media screen and (max-width: 991px){
			padding: 11px 0px;

		}
		#primary-menu{
			.search-form{
				.form-group{
					width: 100%;
					input[type="search"],
					input[type="submit"]{
						height: 40px;
						line-height: 40px;
						font-size: 14px;
						width: auto;
						display: inline-block;
						vertical-align: middle;
						color: #000000;
						border: 1px solid #878787;
						outline: none;
						@include border-radius(0px, 0px);
						&::-webkit-input-placeholder { /* Edge */
							color: #CFCFCF;
						}
						&:-ms-input-placeholder { /* Internet Explorer 10-11 */
							color: #CFCFCF;
						}
						&::placeholder {
							color: #CFCFCF;
						}
						&:focus{
							border: 2px solid #77C2A1;
						}
					}
					input[type="submit"]{
						min-width: 130px;
						height: 40px;
						line-height: 40px;
						font-size: 14px;
						font-weight: 700;
						color: #ffffff;
						padding-top: 0px;
						padding-bottom: 0px;
						background: #77C2A1;
						border: 2px solid #77C2A1;
						&:hover{
							color: #77C2A1;
							background: #ffffff;
							border: 2px solid #77C2A1;
						}
					}
				}
			}
		}
	}
	.navbar-brand{
		padding: 0px;
		display: inline-block;
		img{
			max-width: 100%;
			max-height: 43px;
			@media screen and (max-width: 991px){
				height: 27px;
			}
		}
	}
	.top-bar{
		.sub-menu{
			height:100%;
		}
	}

	.navbar-desktop{
		width: 100%;
		text-align: right;
	}

	#top-menu{
		margin-bottom: 10px;
	}

	ul.top-menu {
		margin: 0;
		padding: 0;
		line-height: 100%;
		li {
			list-style: none;
		}
		> li {
			position: relative;
			&:last-child{
				a{
					margin-right: 0px;
					&::before{
						display: none;
					}
				}
			}
		}
		> li {
			> a {
				font-size: 11px;
				line-height: 100%;
				color: #878787;
				margin-right: 25px;

				display:inline-block;

				&::before{
					display: block;
					content: attr(data-text);
					font-weight: bold;
					height: 0;
					overflow: hidden;
					visibility: hidden;
					letter-spacing: 1px;
				}
			}
		}
		> li:hover > a,
		> li.current_page_item > a,
		> li.current_page_parent > a,
		> li.current-page-ancestor > a,
		> li.current-menu-ancestor > a,
		> li.current-cat > a,
		> ul > li.current-menu-item > a {
			text-decoration: none;
			@include transition-property(all);
			@include transition-duration(0.2s);
			@include transition-timing-function(ease-in-out);
			color: #77C2A1;
			font-weight: bold;
			/*
			-webkit-text-fill-color: #77C2A1;
			-webkit-text-stroke-width: 1px;
			-webkit-text-stroke-color: #77C2A1;
			*/
		}

		> li.menu-item-has-children{
			> a{
				&::after{
					content: '';
					width: 11px;
					height: 6px;
					display: inline-block;
					vertical-align: top;
					margin-top: 4px;
					margin-left: 3px;
					border: 0px;
					background: url('../images/language-switch-arrow-down.svg') no-repeat center center;
					@include transition-property(all);
					@include transition-duration(0.2s);
					@include transition-timing-function(ease-in-out);
				}
			}
			&:hover{
				> a{
					&::after{
						transform: rotate(180deg);
						filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%);
					}
				}
			}
		}

		ul.sub-menu{
			display: none;
			position: absolute;
		}
		> li:hover ul.sub-menu{
			display: block;
		}
		ul.dropdown-menu,
		ul.sub-menu {
			margin: 0;
			padding: 0;
			left: -10px;
			z-index: 1000;
			border: 0px;
			background-color: #ffffff;
			@include border-radius(0px, 0px);
			@include transition-property(all);
			@include transition-duration(0.2s);
			@include transition-timing-function(ease-in-out);

			li {
				list-style: none;
				min-width: 100px;
				position: relative;
			}
			li a {
				font-size: 11px;
				line-height: 100%;
				color: #878787;
				text-transform: uppercase;
				text-align: left;
				display: block;
				padding: 10px 10px;
				border-bottom: 0px;
				@include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
			}
			li:last-child a{

			}
			li a:hover,
			li.current-menu-item a {
				text-decoration: none;
				color: #77C2A1;
				-webkit-text-fill-color: #77C2A1;
				-webkit-text-stroke-width: 1px;
				-webkit-text-stroke-color: #77C2A1;
			}
		}

		li.wpml-ls-item.menu-item-has-children{
			padding-bottom: 15px;
			margin-bottom: -15px;
			ul.sub-menu{
				max-width: 43px;
				margin-top: 14px;
				background: #FFFFFF;
				box-shadow: 1px 0px 3px rgba(0, 0, 0, 0.1);
				li{
					min-width: unset;
					border-bottom: 1px solid #E4F3EC;
					&:last-child{
						border-bottom: 0px;
					}
					a{
						font-size: 11px;
						line-height: 16px;
						padding: 7px 10px;
						display:inline-block;

						&::before{
							display: block;
							content: attr(data-text);
							font-weight: bold;
							height: 0;
							overflow: hidden;
							visibility: hidden;
							letter-spacing: 1px;
						}
						&:hover{
							color: #77C2A1;
							font-weight: bold;
							-webkit-text-stroke-width: 0px;
						}
					}
				}
			}
		}
	}
	.mega-menu-wrap{
		display: flex !important;
		justify-content: flex-end;
	}

	ul.max-mega-menu {
		margin: 0;
		padding: 0;
		li {
			list-style: none;
		}
		> li {
			position: relative;
			&:last-child{
				margin-right: 0px !important;
			}
		}
		> li {
			margin-right: 68px !important;
			> a {
				font-size: 14px;
				line-height: 100%;
				color: #000000;
				position: relative;
				padding: 0px 0px;
				display: inline-block;
				position: relative;
				&::before{
					display: block;
					content: attr(data-text);
					font-weight: bold;
					height: 0;
					overflow: hidden;
					visibility: hidden;
					letter-spacing: 0.2px;
				}
			}
			&::before,
			&::after{
				content: '';
				width: 6px;
				height: 18px;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				opacity: 0;
				@include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
			}
			&:hover{
				> a{
					.link-title{
						color: #77C2A1;
						font-weight: bold;
						&::before{
							display: inline-block !important;
							width: 6px;
							height: 18px;
							position: absolute;
							content: '';
							background: url(../images/menu-hover-left.svg) no-repeat left center;
							left: -5px;
							top: 11px;
						}
						&::after{
							display: inline-block !important;
							width: 6px;
							height: 18px;
							position: absolute;
							content: '';
							background: url('../images/menu-hover-right.svg') no-repeat right center;
							right: -10px;
							top: 11px;
						}
					}
				}
			}
			.mega-sub-menu{
				margin: 0 -5px !important;
				width: calc(100% + 10px) !important;
				padding: 10px 10px !important;
				.mega-menu-link{
					line-height: 1.2 !important;
				}
			}

		}
		> li:hover,
		> li.current_page_item,
		> li.current_page_parent,
		> li.current-page-ancestor,
		> li.current-menu-ancestor,
		> li.current-cat,
		> ul > li.current-menu-item{
			> a{
				text-decoration: none;
				@include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
				color: #77C2A1;
				font-weight: bold;
			}
			&::before,
			&::after{
				opacity: 1;
			}
		}

		ul.sub-menu{
			display: none;
			position: absolute;
		}
		> li:hover ul.sub-menu{
			display: block;
		}

		ul.dropdown-menu,
		ul.sub-menu {
			min-width: 300px;
			margin: 0;
			padding: 0;
			margin-left: 1px;
			z-index: 1000;
			border: 0px;
			@include border-radius(0px, 0px);
			@include transition-property(all);
			@include transition-duration(0.2s);
			@include transition-timing-function(ease-in-out);

			li {
				list-style: none;
				min-width: 260px;
				position: relative;
			}
			li a {
				text-transform: uppercase;
				text-align: left;
				display: block;
				padding: 10px 20px;
				border-bottom: 0px;
				@include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
			}
			li:last-child a{

			}
			li a:hover,
			li.current-menu-item a {
				text-decoration: none;
			}
		}
	}

	.header-search{
		line-height: 100%;
		position: relative;
		.search-icon{
			font-size: 12px;
			line-height: 100%;
    		display: inline-block;
    		vertical-align: top;
			margin-left: 15px;
			color: #000000;
			&:not(.collapsed),
			&:hover,
			&:active{
				filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%);
			}
		}
		.header-search-form{
			width: 210px;
			position: absolute;
			bottom: -40px;
			right: 0px;
			background-color: #ffffff;
			border: 1px solid #77c2a1;
			z-index: 1;
			form{
				padding: 0px;
			}
			input[type="submit"]{
				display: none;
			}
			input[type="search"]{
				height: 30px;
				border: 0px;
				font-size: 14px;
				@include border-radius(0px, 0px);
				// ::-webkit-input-placeholder { /* Edge */
				// 	color: red;
				// }
				// :-ms-input-placeholder { /* Internet Explorer 10-11 */
				// 	color: red;
				// }
				// ::placeholder {
				// 	color: red;
				// }
			}
		}
	}
}

.site-content{
	position:relative;
	overflow:hidden;
	font-size: 14px;
	line-height: 1.6;
	.container{
		@media screen and (max-width: 770px) {
			padding: 0 20px;
		}
	}
	.entry-title{

	}
	h1{
		font-size: 64px;
		line-height: 70px;
		/*line-height: 1.2 !important;*/
		/*color: #000;*/
	}
	h2{
		font-size: 40px;
		line-height: 48px;
		/*line-height: 1.2 !important;*/
		/*color: #000;*/
	}
	h3{
		font-size: 32px;
		line-height: 42px;
		/*line-height: 1.2 !important;*/
		/*color: #000;*/
	}
	h4{
		font-size: 18px;
		line-height: 28px;
		/*line-height: 1.4 !important;*/
		/*color: #000;*/
	}
	h5{
		font-size: 14px;
		line-height: 26px;
		/*line-height: 2 !important;*/
		/*color: #000;*/
	}
	h6{
		font-size: 14px;
		line-height: 26px;
		/*line-height: 2 !important;*/
		/*color: #000;*/
	}
	p{
		
	}
	a{
		color: #77C2A1;
		text-decoration: underline;
		&:visited{
			color: #77C2A1;
		}
		&:hover,
		&:active{
			color: #ADDAC7;
		}
		img{
			&.alignright{
				float: right;
				margin: 5px 0 20px 20px;
			}
			&.alignnone{
				margin: 5px 20px 20px 0;
			}
			&.alignleft{
				float: left;
				margin: 5px 20px 20px 0;
			}
			&.aligncenter{
				display: block;
				margin-left: auto;
				margin-right: auto;
			}
		}
	}
	@media screen and (max-width: 991.9px) {
		h1{
			font-size: 32px;
			line-height: 42px;
		}
		h2{
			font-size: 28px;
			line-height: 36px;
		}
		h3{
			font-size: 24px;
			line-height: 34px;
		}
		h4{
			font-size: 18px;
			line-height: 28px;
		}
	}
	@media screen and (max-width: 501px) {
		h1{
			font-size: 32px;
			line-height: 42px;
		}
		h2{
			font-size: 28px;
			line-height: 36px;
		}
		h3{
			font-size: 24px;
			line-height: 34px;
		}
		h4{
			font-size: 18px;
			line-height: 28px;
		}
	}
	.alignnone{

	}
	.aligncenter,
	div.aligncenter{
		display: block;
		margin: 5px auto 5px auto;
	}
	img{
		max-width: 100%;
		height: auto;
	}
	
	.textwidget,
	.job-response .entry-content,
	.news-singlepage .entry-content{
		ul{
			list-style: none;
			padding: 0px;
			li{
				line-height: 2;
				padding-left: 20px;
				position: relative;
			}
			li::before {
				content: '';
				width: 8px;
				height: 8px;
				position: absolute;
				top: 10px;
				left: 0;
				background: url('../images/li-bullet.svg');
			}
		}
	}

	.wp-caption{
		background: #fff;
		border: 1px solid #f0f0f0;
		max-width: 96%; /* Image does not overflow the content area */
		padding: 5px 3px 10px;
		text-align: center;

		&.alignnone{
			margin: 5px 20px 20px 0;
		}
		&.alignleft{
			margin: 5px 20px 20px 0;
		}
		&.alignright{
			margin: 5px 0 20px 20px;
		}
		img{
			border: 0 none;
			height: auto;
			margin: 0;
			max-width: 98.5%;
			padding: 0;
			width: auto;
		}
		p.wp-caption-text{
			font-size: 11px;
			line-height: 17px;
			margin: 0;
			padding: 0 4px 5px;
		}
	}
	.screen-reader-text{
		clip: rect(1px, 1px, 1px, 1px);
		position: absolute !important;
		white-space: nowrap;
		height: 1px;
		width: 1px;
		overflow: hidden;

		&:focus{
			background-color: #f1f1f1;
			border-radius: 3px;
			box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
			clip: auto !important;
			color: #21759b;
			display: block;
			font-size: 14px;
			font-size: 0.875rem;
			font-weight: bold;
			height: auto;
			left: 5px;
			line-height: normal;
			padding: 15px 23px 14px;
			text-decoration: none;
			top: 5px;
			width: auto;
			z-index: 100000; /* Above WP toolbar. */
		}
	}
	.gallery{
		margin: 0 auto 18px;

		.gallery-item {
			float: left;
			margin-top: 0;
			text-align: center;
			width: 33%;
		}
		img {
			box-shadow: 0px 0px 4px #999;
			border: 1px solid white;
			padding: 8px;
			background: #f2f2f2;

			&:hover {
				background: white;
			}
		}
		.gallery-caption{
			color: #888;
			font-size: 12px;
			margin: 0 0 12px;
		}
		dl, dt{
			margin: 0;
		}
		br+br{
			display: none;
		}
	}
	.gallery-columns-2{

		.gallery-item{
			width: 50%;
		}
		.attachment-medium {
			max-width: 92%;
			height: auto;
		}
	}
	.gallery-columns-4{

		.gallery-item{
			width: 25%;
		}
		.attachment-thumbnail {
			max-width: 84%;
			height: auto;
		}
	}

	.bg-left{
		position: relative;
		&::after{
			left: -999em;
			content: '';
			display: block;
			position: absolute;
			width: 999em;
			top: 0;
			bottom: 0;
			background-color: inherit;
			z-index: -1;
		}
		@media screen and (max-width: 991.9px) {
			padding-left: 2px;
			padding-right: 2px;
			&::after{
				width: calc(999em + 2px);
			}
			&::before{
				right: -999em;
				content: '';
				display: block;
				position: absolute;
				width: calc(999em + 2px);
				top: 0;
				bottom: 0;
				background-color: inherit;
			}
		}
	}

	.bg-right{
		position: relative;
		&::after{
			right: -999em;
			content: '';
			display: block;
			position: absolute;
			width: calc(999em + 2px);
			top: 0;
			bottom: 0;
			background-color: inherit;
			z-index: -1;
		}
		@media screen and (max-width: 991.9px) {
			padding-left: 2px;
			padding-right: 2px;
			&::before{
				left: -999em;
				content: '';
				display: block;
				position: absolute;
				width: calc(999em + 1px);
				top: 0;
				bottom: 0;
				background-color: inherit;
			}
			&::after{
				width: calc(999em + 2px);
			}
		}
	}

	.bg-left-bg-right{
		position: relative;
		@media screen and (max-width: 991.9px) {
			padding-left: 2px;
			padding-right: 2px;
		}
		&::before{
			left: -999em;
			content: '';
			display: block;
			position: absolute;
			width: 999em;
			top: 0;
			bottom: 0;
			background-color: inherit;
			z-index: -1;
			@media screen and (max-width: 991.9px) {
				width: calc(999em + 2px);
			}
		}
		&::after{
			right: -999em;
			content: '';
			display: block;
			position: absolute;
			width: 999em;
			top: 0;
			bottom: 0;
			background-color: inherit;
			z-index: -1;
			@media screen and (max-width: 991.9px) {
				width: calc(999em + 2px);
			}
		}
	}

	.all-font-bold{
		h1,h2,h3,h4,h5,h6,p{
			font-weight: 700 !important;
		}
	}

	.specifications-list{
		width: 810px;
		max-width: 100%;
		margin: 0 auto;
		.specification-item{
			padding: 10px 0px;
			border-bottom: 1px solid #878787;
			&:first-child{
				border-top: 1px solid #878787;
			}
			.label{
				font-weight: 700;
				word-break: break-word;
				padding-right: 15px !important;
			}
		}
	}

	.testimony-single{
		position: relative;
		.content-warp{
			width: 430px;
			max-width: 100%;
			font-size: 18px;
			font-weight: 700;
			margin: 0 auto;
			position: relative;
			&::before,
			&::after{
				content: '';
				width: 35px;
				height: 160px;
				position: absolute;
				top: 50%;
				 transform: translateY(-50%);
				background-repeat: no-repeat;
				background-position: center;
			}
			&::before{
				background-image: url('../images/testimony-single-left.svg');
				left: -65px;
			}
			&::after{
				background-image: url('../images/testimony-single-right.svg');
				right: -65px;
			}
			.content{
				&::before{
					content: '"';
					display: inline-block;
				}
				&::after{
					content: '"';
					display: inline-block;
				}
			}
		}
		.person{
			width: 300px;
			max-width: 100%;
			font-size: 11px;
			line-height: 150%;
			position: absolute;
			bottom: -25px;
			right: 0px;
			.name{
				font-weight: 700;
			}
			@media screen and (max-width: 1199px) {
				width: 150px;
			}
			@media screen and (max-width: 1199px) {
				width: 430px;
				max-width: 100%;
				position: relative;
				margin: 0 auto;
			}
		}
	}

	.front-page-category{
		ul{
			margin-bottom: 0px;
			li{
				list-style: none;
				padding-left: 0;
				line-height: 32px;
				&::before{
					display: none;
				}
				a{
					text-decoration: none;
				}
			}
		}
	}

	.sow-headline{
		a{
			text-decoration: none;
		}
	}

	.fix-sameline-text{
		margin-top: -4px;
	}

	.description,
	.content,
	.siteorigin-widget-tinymce,
	.entry-content,
	.characters-max-content{
		a{
			font-weight: bold;
		}
	}

}

.site-footer{
	position:relative;
	background-color: #1d418e;
	@media screen and (max-width: 991px){
		z-index: 2;
	}
	.container{
		padding-left: 20px;
		padding-right: 20px;
	}
	.footer-logo{
		margin-bottom: 36px;
	}
	.widget-title{
		font-size: 14px;
		font-weight: bold;
		line-height: 28px;
		color: #77C2A1;
		margin-bottom: 10px;
	}
	.widget{
		> div{
			padding-bottom: 30px;
		}
	}
	ul.menu{
		margin: 0;
		padding: 0;
		> li{
			list-style: none;
			&.wpml-ls-item{
				display: none;
			}
			a{
				line-height: 26px;
				margin-bottom: 10px;
				display: block;
				&:hover{
					text-decoration: none;
					-webkit-text-fill-color: #77C2A1;
					-webkit-text-stroke-width: 1px;
					-webkit-text-stroke-color: #77C2A1;
				}
			}
		}
		> ul{
			display: none;
		}
	}
	i{
		font-size: 1.3333333333333333rem;
		margin-right: 15px;
	}
	h1{

	}
	h2{

	}
	h3{

	}
	h4{

	}
	h5{

	}
	h6{

	}
	p{
		font-size: 14px;
		line-height: 26px;
		color: #ffffff;
	}
	a{
		font-size: 14px;
		color: #ffffff;
		&:hover,
		&:active{
			color: #77C2A1;
			text-decoration: none;
		}
	}
	.site-footer-info{
		padding-top: 100px;
		padding-bottom: 70px;
		padding-left: 100px;
		padding-right: 100px;
	}
	.footer-sitemap{
		@media (max-width: 781px){
			.panel-grid-cell{
				margin-bottom: 0px !important;
				&:last-child{
					.widget-title{
						display: none;
					}
				}
			}
			.widget>div{
				padding-bottom: 0px !important;
			}
		}
	}
	.footer-subscribe{
		padding-top: 50px;
		padding-bottom: 56px;
		border-top: 1px solid #ffffff;
		.widget{
			padding-left: 100px;
			padding-right: 100px;
		}
		.form-wrap{
			position: relative;
			@media (max-width: 992px){
				padding-left: 40px;
				padding-right: 40px;
				> div{
					margin-bottom: 20px;
					&:last-child{
						margin-bottom: 0px;
					}
				}
			}
			> div:first-child{
				margin-left: 40px;
				margin-right: 120px;
				@media (max-width: 1200px){
					margin-right: 0px;
				}
				@media (max-width: 992px){
					margin-left: 0px;
				}
			}
			> div:last-child{
				margin-right: 55px;
				@media (max-width: 1200px){
					margin-right: 40px;
				}
				@media (max-width: 992px){
					margin-right: 0px;
				}
			}
			> div:first-child::before,
			> div:last-child::after{
				content: '';
				width: 15px;
				height: 66px;
				position: absolute;
				bottom: -13px;
			}
			> div:first-child::before{
				background-image: url('../images/footer-subscribe-vector-left.svg');
				left: 0;
			}
			> div:last-child::after{
				background-image: url('../images/footer-subscribe-vector-right.svg');
				right: 0;
			}
		}
		h3{
			font-size: 18px;
			font-weight: 700;
			color: #77C2A1;
			margin-bottom: 0px;
		}
		p{
			margin-bottom: 0px;
		}
		label{
			font-size: 11px;
			font-weight: 700;
			color: #ffffff;
			margin-bottom: 4px;
		}
		input[type=text],
		input[type=email]{
			width: 210px;
			max-width: 100%;
			height: 40px;
			font-size: 14px;
			color: #ffffff;
			//color: #C4BFC0;
			padding: 0px 12px;
			background-color: transparent !important;
			border: 1px solid #FFFFFF;
			outline: none;
			border-radius: 0px;
			-webkit-appearance: none;
			@media (max-width: 992px){
				width: 100%;
			}
			&:focus{
				border: 3px solid #ADDAC7;
			}
			&::-webkit-input-placeholder { /* Edge */
				color: #C4BFC0;
				opacity: 1;
			}
			&:-ms-input-placeholder { /* Internet Explorer 10-11 */
				color: #C4BFC0;
				opacity: 1;
			}
			&::placeholder {
				color: #C4BFC0;
				opacity: 1;
			}
			&:-webkit-autofill,
			&:-webkit-autofill:hover,
			&:-webkit-autofill:focus,
			&:-webkit-autofill:active{
				-webkit-transition-delay: 999999999999s;
    			-webkit-transition: color 999999999999s ease-out, background-color 9999s ease-out;
				// -webkit-box-shadow: 0 0 0 30px #1d418e inset !important;
			}
			&[data-com-onepassword-filled="dark"] {
				background-color: #1d418e !important;
			}
		}
		input[type=submit]{
			width: 153px;
			max-width: 100%;
		}
		.mc4wp-response{
			margin-top: 20px;
			text-align: center;
		}
	}
	.site-footer-copyright{
		padding-top: 15px;
		padding-bottom: 15px;
		font-size: 11px;
		position: relative;
		border-top: 1px solid #ffffff;
		ul{
			margin: 0;
			padding: 0;
			padding-left: 100px;
    		padding-right: 100px;
			li{
				list-style: none;
				display: inline-block;
				margin-right: 46px;
			}
		}
		a{
			font-size: 11px;
			opacity: 0.5;
			&:hover{
				-webkit-text-fill-color: #77C2A1;
				-webkit-text-stroke-width: 1px;
				-webkit-text-stroke-color: #77C2A1;
				opacity: 1;
			}
		}
		p{
			margin-bottom: 0px;
		}
	}
}

.btn-primary{
	height: 40px;
	font-size: 14px;
	font-weight: 700;
	line-height: 100%;
	color: #FFFFFF;
	background-color: #77C2A1;
	border: 2px solid #77C2A1;
	border-radius: 0px;
	&:focus,&:active{
		color: #FFFFFF !important;
		background-color: #77C2A1 !important;
		border: 2px solid #77C2A1 !important;
		box-shadow: 0px 0px 0px !important;
		outline: unset !important;
	}
	&:disabled{
		background-color: #E4F3EC;
		border: 2px solid #E4F3EC;
		&:hover{
			color: #FFF;
			background-color: #E4F3EC;
			border: 2px solid #E4F3EC;
		}
	}
	&:hover{
		color: #77C2A1;
		background-color: transparent;
		border: 2px solid #77C2A1;
	}
}
.btn-secondary{
	height: 40px;
	font-size: 14px;
	font-weight: 700;
	line-height: 100%;
	color: #77C2A1;
	background-color: #ffffff;
	border: 2px solid #77C2A1;
	border-radius: 0px;
	&:hover{
		color: #ffffff;
		background-color: #77C2A1;
		border: 2px solid #77C2A1;
	}
}
.btn{
	&.see-all{
		font-size: 14px;
		color: #000;
		text-decoration: none;
		position: relative;
		padding: 0px;
		margin-top: 40px;
		transition: all 0.2s linear;
		&::before,
		&::after{
			content: '';
			width: 7px;
			height: 23px;
			transition: all 0.2s linear;
			display: inline-block;
			vertical-align: middle;
		}
		&:visited{
			color: #000 !important;
		}
		&::before{
			content: '';
			background: url('../images/menu-hover-left-black-thin.svg') no-repeat left center;
			margin-right: 5px;
		}
			&::after{
			background: url('../images/menu-hover-right-black-thin.svg') no-repeat right center;
			margin-left: 5px;
		}
		&:hover{
			color: #000 !important;
			-webkit-text-fill-color: #000;
			-webkit-text-stroke-width: 1px;
			-webkit-text-stroke-color: #000;
		&::before{
			content: '';
			background: url('../images/menu-hover-left-black.svg') no-repeat left center;
		}
		&::after{
			background: url('../images/menu-hover-right-black.svg') no-repeat right center;
		}
		}
	}
	&.remove-tag{
		a{
			&::before,&::after{
				opacity: 0;
			}
			&:hover{
				&::before,&::after{
				opacity: 1;
				}
			}
		}
	}
}

.cookie-notice-container{
	padding: 30px 0px;
	position: relative;
	#cn-accept-cookie{
		width: 18px;
		height: 18px;
		font-size: 0 !important;
		padding: 0px !important;
		top: 10px;
		right: 10px;
		position: absolute;
		border: 0px !important;
		box-shadow: 0px 0px 0px !important;
		background-color: rgba(0,0,0,0) !important;
		background-image: url('../images/close-icon.png') !important;
		background-repeat: no-repeat;
		background-position: center center !important;
		&:hover{
			background-position: center center !important;
		}
	}
}

.totop{
	text-align: center;
	text-transform: uppercase;
	position: absolute;
	bottom: 450px;
	right: 30px;
	cursor: pointer;
	display: none;
	.icon-totop{
		width: 37px;
		height: 138px;
		display: block;
		background-image: url('../images/icon-gototop.svg');
		background-repeat: no-repeat;
		margin-bottom: 10px;
	}
}

button,a{
	cursor: pointer;
	&.btn-primary{
		display: inline-flex;
		align-items: center;
		justify-content: center;
		min-width: 165px;
		/*height: 40px;*/
		height: auto;
		font-size: 14px ;
		text-align: left;
		font-weight: 700;
		border: 2px solid #77C2A1 !important;
		border-radius: 0 !important;
		padding: 11px 30px !important;
		text-decoration: none !important;
		color: #FFF !important;
		background-color: #77C2A1 !important;
		transition: all 0.3s linear;
		position: relative;
		// z-index: 99; disable z-index, The button show above cookie popup
		&:hover{
			color: #77C2A1 !important;
			background-color: transparent !important;
			border: 2px solid #77C2A1 !important;
		}
		&:focus,&:active{
			box-shadow: 0px 0px 0px !important;
			outline: unset !important;
		}
	}
    &.green-link{
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px ;
      font-weight: 700;
      padding: 0;
      color: #77C2A1;
      &:hover{
        color: #ADDAC7 !important;
      }
      &:focus,&:active{
        box-shadow: 0px 0px 0px !important;
        outline: unset !important;
      }
    }
	&.btn-back{
		display: inline-flex;
		align-items: center;
		justify-content: start;
		min-width: 120px;
		height: auto;
		font-size: 14px;
		line-height: 1.4;
		border: 0;
		padding: 0;
		text-decoration: none;
		color: #878787;
		transition: all 0.2s ease;
		@media screen and (max-width: 767px){
			width: 100%;
			justify-content: center;
		}
		&:active,&:visited{
			color: #878787 !important;
			background-color: #FFF !important;
			box-shadow: 0px 0px 0px !important;
			outline: unset !important;
		}
		&:hover{
			color: #77C2A1 !important;
			&:before{
				background-image: url(../images/arrow-left-green.svg);
			}
		}
		&:before{
			content: '';
			display: inline-flex;
			margin-right: 6px;
			width: 28px;
			height: 28px;
			background: transparent;
			background-image: url(../images/arrow-left-grey.svg);
			background-repeat: no-repeat;
			background-size: contain;
			background-position: top -1px center;
			transition: all 0.2s ease;
		}
	}
}

.nf-form-fields-required {
	display: none;
}

.white-text{
	color:#FFF;
	h1,h2,h3,h4,h5,p,span{
		color:#FFF;
	}
}
.green-text{
	color:#77C2A1;
}

.border-box{
	border-top: 1px solid #FFF;
	border-bottom: 1px solid #FFF;
	padding: 15px 0;
	a.mailto,a.link{
		font-weight: 700;

	}
	a.tel{
		font-weight: 700;
		color: #FFF;
		text-decoration: none;
		&:hover{
			color: #ADDAC7;
			text-decoration: underline;
		}
	}
}

.panel-grid{
	position: relative;
	.absolute{
		position: absolute;
		z-index: 2;
		&.left-top{
			left: -105px;
			top: -105px;
			@media screen and (max-width: 810px){
				left: -60px;
			}
			@media screen and (max-width: 768px){
				left: 0px !important;
			}
			@media screen and (max-width: 625px){
				top: -30px;
			}
		}
		&.left-bottom{
			left: -105px;
			bottom: -105px;
			@media screen and (max-width: 810px){
				left: -60px;
			}
			@media screen and (max-width: 768px){
				left: 0px !important;
			}
			@media screen and (max-width: 625px){
				bottom: -30px;
			}
		}
		&.right-top{
			right: -105px;
			top: -105px;
			@media screen and (max-width: 810px){
				right: -60px;
			}
			@media screen and (max-width: 768px){
				right: 0px !important;
			}
			@media screen and (max-width: 625px){
				top: -30px;
			}
		}
		&.right-bottom{
			right: -105px;
			bottom: -105px;
			@media screen and (max-width: 810px){
				right: -60px;
			}
			@media screen and (max-width: 768px){
				right: 0px !important;
			}
			@media screen and (max-width: 625px){
				bottom: -30px;
			}
		}
		h2{
			/*
			font-size: 40px;
			line-height: 1.4;
			@media screen and (max-width: 1199.9px) {
				font-size: 24px;
			}
			@media screen and (max-width: 991.9px) {
				font-size: 18px;
			}
			*/
		}
	}
}

.no-margin{
	margin: 0;
	p{
		margin: 0;
	}
}
.no-padding{
	padding: 0;
}

.filedownload-widget{
	.download-list{
		.download-item{
			border-bottom: 1px solid #878787;
			position: relative;
			&:first-child{
				border-top: 1px solid #878787;
			}
			a{
				color: inherit;
				text-decoration: none;
				display: block;
				padding: 20px 10px;
				padding-right: 60px;
				@include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
				&::before{
					content: '';
					width: 33px;
					height: 33px;
					position: absolute;
					right: 10px;
					top: 50%;
					 transform: translateY(-50%);
					background-image: url('../images/download-icon-back.svg');
					background-repeat: no-repeat;
					background-position: center;
					@include transition-property(all);
					@include transition-duration(0.2s);
					@include transition-timing-function(ease-in-out);
				}
				&:hover{
					background-color: #E4F3EC;
					&::before{
						background-image: url('../images/download-icon-green.svg');
					}
				}
			}
		}
	}
}

.number-repeater-widget{
	position: relative;
	.number-repeater-list{
		.owl-stage{
			padding-top: 1px;
		}
		.owl-item{
			margin-top: -1px;
			border-top: 1px solid #878787;
			border-bottom: 1px solid #878787;
		}
		.number-repeater-item-warp{
			width: 285px;
			max-width: 100%;
			/*margin-bottom: -2px;*/
			background-color: #ffffff;
			.number-repeater-item{
				// border-top: 1px solid #878787;
				// border-bottom: 1px solid #878787;
			}
			.number{
				width: 27%;
				font-size: 64px;
				font-weight: 700;
				letter-spacing: -10px;
				color: #E9F5F5;
				display: inline-block;
				vertical-align: middle;
			}
			.content{
				width: 71%;
				display: inline-block;
				vertical-align: middle;
			}
		}
	}
	.number-repeater-slider-counter{
		display: none;
		visibility: hidden;
	}
	@media (min-width:1199px){
		.number-repeater-list{
			.owl-carousel{
				display: block;
			}
			.owl-stage{
				width: 100% !important;
				transform: unset !important;
				/*
				columns: 3;
				-webkit-columns: 3;
				-moz-columns: 3;
				*/
				.owl-item:nth-child(3n+3){
					margin-right: 0px !important;
				}
			}
			.owl-nav,
			.owl-dots{
				display: none;
				visibility: hidden;
			}
		}
	}
	@media (min-width:992px) and (max-width: 1199px){
		.number-repeater-list{
			.owl-stage{
				width: 100% !important;
				transform: unset !important;
				/*
				columns: 2;
				-webkit-columns: 2;
				-moz-columns: 2;
				*/
				.owl-item:nth-child(2n+2){
					margin-right: 0px !important;
				}
			}
			.owl-nav,
			.owl-dots{
				display: none;
				visibility: hidden;
			}
		}
	}
	@media (max-width: 991px){
		width: 325px;
		.number-repeater-list{
			.number-repeater-item-warp{
				width: 253px;
				margin-bottom: 0px;
			}
			.owl-stage-outer{
				margin-bottom: 39px;
			}
			.owl-nav{
				display: block;
				visibility: visible;
				text-align: left;
				margin: 0;
				padding: 0;
				.owl-prev,
				.owl-next{
					width: 50px;
					height: 50px;
					background-color: rgba(104, 194, 159, 0.2);
					border-radius: 100%;
					opacity: 1;
					position: relative;
					padding: 0;
					margin-right: 8px;
					border: 0px;
					span{
						width: 30px;
						height: 30px;
						position: absolute;
						top: 50%;
						transform: translateY(-50%);
						left: 0;
						right: 0;
						margin: 0 auto;
						background-repeat: no-repeat;
						background-size: cover;
					}
				}
				.owl-prev{
					span{
						background-image: url('../images/arrow-left-green.svg');
					}
				}
				.owl-next{
					span{
						background-image: url('../images/arrow-right-green.svg');
					}
				}
				[class*=owl-]:hover{
					background-color: rgba(104, 194, 159, 0.2);
				}
			}
		}
		.number-repeater-slider-counter{
			display: block;
			visibility: visible;
			color: #77C2A1;
			position: absolute;
			right: 0;
			bottom: 17px;
		}
	}
	@media (max-width: 576px){
		width: 100%;
		.number-repeater-list{
			.number-repeater-item-warp{
				width: 305px;
			}
			.owl-stage-outer{
				margin-right: -20px;
			}
		}
	}
}

.widget_image-gallery-carousel-theme-widget{
	.so-widget-image-gallery-carousel-theme-widget{
		width: 420px;
		max-width: 100%;
    	position: absolute;
		@media screen and (max-width: 991px){
			max-width: 100%;
			left: 0;
			right: 0;
			//top: 100px;
			bottom: -400px;
			margin: 0 auto;
		}
	}
	.image-gallery-owl-carousel{
		position: relative;
		&.owl-theme{
			.owl-stage-outer{
				@media screen and (max-width: 991px){
					height: 350px;
				}
			}
			.owl-nav{
				margin: 0px;
				.owl-prev,
				.owl-next{
					height: 90%;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					padding: 0;
					margin: 0;
					background: transparent;
					border: 0px;
					@media screen and (max-width: 991px){
						height: auto;
						width: auto;
						top: unset;
						bottom: 0;
						transform: unset;
					}
					span{
						width: 50px;
						height: 50px;
						position: absolute;
						top: 50%;
						transform: translateY(-50%);
						background-color: rgba(104, 194, 159, 0.6);
						border-radius: 100%;
						opacity: 0;
						@media screen and (max-width: 991px){
							opacity: 1;
							background-color: rgba(104, 194, 159, 0.2);
							bottom: 0px;
							top: unset;
							transform: unset;
						}
						@include transition-property(all);
						@include transition-duration(0.2s);
						@include transition-timing-function(ease-in-out);
						&::after{
							content: '';
							width: 100%;
							height: 100%;
							position: absolute;
							top: 50%;
							transform: translateY(-50%);
							left: 0;
							right: 0;
							background-repeat: no-repeat;
							background-position: center;
						}
					}
					&:hover{
						background:transparent;
						span{
							opacity: 1;
						}
					}
				}
				.owl-prev{
					width: 50%;
					left: 0;
					span{
						left: 15px;
						@media screen and (max-width: 991px){
							left: 0;
						}
						&::after{
							background-image: url('../images/arrow-left-white.svg');
							@media screen and (max-width: 991px){
								background-image: url('../images/arrow-left-green.svg');
							}
						}
					}
				}
				.owl-next{
					width: 50%;
					right: 0;
					span{
						right: 15px;
						@media screen and (max-width: 991px){
							right: 0;
						}
						&::after{
							background-image: url('../images/arrow-right-white.svg');
							@media screen and (max-width: 991px){
								background-image: url('../images/arrow-right-green.svg');
							}
						}
					}
				}
			}
			.owl-dots{
				margin-top: 20px;
				@media screen and (max-width: 991px){
					margin-top: 48px;
					padding-bottom: 10px;
				}
				.owl-dot{
					border: 0px;
					background-color: transparent;
					span{
						background-color: #E4F3EC;
					}
					&.active,
					&:hover{
						span{
							background-color: #77C2A1;
						}
					}
				}
			}
		}
	}
}

.rmp-results-widget{
	font-size: 11px;
	.rmp-results-widget__visual-rating{
		margin-bottom: 20px;
		.js-rmp-results-icon{
			&::before{
				content: '';
				width: 24px;
				height: 24px;
				background: url('../images/star.svg');
				background-repeat: no-repeat;
				background-size: cover;
				background-position: center;
				display: inline-block;
				margin-right: 6px;
			}
			&:not(.rmp-icon--full-highlight){
				opacity: 0.2;
			}
		}
	}
}

.panel-grid-cell{
	@media screen and (max-width: 600px) {
		position: relative;
	}
}

.tablet-50 {
	@media screen and (max-width: 990.9px) and (min-width: 768px) {
		width: 50%;
	}
}

.hidden-desktop{
	@media screen and (min-width: 991px){
		display: none !important;
	}
}

.hidden-mobile{
	@media screen and (max-width: 991px){
		display: none;
	}
}

.modal{
	.modal-dialog{
		.modal-content{
			iframe{
				max-width: 100%;
			}
		}
	}
}

.read-more-section{
	.text-wrapper{
		.description{
			column-gap: 40px;
		}
	}
}

#usercentrics-root{
    position: absolute;
    z-index: 1;
}

@import "style-responsive.scss";
@import "home";
@import "application_area";
@import "contact-form";
@import "search-filter";
@import "single-page";
@import "products-style";
@import "style-newspage";
@import "distributor";
@import "single-job";
@import "testimony-style";
@import "resource-library";
@import "navbar-mobile";
@import "scroll";
@import "search-result";
@import "404";
@import "page-landing";