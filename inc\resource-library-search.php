<?php
function resource_search_form(){
    wp_enqueue_script('resource-library-search');
    ob_start();
    ?>
    <div class="form-row resource-search">
        <div class="col-12 col-lg-10">
            <div class="form-group">
                <input type="text" class="form-control" id="resource-search-text" placeholder="<?php _e('Eg: Ion Channels', 'radar_child_fd'); ?>">
                <span class="icon-search"></span>
            </div>
        </div>
        <div class="col-12 col-lg-2 text-lg-center">
            <button type="button" class="btn btn-primary" id="resource-search-button"><?php _e('Search', 'radar_child_fd'); ?></button>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('resource-search-text', 'resource_search_form');
?>