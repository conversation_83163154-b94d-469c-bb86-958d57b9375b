@import "compass";

.products-list{
    .products-item{
        a{
            color: #000000;
            text-decoration: none;
            display: block;
            position: relative;
            @include transition-property(all);
            @include transition-duration(0.3s);
            @include transition-timing-function(ease-in-out);
            &::after{
                width: 27px;
                height: 28px;
                content: '';
                position: absolute;
                background: url('../images/arrow-right-black.svg');
                background-repeat: no-repeat;
                background-size: cover;
                background-position: center;
                top: 50%;
                transform: translateY(-50%);
                right: 10px;
                opacity: 0;
                visibility: hidden;
                @include transition-property(all);
                @include transition-duration(0.3s);
                @include transition-timing-function(ease-in-out);
                @media screen and (max-width: 576px) {
                    right: 0px;
                }
            }
            &:hover{
                &::after{
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
        .card{
            border: 0px;
            border-top: 1px solid #878787;
            border-bottom: 1px solid #878787;
            border-radius: 0px;
            background: none;
            .thumb{
                padding-left: 15px;
                padding-right: 15px;
            }
            .card-body{
                width: 214px;
                max-width: 100%;
                padding-top: 20px;
                padding-bottom: 20px;
                padding-right: 15px;
                padding-left: 15px;
            }
            .card-title{
                font-weight: bold;
                margin-bottom: 0px;
            }
        }
        @media screen and (min-width: 992px) {
            &:nth-child(n+4){
                .card{
                    border-top: 0px;
                }
            }
        }
        @media screen and (min-width: 768px) and (max-width: 991px) {
            &:nth-child(n+3){
                .card{
                    border-top: 0px;
                }
            }
        }
        @media screen and (max-width: 767px) {
            &:nth-child(n+2){
                .card{
                    border-top: 0px;
                }
            }
        }
        @media screen and (max-width: 768px) {
            &.port-a-patch{
                order: 1;
            }
            &.patchliner{
                order: 3;
            }
            &.syncropatch-384{
                order: 4;
            }
            &.port-a-patch-mini{
                order: 2;
            }
            &.buffer-solutions{
                order: 5;
            }
        }
    }
    &.automated-patch-clamp{
        .products-item{
            a{
                &:hover{
                    background-color: #D2D9E8;
                }
            }
        }
    }
    &.membrane-biophysics{
        .products-item{
            a{
                &:hover{
                    background-color: #F3F2F7;
                }
            }
        }
    }
    &.cell-monitoring{
        .products-item{
            a{
                &:hover{
                    background-color: #E9F5F5;
                }
            }
        }
    }
}

.single-products{

    .productTabMenu{
        background-color: #66BBC1;
        position: relative;
        opacity: 1;
        @include transition-property(all);
        @include transition-duration(0.5s);
        @include transition-timing-function(ease);
        @media screen and (max-width: 991px){
            position: absolute;
            width: 100%;
            z-index: 9;
        }
        .nav{
            border: 0px;
            .nav-item{
                margin-bottom: 0px;
            }
            .nav-link{
                font-size: 14px;
                font-weight: 400;
                color: #000000;
                line-height: 28px;
                text-decoration: none;
                padding: 17px 0px;
                background: none;
                border: 0px;
                position: relative;
                // text-align: center;
                span{
                    display: block;
                    &::before{
                        display: block;
                        content: attr(data-text);
                        font-weight: bold;
                        height: 0px;
                        color:transparent;
                        overflow: hidden;
                        visibility: hidden;
                        //letter-spacing: 0.6px;
                    }
                }
                &:hover,
                &.active{
                    &::before,
                    &::after{
                        opacity: 1;
                    }
                    span{
                        font-weight: bold;
                    }
                }
                &::before,
                &::after{
                    content: '';
                    width: 8px;
                    height: 23px;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    opacity: 0;
                    @include transition-property(all);
                    @include transition-duration(0.3s);
                    @include transition-timing-function(ease-in-out);
                }
                &::before{
                    background: url('../images/product-menu-hover-left.svg') no-repeat left center;
                    left: -10px;
                }
                &::after{
                    background: url('../images/product-menu-hover-right.svg') no-repeat right center;
                    right: -10px;
                }
            }
        }

        @media screen and (max-width: 991px){
            .nav{
                .nav-item{
                    &:first-child{
                        a{
                            .toggle-arrow{
                                width: 100%;
                                height: 100%;
                                display: inline-block;
                                visibility: visible;
                                position: absolute;
                                top: 0;
                                right: 0px;
                                background: url('../images/arrow-down-black.svg') no-repeat right center;
                                @include transition-property(all);
                                @include transition-duration(0.3s);
                                @include transition-timing-function(ease-in-out);
                            }
                        }
                    }
                    &:not(:first-child){
                        // height: 0px;
                        visibility: hidden;
                        opacity: 0;
                        z-index: 0;
                        position: absolute;
                        @include transition-property(all);
                        @include transition-duration(0.3s);
                        @include transition-timing-function(ease-in-out);
                        a{
                            visibility: hidden;
                        }
                    }
                }
                .nav-link{
                    padding-top: 6px;
                    padding-bottom: 6px;
                    &::before,
                    &::after{
                        display: none;
                        visibility: hidden;
                    }
                    &:hover{
                        -webkit-text-stroke-width: 0px;
                    }
                    &.active{
                        -webkit-text-stroke-width: 0px;
                        font-weight: 700;
                    }
                }
                &.active{
                    .nav-item{
                        &:first-child{
                            a{
                                .toggle-arrow{
                                    @include rotateX(180deg);
                                }
                            }
                        }
                        &:not(:first-child){
                            // height: auto;
                            visibility: visible;
                            opacity: 1;
                            //border-top: 1px solid #000000;
                            z-index: 1;
                            position: relative;
                            a{
                                visibility: visible;
                            }
                            &::before{
                                content: '';
                                height: 1px;
                                width: 9999em;
                                position: absolute;
                                left: -999em;
                                right: -999em;
                                background-color: #000000;
                            }
                        }
                        &:last-child{
                            &::after{
                                content: '';
                                height: 1px;
                                width: 9999em;
                                position: absolute;
                                left: -999em;
                                right: -999em;
                                background-color: #000000;
                            }
                        }
                    }
                }
            }
        }
    }

    .product-content{
        .product-content-head{
            padding-bottom: 110px;
            @media screen and (max-width: 767px){
                padding-bottom: 50px;
            }
        }
        .add_space{
            padding-top: 87px;
        }

        .back-overview{
            font-size: 14px;
            line-height: 32px;
            color: #878787;
            padding: 0;
            margin-left: -5px;
            margin-bottom: 30px;
            text-decoration: none;
            &::before{
                content: '';
                width: 32px;
                height: 32px;
                margin-right: 6px;
                display: inline-block;
                vertical-align: middle;
                background: url('../images/arrow-product-left-back.svg') no-repeat center;
            }
            &:hover{
                color: #77C2A1;
                -webkit-text-fill-color: #77C2A1;
                -webkit-text-stroke-width: 1px;
                -webkit-text-stroke-color: #77C2A1;
                &::before{
                    background: url('../images/arrow-product-left-green.svg');
                }
            }
        }

        .product-subtitle{
            font-weight: 700;
            color: #008D97;
            padding-left: 15px;
            @media screen and (max-width: 991px) {
                margin-bottom: 55px;
            }
            span{
                position: relative;
                &::before,
                &::after{
                    content: '';
                    width: 9px;
                    height: 36px;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    background-size: cover;
                }
                &::before{
                    //background: url('../images/product-sub-title-left.svg') no-repeat left center;
                    background-color: #008D97;
                    -webkit-mask-image: url('../images/product-sub-title-left.svg');
                    mask-image: url('../images/product-sub-title-left.svg');
                    left: -15px;
                }
                &::after{
                    //background: url('../images/product-sub-title-right.svg') no-repeat right center;
                    background-color: #008D97;
                    -webkit-mask-image: url('../images/product-sub-title-right.svg');
                    mask-image: url('../images/product-sub-title-right.svg');
                    right: -15px;
                }
                &.blue{
                    &::before{
                        background-color: #1D418E;
                    }
                    &::after{
                        background-color: #1D418E;
                    }
                }
                &.purple{
                    &::before{
                        background-color: #8F7FB6;
                    }
                    &::after{
                        background-color: #8F7FB6;
                    }
                }
            }
        }

        .rating-section{
            > .panel-grid-cell{
                &:nth-child(2){
                //&:nth-child(3){
                    border-top: 1px solid #000000;
                    border-bottom: 1px solid #000000;
                    overflow: hidden;
                }
                @media (min-width:991px) and (max-width:1199px) {
                    /*
                    &.panel-grid-cell-empty{
                        display: none;
                        visibility: hidden;
                    }
                    */
                }
                @media screen and (max-width: 991px){
                    &:nth-child(2){
                        /*border-bottom: 0px;*/
                    }
                    &:nth-child(3){
                        border-top: 0px;
                    }
                }
            }
            #selectscience-reviews{
                margin-top: -1.7em;
                @media screen and (max-width: 767px){
                    height: 220px;
                }
            }
        }

        @media screen and (max-width: 991px){
            h1{
                font-size: 32px;
            }
            .characters-max-content{
                p{
                    font-size: 14px !important;
                }
            }
        }
    }

    .product-contact{
        .container{
            background-color: #f2f2f2;
            position: relative;
            &::after{
                left: -999em;
                content: '';
                display: block;
                position: absolute;
                width: 999em;
                top: 0;
                bottom: 0;
                background-color: inherit;
            }
        }
        &.active-overview{
        }
    }
}