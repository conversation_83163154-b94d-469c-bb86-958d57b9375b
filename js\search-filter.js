jQuery(function($){
    hiddenItem();

    $('.list-filter').on('click', '.sf-field-taxonomy-news_categories h4, .sf-field-taxonomy-team_categories h4, .sf-field-taxonomy-distributor_categories h4, .sf-field-taxonomy-partners_categories h4', function(e){
        e.preventDefault();
        $(this).closest('li').toggleClass('active');
    });

    $('.search-filter-results').on('click', '.button-area .read-more', function(e){
        e.preventDefault();
        var element = $('.hide-element-mobile');
        toggleItem(element, $(this));
    });
    
    var id;
    $(window).resize(function() {
        var $window = jQuery(window);
        if($window.width() >= 768){
            clearTimeout(id);
            id = setTimeout(hiddenItem, 500);
        }
    });
    
});

(function ( $ ) {
    // $(document).on("sf:init", ".searchandfilter", function(){
    //     hiddenItem();
    // });
    $(document).on("sf:ajaxfinish", ".searchandfilter", function(){
        hiddenItem();
    });
}(jQuery));

function hiddenItem(){
    var $window = jQuery(window);
    if($window.width() < 768){
        jQuery('*[class*=hide-element-mobile]:visible').each(function() {
            var count_item = jQuery('.item').length;
            if(count_item > 2){
                jQuery('.item').each(function(el) {
                    if(el > 1){
                        jQuery(this).addClass('hidden-item-mobile');
                        //jQuery(this).hide();
                    }
                });
                jQuery(this).find('.button-area').html('<a class="btn-primary read-more" href="#">'+ search_filter_val.viewmore_text +'</a>');
            }
        });
    }else{
        jQuery('*[class*=hide-element-mobile]:visible').each(function() {
            jQuery(this).find('.item').show();
            jQuery(this).find('.button-area').html('');
        });
    }
}

function toggleItem(element, alink){
    let hide = true;
    //element.find('.hidden-item-mobile').slideToggle('medium', function() {
    element.find('.hidden-item-mobile').each(function(index){
        //console.log(jQuery(this).is(':visible'));
        if (!jQuery(this).is(':visible')){
            hide = false;
            jQuery(this).addClass('active');

            (function(that, i) { 
                var t = setTimeout(function() { 
                    jQuery(that).addClass('is-open'); 
                }, 200);
            })(this, index);

            //jQuery(this).css('visibility','');
            //jQuery(this).show();
            if(alink){
                alink.text(search_filter_val.viewless_text);
            }
        }else{
            heide = true;
            jQuery(this).removeClass('active');
            jQuery(this).removeClass('is-open'); 
            //jQuery(this).hide();
            if(alink){
                alink.text(search_filter_val.viewmore_text);
            }
        }
    });
    if(hide == true){
        jQuery('html, body').animate({
            scrollTop: element.offset().top - 120
        }, 100);
    }
}