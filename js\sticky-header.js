//======================================================== sticky hide on scroll down, show on scroll up
jQuery(document).ready(function(){
	jQuery(window).scroll(function() {
		stickyScroll();
	});
});

function stickyScroll(){
	let scrollTop = jQuery(this).scrollTop();
	let checkUpOrDown = (this.oldScroll > this.scrollY);
	let HeaderHeight = 200;
	let checkMobileMenuOpen = false;

	if(jQuery('.site-header').hasClass('active-navbar')){
		checkMobileMenuOpen = true;
	}
	
	if(checkMobileMenuOpen !== true){
		if(checkUpOrDown == false){
			if (scrollTop > HeaderHeight){
				jQuery('.site-header').addClass('hide').addClass('fixed');
			}else if (scrollTop > 100){
				jQuery('.site-header').addClass('fixed');
			} else{
				jQuery('.site-header').removeClass('fixed');
			}
		}else if((checkUpOrDown == true)&&(scrollTop > HeaderHeight)){
			jQuery('.site-header').removeClass('hide');
		}else if((checkUpOrDown == true)&&(scrollTop < 100)){
			jQuery('.site-header').removeClass('fixed').removeClass('hide');
		}
		this.oldScroll = this.scrollY; // reset checkUpOrDown
	}

}

jQuery(function($){
    
    setheaderheight();

    var header_resize;
    $(window).resize(function() {
        clearTimeout(header_resize);
        header_resize = setTimeout(setheaderheight, 500);
    });

});

function setheaderheight(){
	nav_primary = jQuery('.nav-primary').height();
	tabmenu = jQuery('.tabmenu').height();
	if(tabmenu !== undefined){
		HeaderHeight = nav_primary + tabmenu;
	}else{
		HeaderHeight = nav_primary;
	}

    jQuery('.site-header').css('height', HeaderHeight + 'px');
    jQuery('body').css('padding-top', HeaderHeight + 'px');
}
//======================================================== sticky hide on scroll down, show on scroll up

// This will select all <a> tags inside #mega-menu-primary, including nested ones
document.addEventListener('DOMContentLoaded', function() {
  document
    .querySelectorAll(
      '#mega-menu-primary > li.mega-menu-megamenu > ul.mega-sub-menu li.mega-menu-column > ul.mega-sub-menu > li.mega-menu-item > a[href="#"]'
    )
    .forEach(function(link) {
      link.classList.add('no-link');
      link.setAttribute('href', 'javascript:void(0);');
    });
});