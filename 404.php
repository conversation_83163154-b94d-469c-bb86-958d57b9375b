<?php
get_header(); ?>
	<div class="container py-5">
		<div class="simple-header-section-404">
			<div class="titlewidget-section" uk-scrollspy="cls:uk-animation-slide-bottom; delay: 300">
				<svg class="bf-text-mobile">	
					<path class="st0" d="M8.6,104.7V16.3H0V0h26v9.5v6.7v88.4v4.4v11.9H0v-16.3H8.6z"></path>
				</svg>
				<svg class="bf-text">
					<path d="M0.5,0.3V23h12v123h-12v22.7h36.3v-16.5V146V23v-9.4V0.3H0.5z"></path>
				</svg>
				<div class="title-box">		
					<div class="texttitle" uk-scrollspy="cls:uk-animation-slide-bottom; delay: 400">
						<h1><?php _e('404 - Page not found', 'radar_child_fd'); ?></h1>
					</div>
					<h4 class="titledescription" uk-scrollspy="cls:uk-animation-slide-bottom; delay: 500">
						<?php echo sprintf(__('The page you are looking for might have been removed had its name changed or is temporarily unavailable. Head back <a href="%s">home</a>.', 'radar_child_fd'), get_home_url()); ?>
					</h4>
				</div>
				<svg class="at-text">
					<path d="M25.2,146.2V22.8h12V0H0.8v13.3v9.4v123.5v6.2V169h36.3v-22.8H25.2z"></path>
				</svg>
				<svg class="at-text-mobile">	
					<path class="st0" d="M17.4,104.7V16.3H26V0H0v9.5v6.7v88.4v4.4v11.9h26v-16.3H17.4z"></path>
				</svg>
			</div>
		</div>
	</div>
<?php get_footer(); ?>
