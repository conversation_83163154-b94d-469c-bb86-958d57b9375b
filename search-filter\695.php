<?php
/**
 * Search & Filter Pro 
 *
 * Sample Results Template
 * 
 * @package   Search_Filter
 * <AUTHOR>
 * @link      https://searchandfilter.com
 * @copyright 2018 Search & Filter
 * 
 * Note: these templates are not full page templates, rather 
 * just an encaspulation of the your results loop which should
 * be inserted in to other pages by using a shortcode - think 
 * of it as a template part
 * 
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs 
 * and using template tags - 
 * 
 * http://codex.wordpress.org/Template_Tags
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/*
$sf_id = $query->query['search_filter_id'];
echo $sf_id;
*/

$post_type = $query->query['post_type'];
$num_cal = 3;

if ( $query->have_posts() )
{
    wp_enqueue_script('news-filter');
    //uk-scrollspy="target: > div; cls: uk-animation-slide-bottom; delay: 200"
    //row row-cols-1 row-cols-md-2 row-cols-lg-<?php echo $num_cal;
    //post-list-owl owl-carousel owl-theme
	?>
	<div class="post-list post-list-<?php echo $post_type; ?> loop-grid row row-cols-1 row-cols-md-2 row-cols-lg-3">

		<?php
		while ($query->have_posts())
		{
			$query->the_post();
            ?>
            <div class="card">
                <a href="<?php the_permalink(); ?>">
                    <?php
                    if ( has_post_thumbnail() ) {
                        $thumb_url=wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'full');
                        $thumb_url=$thumb_url[0];
                    }else{
                        if($radar_fd_option['no-img']['url']){
                            $thumb_url=$radar_fd_option['no-img']['url'];
                        }else{
                            $thumb_url=get_template_directory_uri().'/images/no_image.png';
                        }
                    }

                    $feature_vdo = get_field('feature_video',get_the_ID());
                    ?>
                    <div class="image-wrapper" style="background-image:url(<?php echo $thumb_url; ?>);">
                        <?php if($feature_vdo): ?>
                            <video class="card-vdo-top" oop preload="none" controlsdata-play="hover" muted loop playsinline>
                                <source src="<?php echo $feature_vdo['url']; ?>" type="<?php echo $feature_vdo['mime_type']; ?>">
                            </video>
                        <?php else: ?>
                            <img class="card-img-top" src="<?php echo $thumb_url; ?>">
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="post-date"><?php echo get_the_date('d. M Y',$post->ID)?></div>
                        <h4 class="card-title"><?php the_title(); ?></h4>
                        <div class="card-text">
                            <?php the_excerpt(); ?>
                        </div>
                        <div class="arrow-link"><?php echo __('Read more','radar_fd'); ?></div>
                    </div>
                </a>
            </div>
            <?php
		}
		?>
	</div>
    <div class="button-area"></div>
	<?php
}
else
{
    //echo "No Results Found";
    echo "<div class='no-results-text'>";
    _e('Ingen resultater', 'radar_fd_child');
    echo "</div>";
}
?>