@import "compass";

form{
  &.searchandfilter{
    ul{
      padding: 0;
      > li{
        border: 0 !important;
        padding: 0;
        > ul{
          display: flex;
          width: 100% !important;
          position: initial !important;
          visibility: unset !important;
          height: auto !important;
          border: 0 !important;
          background: transparent !important;
          padding: 0;
          @media screen and (max-width: 768px){
            display: block;
          }
          li{
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            position: relative;
            padding-left: 30px !important;
            padding-right: 30px !important;
            @media screen and (max-width: 768px){
              display: inline-block;
            }
            @media screen and (max-width: 485px){
              padding-left: 0 !important;
              padding-right: 0 !important;
              margin-right: 20px;
            }
            &:first-child{
              padding-left: 0px;
              display: none;
              visibility: hidden;
            }
            &.sf-item-0{
              &:first-child{
                /*font-weight: 700;*/
                font-weight: normal !important;
                .sf-label-radio{
                  font-weight: normal;
                  -webkit-text-stroke-width: 0px;
                }
                &:not(.sf-option-active){
                  &:hover{
                    .sf-label-radio{
                      -webkit-text-stroke-width: 1px;
                    }
                  }
                }
              }
            }
            &.sf-option-active{
              /*padding: 0;*/
              background: transparent;
              .sf-label-radio{
                /*font-weight: 700;*/
                font-weight: normal;
                color: #77C2A1;
                -webkit-text-fill-color: #77C2A1;
                -webkit-text-stroke-width: 1px;
                -webkit-text-stroke-color: #77C2A1;
                &:after{
                  transform: scaleX(1);
                }
              }
              &:after{
                transform: scaleX(1);
                &:hover{
                  display: none;
                }
              }
            }
            .sf-input-radio{
              display: none;
            }
            .sf-label-radio{
              padding: 0;
              font-size: 14px;
              font-weight: 100;
              line-height: 1.2;
              color: #000;
              cursor: pointer;
              display: inline-flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-between;
              text-decoration: none;
              transition: all 0.2s ease;
              @media screen and (max-width: 485px){
                font-size: 14px;
              }
              &:before{
                content: attr(data-text);
                content: attr(data-text) / "";
                height: 0;
                visibility: hidden;
                overflow: hidden;
                user-select: none;
                pointer-events: none;
                font-weight: 700;
              }
              &:after {
                &:hover{
                  font-weight: 700;
                  text-decoration: none;
                }
              }
            }
            &:after {
              content: '';
            }
            &:hover{
              .sf-label-radio{
                color: #77C2A1;
                -webkit-text-fill-color: #77C2A1;
                -webkit-text-stroke-width: 1px;
                -webkit-text-stroke-color: #77C2A1;
                text-decoration: none;
              }
              &:after {
                transform: scaleX(1);
                transform-origin: bottom left;
              }
            }
          }
        }
        &:after{
          display: none;
        }
      }
    }
  }
}
.search-filter-results{
  .no-results-text{
    margin-top: 60px;
    margin-bottom: 60px;
  }
}
.partners-items{
  margin-top: 95px;
  @media screen and (max-width: 991px){
    margin-top: 50px;
  }
  .partners-item{
    display: inline-flex;
    border-top: 1px solid #878787;
    border-bottom: 1px solid #878787;
    flex: 0 0 calc(25% - 30px);
    margin: 0 15px -1px 15px;
    padding: 30px 25px;
    background-color: #FFF;
    transition: all 0.3s linear;
    @media screen and (max-width: 1199.9px) {
      padding: 30px 15px;
    }
    @media screen and (max-width: 991.9px) {
      flex: 0 0 calc(33.333% - 30px);
      max-width: calc(33.333% - 30px);
    }
    @media screen and (max-width: 767.9px) {
      flex: 0 0 calc(100% - 30px);
      max-width: 100%;
      padding: 30px 30px;
      &.hidden-item-mobile{
        opacity: 0;
        display: none;
        @include transition-property(opacity);
				@include transition-duration(1s);
				@include transition-timing-function(ease-in-out);
        &.active{
          display: inline-block;
          @include transition-property(all);
				  @include transition-duration(0.5s);
				  @include transition-timing-function(ease-in-out);
        }
        &.is-open{
          opacity: 1;
        }
      }
    }
    .card{
      width: 100%;
      border: 0;
      background-color: transparent;
      padding: 0;
      .card-photo{
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: start;
        img{
          width: 100%;
          /*max-height: 70px;*/
          object-fit: contain;
          object-position: center;
          filter: grayscale(1);
          //transition: transform 0.3s linear;
          @include transition-property(all);
				  @include transition-duration(0.3s);
				  @include transition-timing-function(linear);
        }
        margin-bottom: 15px;
      }
      .card-body{
        padding: 0;
        font-size: 14px;
        h3{
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 0;
        }
        p{
          font-size: 11px;
        }
        a{
          color:#000;
          text-decoration: none;
          transition: all 0.3s linear;
          &:hover{
            color: #77C2A1;
          }
        }
      }
    }
    &:hover{
      .card{
        .card-photo{
          img{
            filter: grayscale(0);
          }
        }
        .card-body{
          a.text-link{
            color: #77C2A1;
          }
        }
      }
    }
  }
}
.team-items{
  margin-top: 60px;
  .team-item{
    display: inline-flex;
    flex: 0 0 calc(25% - 30px);
    margin: 0 15px -1px 15px;
    padding: 0;
    background-color: transparent;
    transition: all 0.3s linear;
    @media screen and (max-width: 1199.9px) {
      flex: 0 0 calc(33.33% - 30px);
      max-width: calc(33.33% - 30px);
    }
    @media screen and (max-width: 767.9px) {
      flex: 0 0 calc(50% - 30px);
      max-width: calc(50% - 30px);
    }
    @media screen and (max-width: 450.9px) {
	  /*
      flex: 0 0 calc(100% - 30px);
      max-width: calc(100% - 30px);
	  */
    }
    .card{
      border: 0;
      background-color: transparent;
      padding: 0;
      @media screen and (max-width: 991px) {
        display: block;
      }
      @media screen and (max-width: 450.9px) {
        width: 100%;
      }
      .card-photo{
        display: flex;
        width: 100%;
        height: 170px;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        @media screen and (max-width: 450.9px) {
          height: auto;
        }
        img{
          height: 170px;
          object-fit: cover;
          object-position: center;
          transition: all 0.5s ease;
          @media screen and (max-width: 450.9px) {
            width: 100%;
            height: auto !important;
          }
        }
      }
      .card-body{
        padding: 20px;
        padding-bottom: 0;
        font-size: 14px;
        @media screen and (max-width: 991px) {
          padding: 10px 0px;
          float: left;
          width: 80%;
        }
        h3{
          font-size: 14px;
		      line-height: 120% !important;
          font-weight: bold;
          margin-bottom: 10px;
          @media screen and (max-width: 991px) {
            word-spacing: 100vw;
            font-size: 11px;
          }
        }
        p{
          margin-bottom: 20px;
          @media screen and (max-width: 991px) {
            font-size: 11px;
          }
          .sow-icon-fontawesome{
            font-size: 25px;
			line-height: 120%;
            color: #878787;
            margin-right: 10px;
            transition: all 0.3s linear;
          }
        }
        a{
          color:#000;
          text-decoration: none;
          transition: all 0.3s linear;
          &:hover{
            color: #77C2A1;
          }
        }
      }
      .card-footer{
        padding: 20px;
        padding-top: 0px;
        padding-bottom: 40px;
        background: none;
        border: 0px;
        @media screen and (max-width: 991px) {
          float: right;
          width: 19%;
          padding: 10px 0px;
          text-align: right;
        }
        .sow-icon-fontawesome{
          font-size: 25px;
          line-height: 120%;
          color: #878787;
          margin-right: 10px;
          transition: all 0.3s linear;
          @media screen and (max-width: 991px) {
            margin-right: 0px;
          }
        }
        a{
          &:hover{
            .sow-icon-fontawesome{
              color: #77C2A1;
            }
          }
        }
      }
    }
    &:hover{
      .card{
        .card-photo{
          img{
            transform: scale(1.05);
          }
        }
        .card-body{
          a.text-link{
            color: #77C2A1;
          }
          p{
            .sow-icon-fontawesome{
              color: #77C2A1;
            }
          }
        }
      }
    }
  }
}
form.searchandfilter{
  .sf-field-taxonomy-news_categories,
  .sf-field-taxonomy-team_categories,
  .sf-field-taxonomy-distributor_categories,
  .sf-field-taxonomy-partners_categories{
    padding-left: 0 !important;
    &:before{
      display: none;
    }
    > h4{
      display: inline-flex;
      vertical-align: bottom;
      padding: 0;
      margin: 0;
      font-size: 14px;
      line-height: 1.2 !important;
      font-weight: bold;
      margin-right: 30px;
      @media screen and (max-width: 485px){
        margin-right: 20px;
      }
    }
    > ul{
      display: inline;
      vertical-align: bottom;
    }
    ul{
      li{
        &:before{
          display: none;
        }
        &:first-child{
          display: inline-flex !important;
          visibility: visible !important;
          &.sf-option-active{
            .sf-label-radio{
              /*
              font-weight: 700;
              color: #000;
              -webkit-text-fill-color: #000;
              -webkit-text-stroke-width: 1px;
              -webkit-text-stroke-color: #000;
              text-decoration: none;
              */
            }
          }
          .sf-label-radio{
            /*font-weight: 700;*/
            color: #000;
            -webkit-text-fill-color: #000;
            -webkit-text-stroke-width: 0px;
            -webkit-text-stroke-color: #000;
            text-decoration: none;
          }
          &:hover{
            .sf-label-radio{
              -webkit-text-stroke-width: 0px !important;
            }
          }
        }
      }
    }
    @media screen and (max-width: 991px){
      > h4{
        width: 100%;
        padding: 15px 10px;
        margin-right: 0px;
        display: block;
        position: relative;
        border: 1px solid #878787 !important;
        &::after{
          content: '';
          width: 10px;
          height: 6px;
          position: absolute;
          top: 50%;
				  transform: translateY(-50%);
          right: 10px;
          background-image: url('../images/dropdown-arrow.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }
      }
      > ul{
        display: none;
        /*position: absolute !important;*/
        /*
        visibility: hidden;
        opacity: 0;
        height: 0;
        @include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
        */
        > li.sf-level-0{
          display: block !important;
          padding-left: 0 !important;
          padding-right: 0 !important;
          margin-right: 0px !important;
          border-bottom: 1px solid #878787 !important;
          border-left: 1px solid #878787 !important;
          border-right: 1px solid #878787 !important;
          .sf-label-radio{
            display: block;
            padding-left: 10px !important;
            padding-right: 0px !important;
            padding-top: 15px;
            padding-bottom: 15px !important;
          }
          &:first-child{
            /*border-top: 1px solid #878787 !important;*/
          }
          &.sf-option-active{
            background-color: #E4F3EC;
            .sf-label-radio{
              color: #000000;
              -webkit-text-fill-color: unset;
              -webkit-text-stroke-width: 0px;
              -webkit-text-stroke-color: unset;
              &::after{
                content: '';
                width: 13px;
                height: 13px;
                display: inline-block;
                vertical-align: middle;
                margin-left: 14px;
                background-image: url('../images/checked.svg');
                background-repeat: no-repeat;
                background-size: contain;
              }
            }
          }
        }
      }
      &.active{
        h4{
          &::after{
            transform: rotate(180deg);
          }
        }
        > ul{
          display: block;
          /*
          visibility: visible;
          opacity: 1;
          height: auto;
          */
        }
      }
    }
  }
  .sf-field-taxonomy-team_categories{
    ul{
      li{
        &:first-child{
          .sf-label-radio{
            color: #77C2A1;
            -webkit-text-fill-color: #77C2A1;
            -webkit-text-stroke-width: 0px;
            -webkit-text-stroke-color: #77C2A1;
          }
        }
      }
    }
  }
}

.post-list-news{
  width: calc(100% + 30px);
  column-count: 3;
  column-gap: 0;
  orphans: 1;
  widows: 1;
  margin-top: 65px;
  /*
  .owl-stage{
    columns: 3;
    -webkit-columns: 3;
    -moz-columns: 3;
    .owl-item{
      
    }
  }
  */
  @media screen and (max-width: 768px){
    width: 100%;
    margin: auto;
    margin-top: 50px;
    &.post-list-owl, &.owl-theme{
      width: unset;
      column-count: unset;
      column-gap: unset;
      orphans: unset;
      widows: unset;
      text-align: left;
      overflow: hidden;
      .owl-stage-outer{
        margin-left: -40px;
        margin-bottom: 0px;
      }
      .owl-stage{
        display: flex;
        justify-content: start;
      }
      .owl-item{
        display: inline-flex;
        .card-body{
          padding: 25px 20px 15px 20px;
        }
      }
      .owl-nav{
        margin-top: 0px;
      }
      .owl-nav [class*=owl-]{
        border: 0;
        border-radius: 50%;
        background-color: #d6e8e1;
        display: inline-flex;
        width: 50px;
        height: 50px;
        transition: all 0.3s ease;
        &:hover{
          background-color: #77C2A1;
        }
        &.owl-prev{
          @media screen and (max-width: 769px){
            float: left;
          }
          &:before{
            content: '';
            display: block;
            background-image: url("../images/arrow-left-green.svg");
            width: 30px;
            height: 30px;
            background-size: 30px 30px;
            margin: auto;
          }
          &:hover{
            &:before{
              background-image: url("../images/arrow-left-white.svg");
            }
          }
          > span{
            display: none !important;
          }
        }
        &.owl-next{
          margin-left: 8px;
          @media screen and (max-width: 769px){
            float: right;
            margin-right: 0px;
          }
          &:before{
            content: '';
            display: block;
            background-image: url("../images/arrow-right-green.svg");
            width: 30px;
            height: 30px;
            background-size: 30px 30px;
            margin: auto;
          }
          &:hover{
            &:before{
              background-image: url("../images/arrow-right-white.svg");
            }
          }
          > span{
            display: none !important;
          }
        }
        span{
          font-size: 0;
        }
      }
    }
  }
  .card{
    border: 0;
    background-color: transparent;
    display: none;
    width: 100%;
    padding: 0 15px;
    margin-bottom: 42px;
    border-radius: 0px;
    overflow: hidden;
    @include transition-property(opacity);
    @include transition-duration(1s);
    @include transition-timing-function(ease-in-out);
    @media screen and (max-width: 767px){
      padding: 0;
      // display: inline-block !important;
      // opacity: 1 !important;
    }
    &:not(.is-open){
      opacity: 0;
    }
    &.is-visible{
        display: inline-block;
    }
    &.is-open{
        margin-top: 0;
        opacity: 1;
    }
    a{
      text-decoration: none;
      color: #000000;
    }
    .image-wrapper{
      max-width: 390px;
      height: 227px;
      overflow: hidden;
      @media screen and (max-width: 768px){
        padding: 0;
        width: 100%;
        max-width: unset;
      }
      @media screen and (max-width: 767px){
        height: auto;
      }
      img{
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: top center;
        transition: all 0.5s ease;
      }
    }
    .card-body{
      padding: 25px 40px;
      transition: all 0.3s linear;
      height: 100%;
      @media screen and (max-width: 992px){
        background-color: #FFF;
        height: auto;
      }
      .post-date{
        font-size: 11px;
        color: #77C2A1;
        margin-bottom: 15px;
      }
      .card-title{
        // font-size: 18px;
        font-weight: 700;
        line-height: 1.6 !important;
        max-height: 60px;
        margin-bottom: 15px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        @media screen and (max-width: 768px){
          font-size: 14px;
        }
      }
      .card-text{
        font-size: 14px;
        font-weight: normal;
        line-height: 2 !important;
        max-height: 56px;
        margin-bottom: 18px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        @media screen and (max-width: 768px){
          -webkit-appearance: none;
          font-size: 11px;
          max-height: 40px;
        }
      }
      .arrow-link{
        font-size: 0;
        background: transparent;
        border: 0;
        @media screen and (max-width: 992px){
          &:after{
            opacity: 1 !important;
          }
        }
        &:after{
          content: '';
          display: inline-flex;
          margin-left: 0;
          width: 30px;
          height: 24px;
          background: transparent;
          background-image: url(../images/arrow-right-green.svg);
          background-repeat: no-repeat;
          background-size: contain;
          background-position: center;
          opacity: 0;
          transition: all 0.3s linear;
        }
      }
    }
    &:hover{
      .image-wrapper{
        img{
          transform: scale(1.05);
        }
      }
      .card-body{
        background-color: #F2F2F2;
        .arrow-link{
          background: transparent !important;
          border: 0;
          &::after {
            opacity: 1;
          }
        }
      }
    }
  }
}

@media (min-width:1199px){
  .post-list-owl{
    .owl-carousel{
      display: block;
    }
    .owl-stage{
      width: 100% !important;
      transform: unset !important;
      columns: 3;
      -webkit-columns: 3;
      -moz-columns: 3;
    }
    .owl-nav,
    .owl-dots{
      display: none;
      visibility: hidden;
    }
  }
}
@media (min-width:992px) and (max-width: 1199px){
  .post-list-owl{
    .owl-stage{
      width: 100% !important;
      transform: unset !important;
      columns: 2;
      -webkit-columns: 2;
      -moz-columns: 2;
    }
    .owl-nav,
    .owl-dots{
      display: none;
      visibility: hidden;
    }
  }
}
@media (max-width: 991px){
  .post-list-owl{
    .number-repeater-item-warp{
      width: 253px;
      margin-bottom: 0px;
    }
    .owl-stage-outer{
      margin-bottom: 39px;
    }
    .owl-nav{
      display: block;
      visibility: visible;
      text-align: left;
      margin: 0;
      padding: 0;
      .owl-prev,
      .owl-next{
        width: 50px;
        height: 50px;
        background-color: rgba(104, 194, 159, 0.2);
        border-radius: 100%;
        opacity: 1;
        position: relative;
        padding: 0;
        margin-right: 8px;
        span{
          width: 30px;
          height: 30px;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          right: 0;
          margin: 0 auto;
          background-repeat: no-repeat;
          background-size: cover;
        }
      }
      .owl-prev{
        span{
          background-image: url('../images/arrow-left-green.svg');
        }
      }
      .owl-next{
        span{
          background-image: url('../images/arrow-right-green.svg');
        }
      }
      [class*=owl-]:hover{
        background-color: rgba(104, 194, 159, 0.2);
      }
    }
  }
}

.news-result{
  .button-area{
      padding-top: 80px;
      text-align: center;
      @media screen and (max-width: 767px){
        // display: none;
        padding-top: 0px;
      }
  }
}
