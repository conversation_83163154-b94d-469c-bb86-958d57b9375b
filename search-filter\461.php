<?php
/**
 * Search & Filter Pro 
 *
 * Sample Results Template
 * 
 * @package   Search_Filter
 * <AUTHOR>
 * @link      https://searchandfilter.com
 * @copyright 2018 Search & Filter
 * 
 * Note: these templates are not full page templates, rather 
 * just an encaspulation of the your results loop which should
 * be inserted in to other pages by using a shortcode - think 
 * of it as a template part
 * 
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs 
 * and using template tags - 
 * 
 * http://codex.wordpress.org/Template_Tags
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $query->have_posts() )
{
	wp_enqueue_script('search-filter');
	?>
	<div class="loop-grid team-items row" uk-scrollspy="target: > div; cls: uk-animation-slide-bottom; delay: 200">
	<?php
	while ($query->have_posts())
	{
		$query->the_post();
		?>
		<div class="col-6 col-sm-6 col-md-3 team-item">
			<div class="card h-100">
                <?php
                $logo = get_field('logo');
                ?>
				<div class="card-photo">
                    <?php echo get_the_post_thumbnail( get_the_ID(), 'full'); ?>
				</div>
				<div class="card-body">
                    <?php if(get_field('full_name')): ?>
                        <h3><?php echo get_field('full_name') ?></h3>
                    <?php else: ?>
                        <h3><?php the_title(); ?></h3>
                    <?php endif; ?>

                    <?php if(get_field('position')): ?>
                        <p><?php echo nl2br(get_field('position')) ?></p>
                    <?php endif; ?>
				</div>
				<div class="card-footer">
					<p class="no-margin">
                        <?php if(get_field('linkedin')): ?>
                            <a href="<?=get_field('linkedin')?>" rel="noreferrer nofollow" target="_blank"><span class="sow-icon-fontawesome sow-fab" data-sow-icon="" aria-hidden="true"></span>
                        <?php endif; ?></a>
                    </p>
				</div>
			</div>
		</div>
		<?php
	}
	?>
	</div>

	
	<?php
}
else
{
	//echo "No Results Found";
	echo "<div class='no-results-text'>";
	_e('Ingen resultater', 'radar_fd_child');
	echo "</div>";
}
?>