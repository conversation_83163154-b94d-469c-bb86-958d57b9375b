jQuery(document).ready(function($){
    let scrollPosition;
    $('#mobile-menu').on('show.bs.collapse', function(){
        $('.site-header').addClass('active-navbar');
        $('body').addClass('active-navbar');
        scrollPosition = $(window).scrollTop();
        $('body').css({
            position: 'fixed',
            top: -1 * scrollPosition
        });
    });
    $('#mobile-menu').on('hide.bs.collapse', function(){
        $('.site-header').removeClass('active-navbar');
        $('body').removeClass('active-navbar');
        $('html,body').animate({scrollTop: scrollPosition}, 100);
        $('body').css({
            position: '',
            top: ''
        });
        
        $('.navbar-mobile .dropdown-menu, .navbar-mobile .sub-menu').removeClass('show');
        $('.navbar-mobile .dropdown-toggle, .navbar-mobile li.dropdown > a, .navbar-mobile li.menu-item-has-children > a').removeClass('active').attr('aria-expanded', 'false');
    });

    $('.header-search-form-mobile-icon').on('click', function(e){
        e.preventDefault();
        $(this).closest('.header-search').toggleClass('active');
    });
    
    $('.navbar-mobile .dropdown-toggle, .navbar-mobile li.dropdown > a, .navbar-mobile li.menu-item-has-children > a').on('click', function(e){
        const $this = $(this);
        const $parentLi = $this.closest('li');
        let $dropdownMenu = $this.next('.dropdown-menu, .sub-menu');
        
        if ($dropdownMenu.length === 0) {
            $dropdownMenu = $parentLi.find('.dropdown-menu, .sub-menu').first();
        }
        
        if ($dropdownMenu.length === 0) {
            return;
        }
        
        // Calculate if click was on the icon area (right 30px of the link)
        const linkRect = this.getBoundingClientRect();
        const clickX = e.clientX;
        const iconAreaStart = linkRect.right - 30; // 30px from right edge to account for icon
        
        // Only prevent default and toggle dropdown if clicking in icon area
        if (clickX >= iconAreaStart) {
            e.preventDefault();
            e.stopPropagation();
            
            const $currentLevel = $this.closest('.navbar-mobile, .dropdown-menu, .sub-menu');
            $currentLevel.find('.dropdown-menu.show, .sub-menu.show').not($dropdownMenu).not($dropdownMenu.find('.dropdown-menu, .sub-menu')).removeClass('show');
            $currentLevel.find('.dropdown-toggle.active, li.dropdown > a.active, li.menu-item-has-children > a.active').not($this).removeClass('active').attr('aria-expanded', 'false');
        
            $dropdownMenu.toggleClass('show');
            $this.toggleClass('active');

            const isExpanded = $dropdownMenu.hasClass('show');
            $this.attr('aria-expanded', isExpanded);
            
            if (!isExpanded) {
                $dropdownMenu.find('.dropdown-menu, .sub-menu').removeClass('show');
                $dropdownMenu.find('.dropdown-toggle, li.dropdown > a, li.menu-item-has-children > a').removeClass('active').attr('aria-expanded', 'false');
            }
        }
        // If click is outside icon area, allow normal link behavior (navigation)
    });
    
    $(document).on('click', function(e){
        if (!$(e.target).closest('.navbar-mobile .dropdown, .navbar-mobile li.menu-item-has-children').length) {
            $('.navbar-mobile .dropdown-menu, .navbar-mobile .sub-menu').removeClass('show');
            $('.navbar-mobile .dropdown-toggle, .navbar-mobile li.dropdown > a, .navbar-mobile li.menu-item-has-children > a').removeClass('active').attr('aria-expanded', 'false');
        }
    });
});