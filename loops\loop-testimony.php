<?php
if (have_posts()){
	$instance = SiteOrigin_Panels_Widgets_PostLoop::get_current_loop_instance();
	?><div class="row row-cols-1 list testimony-list"><?php
	while (have_posts()){
		the_post();
		get_template_part( 'loops/loop-template/testimony');
	};
	?></div><?php
	if($instance['more']){
		global $wp_query; 
		wp_localize_script( 'post-loop-load-more', 'post_loop_load_more_params', array(
			'ajaxurl' => site_url() . '/wp-admin/admin-ajax.php',
			'posts' => json_encode( $wp_query->query_vars ),
			'current_page' => get_query_var( 'paged' ) ? get_query_var('paged') : 1,
			'max_page' => $wp_query->max_num_pages
		) );
		wp_enqueue_script( 'post-loop-load-more' );
		?>
		<div class="loadmore-section text-center" data-aos="fade-up" data-aos-offset="200">
			<a class="loadmore btn btn-primary">
				<?php _e('Load more', 'radar_child_fd'); ?>
				<div class="spinner-border spinner-border-sm"></div>
			</a>
		</div>
		<?php
	}
}
?>
<?php wp_reset_postdata(); ?>
