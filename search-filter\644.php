<?php
/**
 * Search & Filter Pro 
 *
 * Sample Results Template
 * 
 * @package   Search_Filter
 * <AUTHOR>
 * @link      https://searchandfilter.com
 * @copyright 2018 Search & Filter
 * 
 * Note: these templates are not full page templates, rather 
 * just an encaspulation of the your results loop which should
 * be inserted in to other pages by using a shortcode - think 
 * of it as a template part
 * 
 * This template is an absolute base example showing you what
 * you can do, for more customisation see the WordPress docs 
 * and using template tags - 
 * 
 * http://codex.wordpress.org/Template_Tags
 *
 */

// If this file is called directly, abort.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( $query->have_posts() )
{
	?>
	<div class="loop-grid partners-items row" uk-scrollspy="target: > div; cls: uk-animation-slide-bottom; delay: 200">
	<?php
	while ($query->have_posts())
	{
		$query->the_post();
		?>
		<div class="col-12 col-sm-6 col-md-3 partners-item">
			<div class="card">
                <?php
                $logo = get_field('logo');
                ?>
				<div class="card-photo">
                    <a href="<?php the_permalink();?>"><img src="<?=$logo['url']?>" /></a>
				</div>
				<div class="card-body">
					<?php if(get_field('company_name')): ?>
                        <a href="<?php the_permalink();?>" class="text-link"><h3><?php echo get_field('company_name') ?></h3></a>
                    <?php else: ?>
                        <a href="<?php the_permalink();?>" class="text-link"><h3><?php the_title(); ?></h3></a>
                    <?php endif; ?>

                    <?php if(get_field('company_name')): ?>
                        <p><?php echo nl2br(get_field('address')) ?></p>
                    <?php endif; ?>

                    <p class="no-margin">
                        <?php if(get_field('phone')): ?>
                            <span><?=__('Phone:', 'radar_fd_child')?>
                                <a href="tel:<?=get_field('phone')?>"><?=get_field('phone')?></a>
                            </span><br />
                        <?php endif; ?>
                        <?php if(get_field('email')): ?>
                            <span><?=__('Email:', 'radar_fd_child')?>
                                <a href="mailto:<?=get_field('email')?>"><?=get_field('email')?></a>
                            </span><br />
                        <?php endif; ?>
                        <?php if(get_field('website')): ?>
                            <span><?=__('Website:', 'radar_fd_child')?>
                                <a href="<?=get_field('website')?>" rel="nofollow noreferrer" target="_blank"><?=str_replace(['http:','https:','/'],'',get_field('website'))?></a>
                            </span>
                        <?php endif; ?>
                    </p>
				</div>
			</div>
		</div>
		<?php
	}
	?>
	</div>

	
	<?php
}
else
{
	//echo "No Results Found";
	echo "<div class='no-results-text'>";
	_e('Ingen resultater', 'radar_fd_child');
	echo "</div>";
}
?>