<?php
get_header();
?>
	<div class="container news-singlepage">
		<?php while ( have_posts() ) : the_post(); ?>

			<!-- Post Header -->
			<div class="row toppage">
				<div class="col-12">
					<div class="backbutton"><a href="<?php echo get_permalink( get_page_by_path( 'news' ) ); ?>" class="btn-back"><?php _e('Back', 'radar_child_fd'); ?></a></div>
				</div>
			</div>
			<div class="row toppage last">
				<div class="col-12">
					<?php
					$primary_term = yoast_get_primary_term( 'news_categories', $post->ID );
					if($primary_term){
						?>
							<h3>] <?php echo $primary_term; ?> [</h3>
						<?php
					}
					?>
				</div>
			</div>

			<!-- Post title & content -->
			<div class="row postcontent">
				<div class="col-xs-12 col-md-4 col-lg-3">
					<?php echo "<h4 class='section-headline pd-left-col'>".get_the_date("d.m.Y")."</h4>"; ?>
				</div>
				<div class="col-xs-12 col-md-8 col-lg-6">
					<div id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
						<h2 class="entry-title">
							<?php 
							echo truncate_html(get_the_title(),85); 
							?>
						</h2>
					</div>	

					<div class="entry-content">
							<?php the_content(); ?>
					</div>
				</div>
			</div>
				
			<?php if(get_field('image_caption')){ ?>
				<!-- Post Thumbnail -->
				<?php $thumbnailurl = get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>
				<?php if($thumbnailurl){ ?>
				<div class="row postthumbnail">
					<div class="col-xs-12 col-md-4 col-lg-3 order-2 order-md-1">
						<div class="caption pd-left-col">
							<?php
								echo get_field('image_caption'); 
							?>
						</div>
					</div>
					<div class="col-xs-12 col-md-8 col-lg-6 order-1 order-md-2">
						<img class="thumbnail-img" src="<?=$thumbnailurl?>">
					</div>
				</div>
				<?php } ?>
			<?php } ?>

			<?php
			if(get_field('testimony')){
				$testimony_single = get_post(get_field('testimony'));
				?>
				<!-- Post Testinomy -->
				<div class="row posttestinomy">
					<div class="col-xs-12 col-md-4 col-lg-12">
						
					</div>
					<div class="col-xs-12 col-md-8 col-lg-12">
						<div class="testimony-single">
							<div class="content-warp">
								<div class="content">
									<?php 
									if(function_exists('truncate_html')){
										echo truncate_html($testimony_single->post_content,176,''); 
									}else{
										echo $testimony_single->post_content;
									}
									?>
								</div>
							</div>
							<div class="person">
								<div class="name"><?php echo get_field('name', $testimony_single->ID); ?></div>
								<div class="position"><?php echo get_field('organisation', $testimony_single->ID); ?></div>
								<div class="company"><?php echo get_field('company_name', $testimony_single->ID); ?></div>
							</div>
						</div>
					</div>
				</div>
				<?php
			}
			?>

			<!-- Post Video -->
			<?php if(get_field('video_popup_url')){ ?>
			<div class="row postvideo">
				<div class="col-xs-12 col-md-4 col-lg-3">
					
				</div>
				<div class="col-xs-12 col-md-8 col-lg-6">
					<h2 class="entry-title">
						<?php
							echo get_field('video_title'); 
						?>
					</h2>
				</div>
			</div>
			<div class="row postvideo">
				<div class="col-xs-12 col-md-4 col-lg-3 order-2 order-md-1">
					<div class="caption pd-left-col">
						<?php
							echo get_field('video_caption'); 
						?>
					</div>
				</div>
				<div class="col-xs-12 col-md-8 col-lg-6 order-1 order-md-2">
					<div id="#videoContainer" class="videocontainer">
						<?php 
							$youtubevideo_code = wp_oembed_get( get_field('video_popup_url') );
							$youtubeID = getYouTubeVideoId(get_field('video_popup_url'));
							$videoThumbnail = 'https://img.youtube.com/vi/' . $youtubeID . '/hqdefault.jpg'; // SIZE
						?>
						<!-- Button trigger modal -->
						<button id="video-overlay" style="background-image:url('<?php echo $videoThumbnail; ?>')" data-toggle="modal" data-target="#VideoModal">
						</button>
						
						<!-- Modal -->
						<div class="modal fade" id="VideoModal" tabindex="-1" aria-labelledby="VideoModalLabel" aria-hidden="true">
						<div class="modal-dialog modal-dialog-centered modal-lg">
							<div class="modal-content">
							<div class="modal-body">
								<button type="button" class="close" data-dismiss="modal" aria-label="Close">
									<span aria-hidden="true">&times;</span>
								</button>
								<?php echo $youtubevideo_code; ?>
							</div>
							</div>
						</div>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>

			<!-- Post Podcast -->
			<?php 
				$audioFile = get_field('audio_file'); 
				// Extract variables.
				$urlAudioFile = $audioFile['url'];
				$titleAudioFile = $audioFile['title'];
				$captionAudioFile = $audioFile['caption'];
				$artistAudioFile = $audioFile['artist'];
			
			if ($urlAudioFile!=""){
			?>
			<div class="row postpodcast">
				<div class="col-xs-12 col-md-4 col-lg-3">
					<h4 class="section-headline pd-left-col"><?php _e('Listen to our Podcast', 'radar_child_fd'); ?></h4>
				</div>
				<div class="col-xs-12 col-md-8 col-lg-6">
					<div class="">
						<div class="audio-player">
							<div class="album-image" style="background-image: url('https://artwork-cdn.7static.com/static/img/sleeveart/00/051/614/0005161476_350.jpg')"></div>
							<div id="play-btn"></div>
							<div class="audio-wrapper" id="player-container" href="javascript:;">
							<audio id="player" ontimeupdate="initProgressBar()">
									<source src="<?php echo $urlAudioFile; ?>" type="audio/mp3">
									</audio>
							</div>
							<div class="player-controls scrubber">
							<p>
								<span class="audiotitle"><?php echo $titleAudioFile; ?></span>
								<span class="audiosubtitle"><?php echo $captionAudioFile; ?></span>
								
							</p>
							<small style="float: left; position: absolute; left: 20px; bottom: 10px;" class="start-time"></small>
							<!-- <small style="float: left; position: relative; left: 40px;" class="end-time"></small> -->
							<br>
							<span id="seekObjContainer">
									<progress id="seekObj" value="0" max="1"></progress>
							</span>
							

							</div>
							
						</div>

					
					</div>
				</div>
			</div>
			<!-- Audio player script-->
			<script>
				function calculateTotalValue(length) {
					var minutes = Math.floor(length / 60),
					seconds_int = length - minutes * 60,
					seconds_str = seconds_int.toString(),
					seconds = seconds_str.substr(0, 2),
					time = minutes + ':' + seconds
				
					return time;
				}
				
				function calculateCurrentValue(currentTime) {
					var current_hour = parseInt(currentTime / 3600) % 24,
					current_minute = parseInt(currentTime / 60) % 60,
					current_seconds_long = currentTime % 60,
					current_seconds = current_seconds_long.toFixed(),
					current_time = (current_minute < 10 ? "0" + current_minute : current_minute) + ":" + (current_seconds < 10 ? "0" + current_seconds : current_seconds);
				
					return current_time;
				}
				
				function initProgressBar() {
					var player = document.getElementById('player');
					var length = player.duration
					var current_time = player.currentTime;
				
					// calculate total length of value
					var totalLength = calculateTotalValue(length)
					jQuery(".end-time").html(totalLength);
				
					// calculate current value time
					var currentTime = calculateCurrentValue(current_time);
					jQuery(".start-time").html(currentTime);
				
					var progressbar = document.getElementById('seekObj');
					progressbar.value = (player.currentTime / player.duration);
					progressbar.addEventListener("click", seek);
				
					if (player.currentTime == player.duration) {
						jQuery('#play-btn').removeClass('pause');
					}
				
					function seek(evt) {
					var percent = evt.offsetX / this.offsetWidth;
					player.currentTime = percent * player.duration;
					progressbar.value = percent / 100;
					}
				};
				
				function initPlayers(num) {
					// pass num in if there are multiple audio players e.g 'player' + i
				
					for (var i = 0; i < num; i++) {
					(function() {
				
						// Variables
						// ----------------------------------------------------------
						// audio embed object
						var playerContainer = document.getElementById('player-container'),
						player = document.getElementById('player'),
						isPlaying = false,
						playBtn = document.getElementById('play-btn');
				
						// Controls Listeners
						// ----------------------------------------------------------
						if (playBtn != null) {
						playBtn.addEventListener('click', function() {
							togglePlay()
						});
						}
				
						// Controls & Sounds Methods
						// ----------------------------------------------------------
						function togglePlay() {
						if (player.paused === false) {
							player.pause();
							isPlaying = false;
							jQuery('#play-btn').removeClass('pause');
				
						} else {
							player.play();
							jQuery('#play-btn').addClass('pause');
							isPlaying = true;
						}
						}
					}());
					}
				}
				
				initPlayers(jQuery('#player-container').length);
			</script>
			<?php 
			} //end check null file.
			?>
			<!--End Audio player script-->

			<!-- Post Download -->
			<?php
			// Check repeater rows exists.
			if( have_rows('download_file_repeater') ){
			?>
			<div class="row postdownload">
				<div class="col-xs-12 col-md-4 col-lg-3">
					<h4 class="section-headline pd-left-col"><?php _e('Downloads', 'radar_child_fd'); ?></h4>
				</div>
				<div class="col-xs-12 col-md-8 col-lg-9">
					<div class="download-items">
						<?php 
						while( have_rows('download_file_repeater') ) : the_row();
						// Load sub field value.
						$sub_value  = get_sub_field('download_files');
							// Extract variables.
							$urlDownloadFile = $sub_value['url'];
							$titleDownloadFile = $sub_value['title'];
							if ($urlDownloadFile!==""){
								?>
								<div class="download-item">
									<a href="<?php echo $urlDownloadFile; ?>" class="itembox" download><?php echo $titleDownloadFile; ?></a>
								</div>
								<?php 
							}
						// End loop.
						endwhile;
						wp_reset_query(); ?>
					</div>
				</div>
			</div>
			<?php
			}// End check repeater rows exists.
			?>
		<?php endwhile; ?>
	</div>

<?php get_footer(); ?>
