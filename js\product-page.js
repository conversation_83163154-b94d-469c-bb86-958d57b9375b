jQuery(function($){

    function add_toggle_arrow(){
        var $window = $(window);
        if($window.width() <= 991){
            $('.single-products .productTabMenu .nav .nav-item:first-child a').remove('.toggle-arrow');
            $('.single-products .productTabMenu .nav .nav-item:first-child a').append('<span class="toggle-arrow"></span>');
            $('.single-products .productTabMenu .nav .nav-item:first-child a .toggle-arrow').on('click', function(e){
                e.preventDefault();
                $(this).closest('.nav').toggleClass('active');
            });
        }
    }

    function disableActiveProductTabMenu(){
        var $window = $(window);
        if($window.width() > 991){
            $('.single-products .productTabMenu .nav').removeClass('active');
        }
    }

    add_toggle_arrow();
    disableActiveProductTabMenu();
    //$(window).resize(disableActiveProductTabMenu);
    var id_toggle_arrow;
    var id_disableactive;
    $(window).resize(function() {
            clearTimeout(id_toggle_arrow);
            id_toggle_arrow = setTimeout(add_toggle_arrow, 500);

            clearTimeout(id_disableactive);
            id_disableactive = setTimeout(disableActiveProductTabMenu, 500);
    });
    /*
    $( document ).ready(function() {
        if ($('.productTabMenu').css('background-color') == "rgb(165, 179, 210)") //blue
        {
            $('.product-content-head h3.product-subtitle').css('color','#1D418E'); 
            $('.product-content-head h3.product-subtitle span').addClass('blue'); 
        }

        else if($('.productTabMenu').css('background-color') == 'rgb(188, 178, 211)') //purple
        {
            $('.product-content-head h3.product-subtitle').css('color','#8F7FB6'); 
            $('.product-content-head h3.product-subtitle span').addClass('purple'); 
        }
    });
	*/

});