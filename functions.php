<?php

function enqueue_parent_theme_style() {

	global $radar_fd_option;

	if($radar_fd_option['enabled-sticky-header']==1){

		wp_enqueue_script('sticky-header',get_stylesheet_directory_uri().'/js/sticky-header.js',array('jquery'),'',true);

	}



	//wp_enqueue_style( 'poppins', 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap' );

	wp_enqueue_style( 'century-gothic', get_stylesheet_directory_uri().'/css/font-century-gothic.css' );

	wp_enqueue_style( 'parent-style', get_stylesheet_directory_uri() .'/css/styles.css' );



	//audio script

	//wp_enqueue_script( 'audioscript_mp3', get_stylesheet_directory_uri().'/js/audioplayer_script.js');


	// Font Awesome for mobile menu arrows
	wp_enqueue_style( 'font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0' );
	

	wp_enqueue_script( 'scroll-parallax-image', get_stylesheet_directory_uri().'/js/scroll-parallax-image.js');



	wp_enqueue_script( 'navbar-mobile', get_stylesheet_directory_uri().'/js/navbar-mobile.js');

	wp_register_script( 'product-page', get_stylesheet_directory_uri().'/js/product-page.js');

	wp_enqueue_script( 'add-read-more', get_stylesheet_directory_uri().'/js/add-read-more.js');

	wp_localize_script('add-read-more', 'readmore', array(

		'readmore_text' => __('Read More', 'radar_child_fd'),

		'readless_text' => __('Read Less', 'radar_child_fd')

	));

	wp_register_script( 'search-filter', get_stylesheet_directory_uri().'/js/search-filter.js');

	wp_localize_script( 'search-filter', 'search_filter_val', array( 

		'viewmore_text' => __('View more', 'radar_child_fd'),

		'viewless_text' => __('View less', 'radar_child_fd') 

	));

	wp_register_script( 'resource-library-search', get_stylesheet_directory_uri().'/js/resource-library-search.js');

	wp_localize_script( 'resource-library-search', 'search_filter_val', array( 

		'loadmore_text' => __('Load more', 'radar_child_fd'),

		'target_categories_tax' => get_target_categories_tax(),

		'target_families_tax' => get_target_families_tax(),

	));



	wp_register_script( 'news-filter', get_stylesheet_directory_uri().'/js/news-filter.js');

	wp_localize_script( 'news-filter', 'new_filter_val', array( 

		'loadmore_text' => __('View more', 'radar_child_fd'),

	));



	//OWL post list

	wp_register_script( 'owl-carousel', get_stylesheet_directory_uri().'/js/owl-carousel/js/owl.carousel.min.js' );

	wp_register_script( 'owl-carousel-post-list', get_stylesheet_directory_uri().'/js/owl-post-list.js' );

	wp_register_style( 'card-carousel', get_stylesheet_directory_uri().'/js/owl-carousel/styles/owl.carousel.min.css' );

	wp_register_style( 'card-carousel-theme', get_stylesheet_directory_uri().'/js/owl-carousel/styles/owl.theme.default.min.css' );

	wp_enqueue_script('owl-carousel');

	wp_enqueue_script('owl-carousel-post-list');

	wp_enqueue_style('card-carousel');

	wp_enqueue_style('card-carousel-theme');

	//Slick Slider for Two Cards Row
	wp_register_style( 'slick-carousel-css', '//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css' );
	wp_register_script( 'slick-carousel-js', '//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery') );
	wp_register_script( 'two-card-slider', get_stylesheet_directory_uri().'/js/two-card-slider.js', array('jquery', 'slick-carousel-js') );
	
	wp_enqueue_style('slick-carousel-css');
	wp_enqueue_script('slick-carousel-js');
	wp_enqueue_script('two-card-slider');

	//OWL scroll bar

	//wp_enqueue_script( 'scrollbar', get_stylesheet_directory_uri().'/js/jquery.scrollbar.min.js' );

	

	// jQuery Expander

	wp_register_script( 'jquery-expander', 'https://cdnjs.cloudflare.com/ajax/libs/jquery-expander/1.7.0/jquery.expander.min.js' );

	wp_register_script( 'resource-library-expander', get_stylesheet_directory_uri().'/js/resource-library-expander.js' );

	wp_localize_script( 'resource-library-expander', 'resource_expander_val', array( 

		'viewmore_text' => __('View more', 'radar_child_fd'),

		'viewless_text' => __('View less', 'radar_child_fd') 

	));



	// fancybox

	// wp_register_script( 'fancybox', 'https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js' );

	// wp_register_style( 'fancybox', 'https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css' );

}

add_action( 'wp_enqueue_scripts', 'enqueue_parent_theme_style', 20);



function radar_child_fd_setup(){

	register_nav_menus( array(

        'top-menu' => __( 'Top Menu', 'radar_child_fd' ),

        'footer-menu-2' => __( 'Footer Menu 2', 'radar_child_fd' ),

		'mobile-top-menu' => __( 'Mobile Top Menu', 'radar_child_fd' ),

        'mobile-top-menu-2' => __( 'Mobile Top Menu 2', 'radar_child_fd' ),

	) );

}

add_action( 'after_setup_theme', 'radar_child_fd_setup' );



function radar_child_fd_widgets_init() {

	register_sidebar( array(

		'name'          => __( 'Footer Subscribe newsletter', 'radar_child_fd' ),

		'id'            => 'footer-subscribe-newsletter',

		'description'   => '',

		'before_widget' => '<div id="%1$s" class="widget %2$s">',

		'after_widget'  => '</div>',

		'before_title'  => '<h3 class="widget-title">',

		'after_title'   => '</h3>',

	) );

	register_sidebar( array(

		'name'          => __( 'Product contact widget', 'radar_child_fd' ),

		'id'            => 'product-contact-widget',

		'description'   => '',

		'before_widget' => '<div id="%1$s" class="widget %2$s">',

		'after_widget'  => '</div>',

		'before_title'  => '<h3 class="widget-title">',

		'after_title'   => '</h3>',

	) );

}

add_action( 'widgets_init', 'radar_child_fd_widgets_init', 30 );





if(is_plugin_active('siteorigin-panels/siteorigin-panels.php')){

	if( file_exists(get_stylesheet_directory().'/inc/page-builder-hooks-styles.php') ){

		require_once get_stylesheet_directory() . '/inc/page-builder-hooks-styles.php';

	}

}



if( file_exists(get_stylesheet_directory().'/inc/resource-library-search.php') ){

	require_once get_stylesheet_directory() . '/inc/resource-library-search.php';

}



if( file_exists(get_stylesheet_directory().'/inc/target-database-filter.php') ){

	require_once get_stylesheet_directory() . '/inc/target-database-filter.php';

}



add_post_type_support( 'page', 'excerpt' );



if(is_plugin_active('contact-form-7/wp-contact-form-7.php') && file_exists(get_stylesheet_directory().'/inc/contact-form.php') ){

	require get_stylesheet_directory() . '/inc/contact-form.php';

}



function truncate_html($html, $length = 100, $ending = '...')

{

    $html = str_replace("&nbsp;", "", $html);

	if (!is_string($html)) {

		trigger_error('Function \'truncate_html\' expects argument 1 to be an string', E_USER_ERROR);

		return false;

	}



	if (mb_strlen(strip_tags($html)) <= $length) {

		return $html;

	}

	$total = 0; //mb_strlen($ending);

	$open_tags = array();

	$return = '';

	$finished = false;

	$final_segment = '';

	$self_closing_elements = array(

		'area',

		'base',

		'br',

		'col',

		'frame',

		'hr',

		'img',

		'input',

		'link',

		'meta',

		'param'

	);

	$inline_containers = array(

		'a',

		'b',

		'abbr',

		'cite',

		'em',

		'i',

		'kbd',

		'span',

		'strong',

		'sub',

		'sup'

	);

	while (!$finished) {

		if (preg_match('/^<(\w+)[^>]*>/', $html, $matches)) { // Does the remaining string start in an opening tag?

			// If not self-closing, place tag in $open_tags array:

			if (!in_array($matches[1], $self_closing_elements)) {

				$open_tags[] = $matches[1];

			}

			// Remove tag from $html:

			$html = substr_replace($html, '', 0, strlen($matches[0]));

			// Add tag to $return:

			$return .= $matches[0];

		} elseif (preg_match('/^<\/(\w+)>/', $html, $matches)) { // Does the remaining string start in an end tag?

			// Remove matching opening tag from $open_tags array:

			$key = array_search($matches[1], $open_tags);

			if ($key !== false) {

				unset($open_tags[$key]);

			}

			// Remove tag from $html:

			$html = substr_replace($html, '', 0, strlen($matches[0]));

			// Add tag to $return:

			$return .= $matches[0];

		} else {

			// Extract text up to next tag as $segment:

			if (preg_match('/^([^<]+)(<\/?(\w+)[^>]*>)?/', $html, $matches)) {

				$segment = $matches[1];

				// Following code taken from https://trac.cakephp.org/browser/tags/1.2.1.8004/cake/libs/view/helpers/text.php?rev=8005.

				// Not 100% sure about it, but assume it deals with utf and html entities/multi-byte characters to get accureate string length.

				$segment_length = mb_strlen(preg_replace('/&[0-9a-z]{2,8};|&#[0-9]{1,7};|&#x[0-9a-f]{1,6};/i', ' ', $segment));

				// Compare $segment_length + $total to $length:

				if ($segment_length + $total > $length) { // Truncate $segment and set as $final_segment:

					$remainder = $length - $total;

					$entities_length = 0;

					if (preg_match_all('/&[0-9a-z]{2,8};|&#[0-9]{1,7};|&#x[0-9a-f]{1,6};/i', $segment, $entities, PREG_OFFSET_CAPTURE)) {

						foreach($entities[0] as $entity) {

							if ($entity[1] + 1 - $entities_length <= $remainder) {

								$remainder--;

								$entities_length += mb_strlen($entity[0]);

							} else {

								break;

							}

						}

					}

					// Otherwise truncate $segment and set as $final_segment:

					$finished = true;

					$final_segment = mb_substr($segment, 0, $remainder + $entities_length);

				} else {

					// Add $segment to $return and increase $total:

					$return .= $segment;

					$total += $segment_length;

					// Remove $segment from $html:

					$html = substr_replace($html, '', 0, strlen($segment));

				}

			} else {

				$finshed = true;

			}

		}

	}

	// Check for spaces in $final_segment:

	if (strpos($final_segment, ' ') === false && preg_match('/<(\w+)[^>]*>$/', $return)) { // If none and $return ends in an opening tag: (we ignore $final_segment)

		// Remove opening tag from end of $return:

		$return = preg_replace('/<(\w+)[^>]*>$/', '', $return);

		// Remove opening tag from $open_tags:

		$key = array_search($matches[3], $open_tags);

		if ($key !== false) {

			unset($open_tags[$key]);

		}

	} else { // Otherwise, truncate $final_segment to last space and add to $return:

		// $spacepos = strrpos($final_segment, ' ');

		$return .= mb_substr($final_segment, 0, mb_strrpos($final_segment, ' '));

	}

	$return = trim($return);

	$len = strlen($return);

	$last_char = substr($return, $len - 1, 1);

	if (!preg_match('/[a-zA-Z0-9]/', $last_char)) {

		$return = substr_replace($return, '', $len - 1, 1);

	}

	// Add closing tags:

	$closing_tags = array_reverse($open_tags);

	if(count($closing_tags) > 0){

		$ending_added = false;

		foreach($closing_tags as $tag) {

			if (!in_array($tag, $inline_containers) && !$ending_added) {

				$return .= $ending;

				$ending_added = true;

			}

			$return .= '</' . $tag . '>';

		}

	}elseif (mb_strlen(strip_tags($html)) > $length){

		$return .= $ending;

	}



	return $return;

}



function post_loop_load_more_scripts() {

	wp_register_script( 'post-loop-load-more', get_stylesheet_directory_uri() . '/js/post-loop-load-more.js', array('jquery') );

}

add_action( 'wp_enqueue_scripts', 'post_loop_load_more_scripts' );



function post_loop_load_more_handler(){

	$args = json_decode( stripslashes( $_POST['query'] ), true );

	$args['paged'] = $_POST['page'] + 1;

	$args['post_status'] = 'publish';



	query_posts( $args );



	if( have_posts() ) :

		while( have_posts() ): the_post();

			get_template_part( 'loops/loop-template/'.$args['post_type']);

		endwhile;

	endif;

	die;

}

add_action('wp_ajax_post_loop_load_more', 'post_loop_load_more_handler');

add_action('wp_ajax_nopriv_post_loop_load_more', 'post_loop_load_more_handler');



//Get Youtbue url thumbnail

function getYouTubeVideoId($pageVideUrl) {

    $link = $pageVideUrl;

    $video_id = explode("?v=", $link);

    if (!isset($video_id[1])) {

        $video_id = explode("youtu.be/", $link);

    }

    $youtubeID = $video_id[1];

    if (empty($video_id[1])) $video_id = explode("/v/", $link);

    $video_id = explode("&", $video_id[1]);

    $youtubeVideoID = $video_id[0];

    if ($youtubeVideoID) {

        return $youtubeVideoID;

    } else {

        return false;

    }

}



add_filter( 'body_class', 'theme_custom_body_class' );

function theme_custom_body_class($classes){

	$new_class = is_page() ? get_post_meta( get_the_ID(), 'body_class', true ) : null;



	if ( $new_class ) {

		$classes[] = $new_class;

	}



	return $classes;

}



function sf_change_results_url($url,  $sfid){

	if($sfid == '2618'){

		$url = 'http://localhost/nanion/products/cardioexcyte-96/resources/';

	}

	return $url;

}

//add_filter( 'sf_results_url', 'sf_change_results_url', 20, 2 );

//add_filter( 'sf_ajax_results_url', 'sf_change_results_url', 20, 2 );

//add_filter( 'sf_ajax_form_url', 'sf_change_results_url', 20, 2 );



/* Disable WordPress Admin Bar for all users */

add_filter( 'show_admin_bar', '__return_false' );



function theme_additional_active_item_classes($classes = array(), $menu_item = false){

	global $post;

	$menu_post = get_post($menu_item->object_id);

	

	if ($menu_post &&  $menu_post->post_name == 'news' && is_singular('news') ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'products' && is_singular('products') ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'products' && is_singular('products') ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'products' && $post->post_name == 'automated-patch-clamp' ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'products' && $post->post_name == 'membrane-biophysics' ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'products' && $post->post_name == 'cell-monitoring' ) {

		$classes[] = 'current-menu-ancestor';

	}



	if ($menu_post &&  $menu_post->post_name == 'careers' && is_singular('job-position') ) {

		$classes[] = 'current-menu-ancestor';

	}

	

	return $classes;

}

add_filter( 'nav_menu_css_class', 'theme_additional_active_item_classes', 10, 2 );



function theme_add_menu_attrs( $atts, $item, $args ){

	$atts['data-text'] = $item->title;

	return $atts;

}

add_filter( 'nav_menu_link_attributes', 'theme_add_menu_attrs', 10, 3 );



//================================================== replace file PDF

add_filter( 'sanitize_file_name', 'theme_override_pdf', 10, 1 );

function theme_override_pdf( $name ) 

{

	$ext = pathinfo($name, PATHINFO_EXTENSION);

	//if($ext == 'pdf'){

		global $pdf_fild_post_global;

		$pdf_fild_post_global = array();

		$args = array(

			'numberposts' => -1,

			'post_type' => 'attachment',

			'meta_query' => array(

				array( 

					'key' => '_wp_attached_file',

					'value' => $name,

					'compare' => 'LIKE'

				)

			)

		);

		$attachments_to_remove = get_posts( $args );



		foreach( $attachments_to_remove as $attach ){

			$args_pdf = array(

				'numberposts' => -1,

				'post_type' => 'any',

				'meta_query' => array(

					array( 

						'key' => 'file',

						'value' => $attach->ID,

						'compare' => '='

					)

				),

				'fields' => 'ids'

			);

			$pdf_fild_post = get_posts( $args_pdf );

			$pdf_fild_post_global = array_merge($pdf_fild_post_global, $pdf_fild_post);

			wp_delete_attachment( $attach->ID, true );

			// error_log('theme_override_pdf');

			// error_log(print_r($pdf_fild_post_global, true));

		}

	//}



    return $name;

}



add_filter( 'wp_update_attachment_metadata', 'theme_override_pdf_metadata', 10, 2 );

function theme_override_pdf_metadata($data, $attachment_id){

	global $pdf_fild_post_global;

	// error_log('theme_override_pdf_metadata');

	// error_log(print_r($pdf_fild_post_global, true));

	if(!empty($pdf_fild_post_global)){

		foreach ($pdf_fild_post_global as $resource_id) {

			update_post_meta( $resource_id, 'file', $attachment_id );

		}

		$pdf_fild_post_global = null;

	}

	return $data;

}

//================================================== replace file PDF



/*-------- Product single Check color ----------*/

function add_my_script() {

    return "<script>

				if ($('.productTabMenu').css('background-color') == '#a5b3d2')

				{

					$('.product-content-head h3.product-subtitle').css('color',''); 

				}else if{

				}

            </script>";

}  

 add_shortcode( 'myCustomShortCode', 'add_my_script' );



// Include custom Mega Menu Walker

if ( ! class_exists( 'Custom_Mega_Menu_Walker' ) && class_exists( 'Mega_Menu_Walker' ) ){

	require_once get_stylesheet_directory() . '/inc/custom-mega-menu-walker.php';

}