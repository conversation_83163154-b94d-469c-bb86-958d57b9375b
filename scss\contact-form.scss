@import "compass";

.wpcf7,
.salesforce-form{
  .validate{
    strong{
      &.wpcf7-not-valid{
        color: #DE4668;
      }
    }
  }
  input[type="text"], input[type="email"], input[type="url"], input[type="tel"]{
    width: 100%;
    max-width: 100%;
    color: #000;
    padding: 0 12px;
    height: 40px;
    border: 1px solid #878787;
    border-radius: 0px;
    background: transparent;
    font-size: 14px;
    transition: all 0.3s ease;
    background-image: unset !important;
    background-size: 16px !important;
    background-position: right 12px center !important;
    background-repeat: no-repeat !important;
    &:focus,
    &:focus-visible{
      outline: none;
      border-radius: 0px;
      box-shadow: 0px 0px 0px;
      border: 3px solid #ADDAC7;
    }
    &::-webkit-input-placeholder { /* Edge */
      color: #CFCFCF;
    }
    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: #CFCFCF;
    }
    &::placeholder {
      color: #CFCFCF;
    }
    &.wpcf7-not-valid{
      color: #DE4668 !important;
      border: 1px solid #DE4668 !important;
      background-image: url(../images/warning_circle.svg) !important;
      &::-webkit-input-placeholder {
        color: #CFCFCF !important;
      }
      &:-ms-input-placeholder {
        color: #CFCFCF !important;
      }
      &::placeholder {
        color: #CFCFCF !important;
      }
    }
    &.wpcf7-valid{
      font-weight: 100 !important;
      border: 1px solid #77C2A1 !important;
    }
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active{
      -webkit-box-shadow: 0 0 0 30px white inset !important;
    }
  }
  .wpcf7-not-valid-tip{
    display: block !important;
    position: relative;
    top: 5px;
    left: 0;
    text-align: left;
    font-size: 11px;
    line-height: 1;
    z-index: 100;
    border: 0;
    background: transparent;
    padding: 0;
    width: 100%;
  }
  .dnd-upload-status{
    .dnd-upload-details{
      .name{
        color: #77C2A1;
      }
      .remove-file{
        span:after{
          margin-top: 7px;
        }
      }
    }
    .dnd-progress-bar{
      span{
        background: #77C2A1;
      }
    }
    &.complete{
      .dnd-progress-bar{
        display: none;
      }
    }
  }

  .bottom-section{
    margin: 30px 0 0 20px;
    @media screen and (max-width: 560px){
      margin: 30px 0 0 5px;
    }
    .acceptance{
      .wpcf7-list-item{
        margin: 0 0 0 30px;
        font-size: 14px;
        color: #000;
        a{
          text-decoration: underline;
          color: #000;
        }
      }
    }
    /* Customize the label (the container) */
    .mc4wp-checkbox label,
    .wpcf7-list-item label {
      display: block;
      position: relative;
      margin-bottom: 0;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      a{
        @include transition-property(all);
        @include transition-duration(0.5s);
        @include transition-timing-function(ease-in-out);
        &:hover{
          color: #000;
        }
      }
    }

    /* Hide the browser's default checkbox */
    .mc4wp-checkbox label input,
    .wpcf7-list-item label input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }

    /* Create a custom checkbox */
    .mc4wp-checkbox label span::before,
    .wpcf7-list-item-label::before {
      content: '';
      position: absolute;
      top: 0;
      left: -30px;
      height: 18px;
      width: 18px;
      background-color: transparent;
      border: 1px solid #000;
      border-radius: 2px;
    }

    /* On mouse-over, add a grey background color */
    .mc4wp-checkbox label:hover input ~ span,
    .wpcf7-list-item label:hover input ~ .wpcf7-list-item-label {
      background-color: transparent;
    }

    /* When the checkbox is checked, add a blue background */
    .mc4wp-checkbox label input:checked ~ span::before,
    .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::before {
      background-color: transparent;
      border: 1px solid #000;
    }

    /* Create the checkmark/indicator (hidden when not checked) */
    .mc4wp-checkbox span:after,
    .wpcf7-list-item-label:after {
      content: "";
      position: absolute;
      display: none;
    }

    /* Show the checkmark when checked */
    .mc4wp-checkbox label input:checked ~ span::after,
    .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::after {
      display: block;
    }

    /* Style the checkmark/indicator */
    .mc4wp-checkbox label span:after,
    .wpcf7-list-item label .wpcf7-list-item-label:after {
      left: -24px;
      top: 2px;
      width: 5px;
      height: 10px;
      border: solid #000;
      border-width: 0 1px 1px 0;
      -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      transform: rotate(45deg);
    }
    .btn-primary{
      margin-top: 30px;
    }
    .wpcf7-spinner{
      display: none;
    }
  }
  textarea{
    width: 100%;
    max-width: 100%;
    color: #000;
    padding: 0 12px;
    height: 74px;
    border: 1px solid #878787;
    border-radius: 0px;
    background: transparent;
    font-size: 14px;
    &:focus,
    &:focus-visible{
      outline: none;
      border-radius: 0px;
      box-shadow: 0px 0px 0px;
    }
    &::-webkit-input-placeholder { /* Edge */
      color: #CFCFCF;
    }
    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: #CFCFCF;
    }
    &::placeholder {
      color: #CFCFCF;
    }
    &.wpcf7-valid{
      font-weight: 100 !important;
      border: 1px solid #77C2A1 !important;
    }
  }

  .success-message{
    margin: -1rem -3rem;
    @media screen and (max-width: 560px){
      margin: -1rem -1rem;
    }
    p{
      font-size: 24px;
      line-height: 1.2;
      @media screen and (max-width: 560px){
        font-size: 20px;
      }
    }
    .tn-secondary.close-popup{
      margin-top: 40px;
      justify-content: center;
      @media screen and (max-width: 560px){
        margin-top: 20px;
      }
    }
    .green-line {   
      width: 100%;
      height: 1px;
      background: linear-gradient(151deg, rgba(255, 255, 255, 0) 0%, #29c17a 100%);
      margin: 20px 0px 60px 0px;
    }  
  }

  .text-box{
    margin-bottom: 42px;
    .wpcf7-form-control-wrap{
      input{
        width: 100%;
        max-width: 100%;
        color: #000;
        padding: 0 12px;
        height: 40px;
        border: 1px solid #878787;
        border-radius: 0px;
        background: transparent;
        font-size: 14px;
        &:focus,
        &:focus-visible{
          outline: none;
          border-radius: 0px;
          box-shadow: 0px 0px 0px;
        }
        &::-webkit-input-placeholder { /* Edge */
          color: #CFCFCF;
        }
        &:-ms-input-placeholder { /* Internet Explorer 10-11 */
          color: #CFCFCF;
        }
        &::placeholder {
          color: #CFCFCF;
        }
      }

    }
  }
  .select-box{
    margin-bottom: 42px;
    .wpcf7-form-control-wrap{
      .select2-container{
        width: 100% !important;
        .selection{
          display: block;
          height: 40px;
          .select2-selection{
            height: 40px;
            border: 1px solid #878787;
            border-radius: 0;
            transition: all 0.3s ease;
            &.wpcf7-valid{
              border: 1px solid #77C2A1 !important;
              .select2-selection__rendered{
                color: #000;
              }
            }
            .select2-selection__rendered{
              height: 100%;
              line-height: 1.4;
              align-items: center;
              display: flex;
              color: #CFCFCF;
            }
            .select2-selection__arrow{
              height: 100%;
              line-height: 1.4;
              align-items: center;
              display: flex;
              b{
                background-color: transparent;
                border: 0;
              }
              &:after{
                content: '';
                display: block;
                position: absolute;
                top: 50%;
                right: 20%;
                transform: translate(-50%,-50%);
                width: 14px;
                height: 13px;
                background-image: url(../images/dropdown-arrow.svg);
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                transition: all 0.1s ease;
              }
            }
          }
        }
        &.select2-container--open{
          .selection{
            .select2-selection{
              height: 40px;
              border: 3px solid #ADDAC7;
              border-radius: 0;
              .select2-selection__arrow{
                &:after{
                  background-image: url(../images/dropdown-arrow-up.svg);
                }
              }
            }
          }
        }
      }
    }
  }
  .codedropz-upload-handler{
    cursor: pointer;
    padding: 0;
    background-color: transparent;
    border: 1px dashed #000;
    border-radius: 0;
    .codedropz-upload-container{
      display: none;
      .codedropz-upload-inner{

      }
      .codedropz-btn-wrap{
        display: none;
      }
    }
    .dnd-upload-counter{
      display: none;
    }
    .dummy-text{
      height: 72px;
      text-align: left;
      padding: 10px;
      h3{
        font-size: 18px;
        font-weight: 400;
        color: #3D4764;
        line-height: 1.2;
      }
      p{
        font-size: 14px;
        color: #C4BFC0;
        line-height: 1.2;
      }
    }
  }
  .wpcf7-acceptance{
    .wpcf7-list-item{
      margin: 0;
      font-size: 11px;
      color: #3D4763;
      padding-top: 24px;
      label{
        input[type="checkbox"]{
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;
          &:checked{
            ~ span.wpcf7-list-item-label{
              &:before{
                background-color: #77C2A1;
                border: 1px solid #77C2A1;
              }
              &:after{
                display: block;
                position: absolute;
                left: 6px;
                top: 0px;
                width: 6px;
                height: 10px;
                border: solid #FFF;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
              }
            }
          }
        }
        span.wpcf7-list-item-label{
          position: relative;
          padding: 0 0 0 30px;
          align-items: center;
          display: inline-block;
          &:before{
            cursor: pointer;
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            transform: translate(0,-50%);
            height: 18px;
            width: 18px;
            background-color: transparent;
            border: 1px solid #878787;
            border-radius: 2px;
          }
          .readmore{
            padding-top: 10px;
            padding-bottom: 15px;
            display: block;
          }
        }
      }
      a{
        text-decoration: none;
        color: #000;
        transition: all 0.2s ease;
        &:hover{
          color: #77C2A1;
          -webkit-text-fill-color: #77C2A1;
          -webkit-text-stroke-width: 1px;
          -webkit-text-stroke-color: #77C2A1;
        }
      }
    }
  }
  .btn-primary{
    min-width: 120px;
    margin-top: 4px;
  }
  .subscription-form{
    .text-box{
      @media screen and (max-width: 991.9px) {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 15px;
      }
    }
    .wpcf7-acceptance{
      .wpcf7-list-item{
        padding-top: 0;
      }
    }
  }
}

.select2-container--open{
  .select2-dropdown--below,.select2-dropdown--above{
    padding-top: 32px;
    background: transparent;
    border: 0;
    .select2-results{
      background-color: #FFF;
      ul.select2-results__options{
        li{
          padding-left: 12px;
          border-bottom: 1px solid #878787;
          &.select2-results__option--highlighted{
            background-color: #E4F3EC;
            color: #000;
          }
          &.select2-results__option--selected{
            background-color: #E4F3EC;
            &:after{
              content: '';
              display: inline-block;
              width: 14px;
              height: 13px;
              background-image: url(../images/checked.svg);
              background-size: contain;
              background-repeat: no-repeat;
              background-position: center;
              position: relative;
              margin-left: 25px;
            }
          }
        }
      }
    }
  }
}
.subscription-button{
  min-width: 256px;
  min-height: 58px;
  @media screen and (max-width: 501px) {
    max-width: 60%;
    min-width: unset !important;
  }
}