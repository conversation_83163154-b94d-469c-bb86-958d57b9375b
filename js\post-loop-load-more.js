jQuery(function($){
	$('.loadmore').click(function(){
        var list = $('.list');
		var button = $(this),
		    data = {
			'action': 'post_loop_load_more',
			'query': post_loop_load_more_params.posts,
			'page' : post_loop_load_more_params.current_page
		};
 
		$.ajax({
			url : post_loop_load_more_params.ajaxurl,
			data : data,
			type : 'POST',
			beforeSend : function ( xhr ) {
				button.find('.spinner-border').show();
			},
			success : function( data ){
                button.find('.spinner-border').hide();
				if( data ) { 
                    list.append(data);

					post_loop_load_more_params.current_page++;
 
					if ( post_loop_load_more_params.current_page == post_loop_load_more_params.max_page ){
                        button.remove();
                    }
				} else {
					button.remove();
				}
			}
		});
	});
});