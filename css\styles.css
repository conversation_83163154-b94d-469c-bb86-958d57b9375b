html, body {
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Century Gothic', sans-serif;
  letter-spacing: 0.1px;
  color: #000000;
  padding-top: 80px;
}
body.active-navbar {
  width: 100%;
}

@media (min-width: 1200px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl {
    max-width: 1260px;
  }
}
a,
button {
  outline: 0 !important;
}
a:hover, a:visited, a:active,
button:hover,
button:visited,
button:active {
  outline: 0 !important;
}

.form-control:focus {
  box-shadow: 0px 0px 0px;
}

.site-header {
  width: 100%;
  height: auto;
  position: fixed;
  z-index: 9999999;
  opacity: 1;
  top: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
@media screen and (max-width: 991px) {
  .site-header {
    margin-top: -1px;
    height: auto !important;
    min-height: unset;
    background-color: #ffffff;
    box-shadow: 0px 1px 3px 2px rgba(0, 0, 0, 0.06);
  }
  .site-header.hide {
    background: unset;
    box-shadow: unset;
  }
}
.site-header .site-header-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  background-color: #ffffff;
  box-shadow: 0px 1px 3px 2px rgba(0, 0, 0, 0.06);
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
@media screen and (max-width: 991px) {
  .site-header .site-header-bg {
    display: none;
  }
}
.site-header.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  opacity: 1;
  z-index: 999999;
}
.site-header.hide {
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
.site-header.hide .site-header-bg {
  height: 0px;
}
.site-header.hide .navbar {
  opacity: 0;
}
.site-header.hide .childTabMenu {
  opacity: 0;
}
.site-header.hide .productTabMenu {
  opacity: 0;
}
.site-header .header-logo a {
  display: inline-block;
}
.site-header .header-logo img {
  max-width: 100%;
  max-height: 43px;
}
.site-header .navbar {
  padding: 15px 0px 0px;
  opacity: 1;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
@media screen and (max-width: 991px) {
  .site-header .navbar {
    padding: 11px 0px;
  }
}
.site-header .navbar #primary-menu .search-form .form-group {
  width: 100%;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="search"],
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"] {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  width: auto;
  display: inline-block;
  vertical-align: middle;
  color: #000000;
  border: 1px solid #878787;
  outline: none;
  -moz-border-radius: 0px / 0px;
  -webkit-border-radius: 0px 0px;
  border-radius: 0px / 0px;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="search"]::-webkit-input-placeholder,
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"]::-webkit-input-placeholder {
  /* Edge */
  color: #CFCFCF;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="search"]:-ms-input-placeholder,
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"]:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #CFCFCF;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="search"]::placeholder,
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"]::placeholder {
  color: #CFCFCF;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="search"]:focus,
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"]:focus {
  border: 2px solid #77C2A1;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"] {
  min-width: 130px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
  padding-top: 0px;
  padding-bottom: 0px;
  background: #77C2A1;
  border: 2px solid #77C2A1;
}
.site-header .navbar #primary-menu .search-form .form-group input[type="submit"]:hover {
  color: #77C2A1;
  background: #ffffff;
  border: 2px solid #77C2A1;
}
.site-header .navbar-brand {
  padding: 0px;
  display: inline-block;
}
.site-header .navbar-brand img {
  max-width: 100%;
  max-height: 43px;
}
@media screen and (max-width: 991px) {
  .site-header .navbar-brand img {
    height: 27px;
  }
}
.site-header .top-bar .sub-menu {
  height: 100%;
}
.site-header .navbar-desktop {
  width: 100%;
  text-align: right;
}
.site-header #top-menu {
  margin-bottom: 10px;
}
.site-header ul.top-menu {
  margin: 0;
  padding: 0;
  line-height: 100%;
}
.site-header ul.top-menu li {
  list-style: none;
}
.site-header ul.top-menu > li {
  position: relative;
}
.site-header ul.top-menu > li:last-child a {
  margin-right: 0px;
}
.site-header ul.top-menu > li:last-child a::before {
  display: none;
}
.site-header ul.top-menu > li > a {
  font-size: 11px;
  line-height: 100%;
  color: #878787;
  margin-right: 25px;
  display: inline-block;
}
.site-header ul.top-menu > li > a::before {
  display: block;
  content: attr(data-text);
  font-weight: bold;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  letter-spacing: 1px;
}
.site-header ul.top-menu > li:hover > a,
.site-header ul.top-menu > li.current_page_item > a,
.site-header ul.top-menu > li.current_page_parent > a,
.site-header ul.top-menu > li.current-page-ancestor > a,
.site-header ul.top-menu > li.current-menu-ancestor > a,
.site-header ul.top-menu > li.current-cat > a,
.site-header ul.top-menu > ul > li.current-menu-item > a {
  text-decoration: none;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  color: #77C2A1;
  font-weight: bold;
}
.site-header ul.top-menu > li.menu-item-has-children > a::after {
  content: '';
  width: 11px;
  height: 6px;
  display: inline-block;
  vertical-align: top;
  margin-top: 4px;
  margin-left: 3px;
  border: 0px;
  background: url("../images/language-switch-arrow-down.svg") no-repeat center center;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.top-menu > li.menu-item-has-children:hover > a::after {
  transform: rotate(180deg);
  filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%);
}
.site-header ul.top-menu ul.sub-menu {
  display: none;
  position: absolute;
}
.site-header ul.top-menu > li:hover ul.sub-menu {
  display: block;
}
.site-header ul.top-menu ul.dropdown-menu,
.site-header ul.top-menu ul.sub-menu {
  width: 200px;
  margin: 0;
  padding: 0;
  left: 0;
  top: 18px;
  z-index: 1000;
  border: 0px;
  background-color: #ffffff;
  box-shadow: 1px 0px 3px rgba(0, 0, 0, 0.1);
  -moz-border-radius: 0px / 0px;
  -webkit-border-radius: 0px 0px;
  border-radius: 0px / 0px;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.top-menu ul.dropdown-menu li,
.site-header ul.top-menu ul.sub-menu li {
  list-style: none;
  position: relative;
  border-bottom: 1px solid #E4F3EC;
}
.site-header ul.top-menu ul.dropdown-menu li a,
.site-header ul.top-menu ul.sub-menu li a {
  font-size: 11px;
  line-height: 100%;
  color: #878787;
  text-transform: initial;
  text-align: left;
  display: block;
  padding: 10px 10px;
  border-bottom: 0px;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.top-menu ul.dropdown-menu li a:hover,
.site-header ul.top-menu ul.dropdown-menu li.current-menu-item a,
.site-header ul.top-menu ul.sub-menu li a:hover,
.site-header ul.top-menu ul.sub-menu li.current-menu-item a {
  text-decoration: none;
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children {
  padding-bottom: 15px;
  margin-bottom: -15px;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu {
  max-width: 43px;
  background: #FFFFFF;
  box-shadow: 1px 0px 3px rgba(0, 0, 0, 0.1);
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu li {
  min-width: unset;
  border-bottom: 1px solid #E4F3EC;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu li:last-child {
  border-bottom: 0px;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu li a {
  font-size: 11px;
  line-height: 16px;
  padding: 7px 10px;
  display: inline-block;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu li a::before {
  display: block;
  content: attr(data-text);
  font-weight: bold;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  letter-spacing: 1px;
}
.site-header ul.top-menu li.wpml-ls-item.menu-item-has-children ul.sub-menu li a:hover {
  color: #77C2A1;
  font-weight: bold;
  -webkit-text-stroke-width: 0px;
}
.site-header .mega-menu-wrap {
  display: flex !important;
  justify-content: flex-end;
}
.site-header ul.max-mega-menu {
  margin: 0;
  padding: 0;
}
.site-header ul.max-mega-menu li {
  list-style: none;
}
.site-header ul.max-mega-menu li.mega-current-menu-item > a {
  color: #77C2A1 !important;
  -webkit-text-stroke: 1px #77C2A1 !important;
}
.site-header ul.max-mega-menu > li {
  position: relative;
}
.site-header ul.max-mega-menu > li:last-child {
  margin-right: 0px !important;
}
.site-header ul.max-mega-menu > li {
  margin-right: 68px !important;
}
.site-header ul.max-mega-menu > li > a {
  font-size: 14px;
  line-height: 100%;
  color: #000000;
  position: relative;
  padding: 0px 0px;
  display: inline-block;
  position: relative;
}
.site-header ul.max-mega-menu > li > a::before {
  display: block;
  content: attr(data-text);
  font-weight: bold;
  height: 0;
  overflow: hidden;
  visibility: hidden;
  letter-spacing: 0.2px;
}
.site-header ul.max-mega-menu > li::before, .site-header ul.max-mega-menu > li::after {
  content: '';
  width: 6px;
  height: 18px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.max-mega-menu > li:hover > a .link-title {
  color: #77C2A1;
  -webkit-text-stroke: 1px #77C2A1 !important;
}
.site-header ul.max-mega-menu > li:hover > a .link-title::before {
  display: inline-block !important;
  width: 6px;
  height: 18px;
  position: absolute;
  content: '';
  background: url(../images/menu-hover-left.svg) no-repeat left center;
  left: -5px;
  top: 11px;
}
.site-header ul.max-mega-menu > li:hover > a .link-title::after {
  display: inline-block !important;
  width: 6px;
  height: 18px;
  position: absolute;
  content: '';
  background: url("../images/menu-hover-right.svg") no-repeat right center;
  right: -10px;
  top: 11px;
}
.site-header ul.max-mega-menu > li.mega-current_page_ancestor > a, .site-header ul.max-mega-menu > li.mega-current-menu-item > a {
  color: #77C2A1 !important;
  -webkit-text-stroke: 1px #77C2A1 !important;
}
.site-header ul.max-mega-menu > li.mega-current_page_ancestor > a .link-title, .site-header ul.max-mega-menu > li.mega-current-menu-item > a .link-title {
  color: #77C2A1;
}
.site-header ul.max-mega-menu > li.mega-current_page_ancestor > a .link-title::before, .site-header ul.max-mega-menu > li.mega-current-menu-item > a .link-title::before {
  display: inline-block !important;
  width: 6px;
  height: 18px;
  position: absolute;
  content: '';
  background: url(../images/menu-hover-left.svg) no-repeat left center;
  left: -5px;
  top: 11px;
}
.site-header ul.max-mega-menu > li.mega-current_page_ancestor > a .link-title::after, .site-header ul.max-mega-menu > li.mega-current-menu-item > a .link-title::after {
  display: inline-block !important;
  width: 6px;
  height: 18px;
  position: absolute;
  content: '';
  background: url("../images/menu-hover-right.svg") no-repeat right center;
  right: -10px;
  top: 11px;
}
.site-header ul.max-mega-menu > li.mega-menu-grid > .mega-sub-menu {
  margin: 0 -5px !important;
  width: calc(100% + 10px) !important;
  padding: 10px 10px !important;
}
.site-header ul.max-mega-menu > li.mega-menu-grid > .mega-sub-menu .mega-indicator {
  float: none !important;
  margin-left: 7px !important;
  top: 10px !important;
}
.site-header ul.max-mega-menu > li.mega-menu-grid > .mega-sub-menu .mega-indicator::after {
  background: url(../images/language-switch-arrow-down.svg) no-repeat center center !important;
  content: '' !important;
  width: 11px !important;
  height: 6px !important;
}
.site-header ul.max-mega-menu > li.mega-menu-grid > .mega-sub-menu .mega-menu-item .mega-indicator:hover::after {
  filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%) !important;
}
.site-header ul.max-mega-menu > li.mega-menu-grid > .mega-sub-menu .mega-menu-item.mega-toggle-on .mega-indicator::after {
  transform: rotate(180deg) !important;
  filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%) !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu > li.mega-menu-row .mega-menu-column > ul.mega-sub-menu > li.mega-menu-item {
  padding-bottom: 15px !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu > li.mega-menu-row .mega-menu-column > ul.mega-sub-menu > li.mega-menu-item > .mega-menu-link {
  line-height: 1.2 !important;
  border-bottom: 1px solid #77c2a1 !important;
  padding-bottom: 15px !important;
  margin-bottom: 15px !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu li.mega-menu-column > ul.mega-sub-menu ul.mega-sub-menu > li.mega-menu-item {
  padding-bottom: 15px !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu li.mega-menu-column > ul.mega-sub-menu ul.mega-sub-menu ul.mega-sub-menu {
  margin-left: 30px !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu li.mega-menu-column > ul.mega-sub-menu ul.mega-sub-menu ul.mega-sub-menu > li.mega-menu-item {
  padding-bottom: 6px !important;
}
.site-header ul.max-mega-menu > li > ul.mega-sub-menu li.mega-menu-column > ul.mega-sub-menu ul.mega-sub-menu ul.mega-sub-menu > li.mega-menu-item > .mega-menu-link > .link-title {
  font-size: 13px !important;
}
.site-header ul.max-mega-menu > li.mega-menu-flyout > ul.mega-sub-menu > li.mega-menu-item {
  border-bottom: 1px solid #77c2a1 !important;
}
.site-header ul.max-mega-menu > li.mega-menu-flyout > ul.mega-sub-menu > li.mega-menu-item:last-child {
  border-bottom: 0px !important;
}
.site-header ul.max-mega-menu > li:hover > a,
.site-header ul.max-mega-menu > li.current_page_item > a,
.site-header ul.max-mega-menu > li.current_page_parent > a,
.site-header ul.max-mega-menu > li.current-page-ancestor > a,
.site-header ul.max-mega-menu > li.current-menu-ancestor > a,
.site-header ul.max-mega-menu > li.current-cat > a,
.site-header ul.max-mega-menu > ul > li.mega-current-menu-item > a {
  text-decoration: none;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  color: #77C2A1;
  -webkit-text-stroke: 1px #77C2A1;
}
.site-header ul.max-mega-menu > li:hover::before, .site-header ul.max-mega-menu > li:hover::after,
.site-header ul.max-mega-menu > li.current_page_item::before,
.site-header ul.max-mega-menu > li.current_page_item::after,
.site-header ul.max-mega-menu > li.current_page_parent::before,
.site-header ul.max-mega-menu > li.current_page_parent::after,
.site-header ul.max-mega-menu > li.current-page-ancestor::before,
.site-header ul.max-mega-menu > li.current-page-ancestor::after,
.site-header ul.max-mega-menu > li.current-menu-ancestor::before,
.site-header ul.max-mega-menu > li.current-menu-ancestor::after,
.site-header ul.max-mega-menu > li.current-cat::before,
.site-header ul.max-mega-menu > li.current-cat::after,
.site-header ul.max-mega-menu > ul > li.mega-current-menu-item::before,
.site-header ul.max-mega-menu > ul > li.mega-current-menu-item::after {
  opacity: 1;
}
.site-header ul.max-mega-menu ul.sub-menu {
  display: none;
  position: absolute;
}
.site-header ul.max-mega-menu > li:hover ul.sub-menu {
  display: block;
}
.site-header ul.max-mega-menu ul.dropdown-menu,
.site-header ul.max-mega-menu ul.sub-menu {
  min-width: 300px;
  margin: 0;
  padding: 0;
  margin-left: 1px;
  z-index: 1000;
  border: 0px;
  -moz-border-radius: 0px / 0px;
  -webkit-border-radius: 0px 0px;
  border-radius: 0px / 0px;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.max-mega-menu ul.dropdown-menu li,
.site-header ul.max-mega-menu ul.sub-menu li {
  list-style: none;
  min-width: 260px;
  position: relative;
}
.site-header ul.max-mega-menu ul.dropdown-menu li a,
.site-header ul.max-mega-menu ul.sub-menu li a {
  text-transform: uppercase;
  text-align: left;
  display: block;
  padding: 10px 20px;
  border-bottom: 0px;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.site-header ul.max-mega-menu ul.dropdown-menu li a:hover,
.site-header ul.max-mega-menu ul.dropdown-menu li.current-menu-item a,
.site-header ul.max-mega-menu ul.sub-menu li a:hover,
.site-header ul.max-mega-menu ul.sub-menu li.current-menu-item a {
  text-decoration: none;
}
.site-header .header-search {
  line-height: 100%;
  position: relative;
}
.site-header .header-search .search-icon {
  font-size: 12px;
  line-height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-left: 15px;
  color: #000000;
}
.site-header .header-search .search-icon:not(.collapsed), .site-header .header-search .search-icon:hover, .site-header .header-search .search-icon:active {
  filter: invert(74%) sepia(39%) saturate(323%) hue-rotate(101deg) brightness(89%) contrast(88%);
}
.site-header .header-search .header-search-form {
  width: 210px;
  position: absolute;
  bottom: -40px;
  right: 0px;
  background-color: #ffffff;
  border: 1px solid #77c2a1;
  z-index: 1;
}
.site-header .header-search .header-search-form form {
  padding: 0px;
}
.site-header .header-search .header-search-form input[type="submit"] {
  display: none;
}
.site-header .header-search .header-search-form input[type="search"] {
  height: 30px;
  border: 0px;
  font-size: 14px;
  -moz-border-radius: 0px / 0px;
  -webkit-border-radius: 0px 0px;
  border-radius: 0px / 0px;
}

.site-content {
  position: relative;
  overflow: hidden;
  font-size: 14px;
  line-height: 1.6;
}
@media screen and (max-width: 770px) {
  .site-content .container {
    padding: 0 20px;
  }
}
.site-content h1 {
  font-size: 64px;
  line-height: 70px;
  /*line-height: 1.2 !important;*/
  /*color: #000;*/
}
.site-content h2 {
  font-size: 40px;
  line-height: 48px;
  /*line-height: 1.2 !important;*/
  /*color: #000;*/
}
.site-content h3 {
  font-size: 32px;
  line-height: 42px;
  /*line-height: 1.2 !important;*/
  /*color: #000;*/
}
.site-content h4 {
  font-size: 18px;
  line-height: 28px;
  /*line-height: 1.4 !important;*/
  /*color: #000;*/
}
.site-content h5 {
  font-size: 14px;
  line-height: 26px;
  /*line-height: 2 !important;*/
  /*color: #000;*/
}
.site-content h6 {
  font-size: 14px;
  line-height: 26px;
  /*line-height: 2 !important;*/
  /*color: #000;*/
}
.site-content a {
  color: #77C2A1;
  text-decoration: underline;
}
.site-content a:visited {
  color: #77C2A1;
}
.site-content a:hover, .site-content a:active {
  color: #ADDAC7;
}
.site-content a img.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}
.site-content a img.alignnone {
  margin: 5px 20px 20px 0;
}
.site-content a img.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}
.site-content a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
@media screen and (max-width: 991.9px) {
  .site-content h1 {
    font-size: 32px;
    line-height: 42px;
  }
  .site-content h2 {
    font-size: 28px;
    line-height: 36px;
  }
  .site-content h3 {
    font-size: 24px;
    line-height: 34px;
  }
  .site-content h4 {
    font-size: 18px;
    line-height: 28px;
  }
}
@media screen and (max-width: 501px) {
  .site-content h1 {
    font-size: 32px;
    line-height: 42px;
  }
  .site-content h2 {
    font-size: 28px;
    line-height: 36px;
  }
  .site-content h3 {
    font-size: 24px;
    line-height: 34px;
  }
  .site-content h4 {
    font-size: 18px;
    line-height: 28px;
  }
}
.site-content .aligncenter,
.site-content div.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}
.site-content img {
  max-width: 100%;
  height: auto;
}
.site-content .textwidget ul,
.site-content .job-response .entry-content ul,
.site-content .news-singlepage .entry-content ul {
  list-style: none;
  padding: 0px;
}
.site-content .textwidget ul li,
.site-content .job-response .entry-content ul li,
.site-content .news-singlepage .entry-content ul li {
  line-height: 2;
  padding-left: 20px;
  position: relative;
}
.site-content .textwidget ul li::before,
.site-content .job-response .entry-content ul li::before,
.site-content .news-singlepage .entry-content ul li::before {
  content: '';
  width: 8px;
  height: 8px;
  position: absolute;
  top: 10px;
  left: 0;
  background: url("../images/li-bullet.svg");
}
.site-content .wp-caption {
  background: #fff;
  border: 1px solid #f0f0f0;
  max-width: 96%;
  /* Image does not overflow the content area */
  padding: 5px 3px 10px;
  text-align: center;
}
.site-content .wp-caption.alignnone {
  margin: 5px 20px 20px 0;
}
.site-content .wp-caption.alignleft {
  margin: 5px 20px 20px 0;
}
.site-content .wp-caption.alignright {
  margin: 5px 0 20px 20px;
}
.site-content .wp-caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 98.5%;
  padding: 0;
  width: auto;
}
.site-content .wp-caption p.wp-caption-text {
  font-size: 11px;
  line-height: 17px;
  margin: 0;
  padding: 0 4px 5px;
}
.site-content .screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  white-space: nowrap;
  height: 1px;
  width: 1px;
  overflow: hidden;
}
.site-content .screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #21759b;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
  /* Above WP toolbar. */
}
.site-content .gallery {
  margin: 0 auto 18px;
}
.site-content .gallery .gallery-item {
  float: left;
  margin-top: 0;
  text-align: center;
  width: 33%;
}
.site-content .gallery img {
  box-shadow: 0px 0px 4px #999;
  border: 1px solid white;
  padding: 8px;
  background: #f2f2f2;
}
.site-content .gallery img:hover {
  background: white;
}
.site-content .gallery .gallery-caption {
  color: #888;
  font-size: 12px;
  margin: 0 0 12px;
}
.site-content .gallery dl, .site-content .gallery dt {
  margin: 0;
}
.site-content .gallery br + br {
  display: none;
}
.site-content .gallery-columns-2 .gallery-item {
  width: 50%;
}
.site-content .gallery-columns-2 .attachment-medium {
  max-width: 92%;
  height: auto;
}
.site-content .gallery-columns-4 .gallery-item {
  width: 25%;
}
.site-content .gallery-columns-4 .attachment-thumbnail {
  max-width: 84%;
  height: auto;
}
.site-content .bg-left {
  position: relative;
}
.site-content .bg-left::after {
  left: -999em;
  content: '';
  display: block;
  position: absolute;
  width: 999em;
  top: 0;
  bottom: 0;
  background-color: inherit;
  z-index: -1;
}
@media screen and (max-width: 991.9px) {
  .site-content .bg-left {
    padding-left: 2px;
    padding-right: 2px;
  }
  .site-content .bg-left::after {
    width: calc(999em + 2px);
  }
  .site-content .bg-left::before {
    right: -999em;
    content: '';
    display: block;
    position: absolute;
    width: calc(999em + 2px);
    top: 0;
    bottom: 0;
    background-color: inherit;
  }
}
.site-content .bg-right {
  position: relative;
}
.site-content .bg-right::after {
  right: -999em;
  content: '';
  display: block;
  position: absolute;
  width: calc(999em + 2px);
  top: 0;
  bottom: 0;
  background-color: inherit;
  z-index: -1;
}
@media screen and (max-width: 991.9px) {
  .site-content .bg-right {
    padding-left: 2px;
    padding-right: 2px;
  }
  .site-content .bg-right::before {
    left: -999em;
    content: '';
    display: block;
    position: absolute;
    width: calc(999em + 1px);
    top: 0;
    bottom: 0;
    background-color: inherit;
  }
  .site-content .bg-right::after {
    width: calc(999em + 2px);
  }
}
.site-content .bg-left-bg-right {
  position: relative;
}
@media screen and (max-width: 991.9px) {
  .site-content .bg-left-bg-right {
    padding-left: 2px;
    padding-right: 2px;
  }
}
.site-content .bg-left-bg-right::before {
  left: -999em;
  content: '';
  display: block;
  position: absolute;
  width: 999em;
  top: 0;
  bottom: 0;
  background-color: inherit;
  z-index: -1;
}
@media screen and (max-width: 991.9px) {
  .site-content .bg-left-bg-right::before {
    width: calc(999em + 2px);
  }
}
.site-content .bg-left-bg-right::after {
  right: -999em;
  content: '';
  display: block;
  position: absolute;
  width: 999em;
  top: 0;
  bottom: 0;
  background-color: inherit;
  z-index: -1;
}
@media screen and (max-width: 991.9px) {
  .site-content .bg-left-bg-right::after {
    width: calc(999em + 2px);
  }
}
.site-content .all-font-bold h1, .site-content .all-font-bold h2, .site-content .all-font-bold h3, .site-content .all-font-bold h4, .site-content .all-font-bold h5, .site-content .all-font-bold h6, .site-content .all-font-bold p {
  font-weight: 700 !important;
}
.site-content .specifications-list {
  width: 810px;
  max-width: 100%;
  margin: 0 auto;
}
.site-content .specifications-list .specification-item {
  padding: 10px 0px;
  border-bottom: 1px solid #878787;
}
.site-content .specifications-list .specification-item:first-child {
  border-top: 1px solid #878787;
}
.site-content .specifications-list .specification-item .label {
  font-weight: 700;
  word-break: break-word;
  padding-right: 15px !important;
}
.site-content .testimony-single {
  position: relative;
}
.site-content .testimony-single .content-warp {
  width: 430px;
  max-width: 100%;
  font-size: 18px;
  font-weight: 700;
  margin: 0 auto;
  position: relative;
}
.site-content .testimony-single .content-warp::before, .site-content .testimony-single .content-warp::after {
  content: '';
  width: 35px;
  height: 160px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-repeat: no-repeat;
  background-position: center;
}
.site-content .testimony-single .content-warp::before {
  background-image: url("../images/testimony-single-left.svg");
  left: -65px;
}
.site-content .testimony-single .content-warp::after {
  background-image: url("../images/testimony-single-right.svg");
  right: -65px;
}
.site-content .testimony-single .content-warp .content::before {
  content: '"';
  display: inline-block;
}
.site-content .testimony-single .content-warp .content::after {
  content: '"';
  display: inline-block;
}
.site-content .testimony-single .person {
  width: 300px;
  max-width: 100%;
  font-size: 11px;
  line-height: 150%;
  position: absolute;
  bottom: -25px;
  right: 0px;
}
.site-content .testimony-single .person .name {
  font-weight: 700;
}
@media screen and (max-width: 1199px) {
  .site-content .testimony-single .person {
    width: 150px;
  }
}
@media screen and (max-width: 1199px) {
  .site-content .testimony-single .person {
    width: 430px;
    max-width: 100%;
    position: relative;
    margin: 0 auto;
  }
}
.site-content .front-page-category ul {
  margin-bottom: 0px;
}
.site-content .front-page-category ul li {
  list-style: none;
  padding-left: 0;
  line-height: 32px;
}
.site-content .front-page-category ul li::before {
  display: none;
}
.site-content .front-page-category ul li a {
  text-decoration: none;
}
.site-content .sow-headline a {
  text-decoration: none;
}
.site-content .fix-sameline-text {
  margin-top: -4px;
}
.site-content .description a,
.site-content .content a,
.site-content .siteorigin-widget-tinymce a,
.site-content .entry-content a,
.site-content .characters-max-content a {
  font-weight: bold;
}

.site-footer {
  position: relative;
  background-color: #1d418e;
}
@media screen and (max-width: 991px) {
  .site-footer {
    z-index: 2;
  }
}
.site-footer .container {
  padding-left: 20px;
  padding-right: 20px;
}
.site-footer .footer-logo {
  margin-bottom: 36px;
}
.site-footer .widget-title {
  font-size: 14px;
  font-weight: bold;
  line-height: 28px;
  color: #77C2A1;
  margin-bottom: 10px;
}
.site-footer .widget > div {
  padding-bottom: 30px;
}
.site-footer ul.menu {
  margin: 0;
  padding: 0;
}
.site-footer ul.menu > li {
  list-style: none;
}
.site-footer ul.menu > li.wpml-ls-item {
  display: none;
}
.site-footer ul.menu > li a {
  line-height: 26px;
  margin-bottom: 10px;
  display: block;
}
.site-footer ul.menu > li a:hover {
  text-decoration: none;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.site-footer ul.menu > ul {
  display: none;
}
.site-footer i {
  font-size: 1.3333333333333333rem;
  margin-right: 15px;
}
.site-footer p {
  font-size: 14px;
  line-height: 26px;
  color: #ffffff;
}
.site-footer a {
  font-size: 14px;
  color: #ffffff;
}
.site-footer a:hover, .site-footer a:active {
  color: #77C2A1;
  text-decoration: none;
}
.site-footer .site-footer-info {
  padding-top: 100px;
  padding-bottom: 70px;
  padding-left: 100px;
  padding-right: 100px;
}
@media (max-width: 781px) {
  .site-footer .footer-sitemap .panel-grid-cell {
    margin-bottom: 0px !important;
  }
  .site-footer .footer-sitemap .panel-grid-cell:last-child .widget-title {
    display: none;
  }
  .site-footer .footer-sitemap .widget > div {
    padding-bottom: 0px !important;
  }
}
.site-footer .footer-subscribe {
  padding-top: 50px;
  padding-bottom: 56px;
  border-top: 1px solid #ffffff;
}
.site-footer .footer-subscribe .widget {
  padding-left: 100px;
  padding-right: 100px;
}
.site-footer .footer-subscribe .form-wrap {
  position: relative;
}
@media (max-width: 992px) {
  .site-footer .footer-subscribe .form-wrap {
    padding-left: 40px;
    padding-right: 40px;
  }
  .site-footer .footer-subscribe .form-wrap > div {
    margin-bottom: 20px;
  }
  .site-footer .footer-subscribe .form-wrap > div:last-child {
    margin-bottom: 0px;
  }
}
.site-footer .footer-subscribe .form-wrap > div:first-child {
  margin-left: 40px;
  margin-right: 120px;
}
@media (max-width: 1200px) {
  .site-footer .footer-subscribe .form-wrap > div:first-child {
    margin-right: 0px;
  }
}
@media (max-width: 992px) {
  .site-footer .footer-subscribe .form-wrap > div:first-child {
    margin-left: 0px;
  }
}
.site-footer .footer-subscribe .form-wrap > div:last-child {
  margin-right: 55px;
}
@media (max-width: 1200px) {
  .site-footer .footer-subscribe .form-wrap > div:last-child {
    margin-right: 40px;
  }
}
@media (max-width: 992px) {
  .site-footer .footer-subscribe .form-wrap > div:last-child {
    margin-right: 0px;
  }
}
.site-footer .footer-subscribe .form-wrap > div:first-child::before,
.site-footer .footer-subscribe .form-wrap > div:last-child::after {
  content: '';
  width: 15px;
  height: 66px;
  position: absolute;
  bottom: -13px;
}
.site-footer .footer-subscribe .form-wrap > div:first-child::before {
  background-image: url("../images/footer-subscribe-vector-left.svg");
  left: 0;
}
.site-footer .footer-subscribe .form-wrap > div:last-child::after {
  background-image: url("../images/footer-subscribe-vector-right.svg");
  right: 0;
}
.site-footer .footer-subscribe h3 {
  font-size: 18px;
  font-weight: 700;
  color: #77C2A1;
  margin-bottom: 0px;
}
.site-footer .footer-subscribe p {
  margin-bottom: 0px;
}
.site-footer .footer-subscribe label {
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}
.site-footer .footer-subscribe input[type=text],
.site-footer .footer-subscribe input[type=email] {
  width: 210px;
  max-width: 100%;
  height: 40px;
  font-size: 14px;
  color: #ffffff;
  padding: 0px 12px;
  background-color: transparent !important;
  border: 1px solid #FFFFFF;
  outline: none;
  border-radius: 0px;
  -webkit-appearance: none;
}
@media (max-width: 992px) {
  .site-footer .footer-subscribe input[type=text],
  .site-footer .footer-subscribe input[type=email] {
    width: 100%;
  }
}
.site-footer .footer-subscribe input[type=text]:focus,
.site-footer .footer-subscribe input[type=email]:focus {
  border: 3px solid #ADDAC7;
}
.site-footer .footer-subscribe input[type=text]::-webkit-input-placeholder,
.site-footer .footer-subscribe input[type=email]::-webkit-input-placeholder {
  /* Edge */
  color: #C4BFC0;
  opacity: 1;
}
.site-footer .footer-subscribe input[type=text]:-ms-input-placeholder,
.site-footer .footer-subscribe input[type=email]:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #C4BFC0;
  opacity: 1;
}
.site-footer .footer-subscribe input[type=text]::placeholder,
.site-footer .footer-subscribe input[type=email]::placeholder {
  color: #C4BFC0;
  opacity: 1;
}
.site-footer .footer-subscribe input[type=text]:-webkit-autofill, .site-footer .footer-subscribe input[type=text]:-webkit-autofill:hover, .site-footer .footer-subscribe input[type=text]:-webkit-autofill:focus, .site-footer .footer-subscribe input[type=text]:-webkit-autofill:active,
.site-footer .footer-subscribe input[type=email]:-webkit-autofill,
.site-footer .footer-subscribe input[type=email]:-webkit-autofill:hover,
.site-footer .footer-subscribe input[type=email]:-webkit-autofill:focus,
.site-footer .footer-subscribe input[type=email]:-webkit-autofill:active {
  -webkit-transition-delay: 999999999999s;
  -webkit-transition: color 999999999999s ease-out, background-color 9999s ease-out;
}
.site-footer .footer-subscribe input[type=text][data-com-onepassword-filled="dark"],
.site-footer .footer-subscribe input[type=email][data-com-onepassword-filled="dark"] {
  background-color: #1d418e !important;
}
.site-footer .footer-subscribe input[type=submit] {
  width: 153px;
  max-width: 100%;
}
.site-footer .footer-subscribe .mc4wp-response {
  margin-top: 20px;
  text-align: center;
}
.site-footer .site-footer-copyright {
  padding-top: 15px;
  padding-bottom: 15px;
  font-size: 11px;
  position: relative;
  border-top: 1px solid #ffffff;
}
.site-footer .site-footer-copyright ul {
  margin: 0;
  padding: 0;
  padding-left: 100px;
  padding-right: 100px;
}
.site-footer .site-footer-copyright ul li {
  list-style: none;
  display: inline-block;
  margin-right: 46px;
}
.site-footer .site-footer-copyright a {
  font-size: 11px;
  opacity: 0.5;
}
.site-footer .site-footer-copyright a:hover {
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
  opacity: 1;
}
.site-footer .site-footer-copyright p {
  margin-bottom: 0px;
}

.btn-primary {
  height: 40px;
  font-size: 14px;
  font-weight: 700;
  line-height: 100%;
  color: #FFFFFF;
  background-color: #77C2A1;
  border: 2px solid #77C2A1;
  border-radius: 0px;
}
.btn-primary:focus, .btn-primary:active {
  color: #FFFFFF !important;
  background-color: #77C2A1 !important;
  border: 2px solid #77C2A1 !important;
  box-shadow: 0px 0px 0px !important;
  outline: unset !important;
}
.btn-primary:disabled {
  background-color: #E4F3EC;
  border: 2px solid #E4F3EC;
}
.btn-primary:disabled:hover {
  color: #FFF;
  background-color: #E4F3EC;
  border: 2px solid #E4F3EC;
}
.btn-primary:hover {
  color: #77C2A1;
  background-color: transparent;
  border: 2px solid #77C2A1;
}

.btn-secondary {
  height: 40px;
  font-size: 14px;
  font-weight: 700;
  line-height: 100%;
  color: #77C2A1;
  background-color: #ffffff;
  border: 2px solid #77C2A1;
  border-radius: 0px;
}
.btn-secondary:hover {
  color: #ffffff;
  background-color: #77C2A1;
  border: 2px solid #77C2A1;
}

.btn.see-all {
  font-size: 14px;
  color: #000;
  text-decoration: none;
  position: relative;
  padding: 0px;
  margin-top: 40px;
  transition: all 0.2s linear;
}
.btn.see-all::before, .btn.see-all::after {
  content: '';
  width: 7px;
  height: 23px;
  transition: all 0.2s linear;
  display: inline-block;
  vertical-align: middle;
}
.btn.see-all:visited {
  color: #000 !important;
}
.btn.see-all::before {
  content: '';
  background: url("../images/menu-hover-left-black-thin.svg") no-repeat left center;
  margin-right: 5px;
}
.btn.see-all::after {
  background: url("../images/menu-hover-right-black-thin.svg") no-repeat right center;
  margin-left: 5px;
}
.btn.see-all:hover {
  color: #000 !important;
  -webkit-text-fill-color: #000;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #000;
}
.btn.see-all:hover::before {
  content: '';
  background: url("../images/menu-hover-left-black.svg") no-repeat left center;
}
.btn.see-all:hover::after {
  background: url("../images/menu-hover-right-black.svg") no-repeat right center;
}
.btn.remove-tag a::before, .btn.remove-tag a::after {
  opacity: 0;
}
.btn.remove-tag a:hover::before, .btn.remove-tag a:hover::after {
  opacity: 1;
}

.cookie-notice-container {
  padding: 30px 0px;
  position: relative;
}
.cookie-notice-container #cn-accept-cookie {
  width: 18px;
  height: 18px;
  font-size: 0 !important;
  padding: 0px !important;
  top: 10px;
  right: 10px;
  position: absolute;
  border: 0px !important;
  box-shadow: 0px 0px 0px !important;
  background-color: transparent !important;
  background-image: url("../images/close-icon.png") !important;
  background-repeat: no-repeat;
  background-position: center center !important;
}
.cookie-notice-container #cn-accept-cookie:hover {
  background-position: center center !important;
}

.totop {
  text-align: center;
  text-transform: uppercase;
  position: absolute;
  bottom: 450px;
  right: 30px;
  cursor: pointer;
  display: none;
}
.totop .icon-totop {
  width: 37px;
  height: 138px;
  display: block;
  background-image: url("../images/icon-gototop.svg");
  background-repeat: no-repeat;
  margin-bottom: 10px;
}

button, a {
  cursor: pointer;
}
button.btn-primary, a.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 165px;
  /*height: 40px;*/
  height: auto;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border: 2px solid #77C2A1 !important;
  border-radius: 0 !important;
  padding: 11px 30px !important;
  text-decoration: none !important;
  color: #FFF !important;
  background-color: #77C2A1 !important;
  transition: all 0.3s linear;
  position: relative;
}
button.btn-primary:hover, a.btn-primary:hover {
  color: #77C2A1 !important;
  background-color: transparent !important;
  border: 2px solid #77C2A1 !important;
}
button.btn-primary:focus, button.btn-primary:active, a.btn-primary:focus, a.btn-primary:active {
  box-shadow: 0px 0px 0px !important;
  outline: unset !important;
}
button.green-link, a.green-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  padding: 0;
  color: #77C2A1;
}
button.green-link:hover, a.green-link:hover {
  color: #ADDAC7 !important;
}
button.green-link:focus, button.green-link:active, a.green-link:focus, a.green-link:active {
  box-shadow: 0px 0px 0px !important;
  outline: unset !important;
}
button.btn-back, a.btn-back {
  display: inline-flex;
  align-items: center;
  justify-content: start;
  min-width: 120px;
  height: auto;
  font-size: 14px;
  line-height: 1.4;
  border: 0;
  padding: 0;
  text-decoration: none;
  color: #878787;
  transition: all 0.2s ease;
}
@media screen and (max-width: 767px) {
  button.btn-back, a.btn-back {
    width: 100%;
    justify-content: center;
  }
}
button.btn-back:active, button.btn-back:visited, a.btn-back:active, a.btn-back:visited {
  color: #878787 !important;
  background-color: #FFF !important;
  box-shadow: 0px 0px 0px !important;
  outline: unset !important;
}
button.btn-back:hover, a.btn-back:hover {
  color: #77C2A1 !important;
}
button.btn-back:hover:before, a.btn-back:hover:before {
  background-image: url(../images/arrow-left-green.svg);
}
button.btn-back:before, a.btn-back:before {
  content: '';
  display: inline-flex;
  margin-right: 6px;
  width: 28px;
  height: 28px;
  background: transparent;
  background-image: url(../images/arrow-left-grey.svg);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: top -1px center;
  transition: all 0.2s ease;
}

.nf-form-fields-required {
  display: none;
}

.white-text {
  color: #FFF;
}
.white-text h1, .white-text h2, .white-text h3, .white-text h4, .white-text h5, .white-text p, .white-text span {
  color: #FFF;
}

.green-text {
  color: #77C2A1;
}

.border-box {
  border-top: 1px solid #FFF;
  border-bottom: 1px solid #FFF;
  padding: 15px 0;
}
.border-box a.mailto, .border-box a.link {
  font-weight: 700;
}
.border-box a.tel {
  font-weight: 700;
  color: #FFF;
  text-decoration: none;
}
.border-box a.tel:hover {
  color: #ADDAC7;
  text-decoration: underline;
}

.panel-grid {
  position: relative;
}
.panel-grid .absolute {
  position: absolute;
  z-index: 2;
}
.panel-grid .absolute.left-top {
  left: -105px;
  top: -105px;
}
@media screen and (max-width: 810px) {
  .panel-grid .absolute.left-top {
    left: -60px;
  }
}
@media screen and (max-width: 768px) {
  .panel-grid .absolute.left-top {
    left: 0px !important;
  }
}
@media screen and (max-width: 625px) {
  .panel-grid .absolute.left-top {
    top: -30px;
  }
}
.panel-grid .absolute.left-bottom {
  left: -105px;
  bottom: -105px;
}
@media screen and (max-width: 810px) {
  .panel-grid .absolute.left-bottom {
    left: -60px;
  }
}
@media screen and (max-width: 768px) {
  .panel-grid .absolute.left-bottom {
    left: 0px !important;
  }
}
@media screen and (max-width: 625px) {
  .panel-grid .absolute.left-bottom {
    bottom: -30px;
  }
}
.panel-grid .absolute.right-top {
  right: -105px;
  top: -105px;
}
@media screen and (max-width: 810px) {
  .panel-grid .absolute.right-top {
    right: -60px;
  }
}
@media screen and (max-width: 768px) {
  .panel-grid .absolute.right-top {
    right: 0px !important;
  }
}
@media screen and (max-width: 625px) {
  .panel-grid .absolute.right-top {
    top: -30px;
  }
}
.panel-grid .absolute.right-bottom {
  right: -105px;
  bottom: -105px;
}
@media screen and (max-width: 810px) {
  .panel-grid .absolute.right-bottom {
    right: -60px;
  }
}
@media screen and (max-width: 768px) {
  .panel-grid .absolute.right-bottom {
    right: 0px !important;
  }
}
@media screen and (max-width: 625px) {
  .panel-grid .absolute.right-bottom {
    bottom: -30px;
  }
}
.panel-grid .absolute h2 {
  /*
  font-size: 40px;
  line-height: 1.4;
  @media screen and (max-width: 1199.9px) {
  	font-size: 24px;
  }
  @media screen and (max-width: 991.9px) {
  	font-size: 18px;
  }
  */
}

.no-margin {
  margin: 0;
}
.no-margin p {
  margin: 0;
}

.no-padding {
  padding: 0;
}

.filedownload-widget .download-list .download-item {
  border-bottom: 1px solid #878787;
  position: relative;
}
.filedownload-widget .download-list .download-item:first-child {
  border-top: 1px solid #878787;
}
.filedownload-widget .download-list .download-item a {
  color: inherit;
  text-decoration: none;
  display: block;
  padding: 20px 10px;
  padding-right: 60px;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.filedownload-widget .download-list .download-item a::before {
  content: '';
  width: 33px;
  height: 33px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url("../images/download-icon-back.svg");
  background-repeat: no-repeat;
  background-position: center;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.filedownload-widget .download-list .download-item a:hover {
  background-color: #E4F3EC;
}
.filedownload-widget .download-list .download-item a:hover::before {
  background-image: url("../images/download-icon-green.svg");
}

.number-repeater-widget {
  position: relative;
}
.number-repeater-widget .number-repeater-list .owl-stage {
  padding-top: 1px;
}
.number-repeater-widget .number-repeater-list .owl-item {
  margin-top: -1px;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
}
.number-repeater-widget .number-repeater-list .number-repeater-item-warp {
  width: 285px;
  max-width: 100%;
  /*margin-bottom: -2px;*/
  background-color: #ffffff;
}
.number-repeater-widget .number-repeater-list .number-repeater-item-warp .number {
  width: 27%;
  font-size: 64px;
  font-weight: 700;
  letter-spacing: -10px;
  color: #E9F5F5;
  display: inline-block;
  vertical-align: middle;
}
.number-repeater-widget .number-repeater-list .number-repeater-item-warp .content {
  width: 71%;
  display: inline-block;
  vertical-align: middle;
}
.number-repeater-widget .number-repeater-slider-counter {
  display: none;
  visibility: hidden;
}
@media (min-width: 1199px) {
  .number-repeater-widget .number-repeater-list .owl-carousel {
    display: block;
  }
  .number-repeater-widget .number-repeater-list .owl-stage {
    width: 100% !important;
    transform: unset !important;
    /*
    columns: 3;
    -webkit-columns: 3;
    -moz-columns: 3;
    */
  }
  .number-repeater-widget .number-repeater-list .owl-stage .owl-item:nth-child(3n+3) {
    margin-right: 0px !important;
  }
  .number-repeater-widget .number-repeater-list .owl-nav,
  .number-repeater-widget .number-repeater-list .owl-dots {
    display: none;
    visibility: hidden;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .number-repeater-widget .number-repeater-list .owl-stage {
    width: 100% !important;
    transform: unset !important;
    /*
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
    */
  }
  .number-repeater-widget .number-repeater-list .owl-stage .owl-item:nth-child(2n+2) {
    margin-right: 0px !important;
  }
  .number-repeater-widget .number-repeater-list .owl-nav,
  .number-repeater-widget .number-repeater-list .owl-dots {
    display: none;
    visibility: hidden;
  }
}
@media (max-width: 991px) {
  .number-repeater-widget {
    width: 325px;
  }
  .number-repeater-widget .number-repeater-list .number-repeater-item-warp {
    width: 253px;
    margin-bottom: 0px;
  }
  .number-repeater-widget .number-repeater-list .owl-stage-outer {
    margin-bottom: 39px;
  }
  .number-repeater-widget .number-repeater-list .owl-nav {
    display: block;
    visibility: visible;
    text-align: left;
    margin: 0;
    padding: 0;
  }
  .number-repeater-widget .number-repeater-list .owl-nav .owl-prev,
  .number-repeater-widget .number-repeater-list .owl-nav .owl-next {
    width: 50px;
    height: 50px;
    background-color: rgba(104, 194, 159, 0.2);
    border-radius: 100%;
    opacity: 1;
    position: relative;
    padding: 0;
    margin-right: 8px;
    border: 0px;
  }
  .number-repeater-widget .number-repeater-list .owl-nav .owl-prev span,
  .number-repeater-widget .number-repeater-list .owl-nav .owl-next span {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .number-repeater-widget .number-repeater-list .owl-nav .owl-prev span {
    background-image: url("../images/arrow-left-green.svg");
  }
  .number-repeater-widget .number-repeater-list .owl-nav .owl-next span {
    background-image: url("../images/arrow-right-green.svg");
  }
  .number-repeater-widget .number-repeater-list .owl-nav [class*=owl-]:hover {
    background-color: rgba(104, 194, 159, 0.2);
  }
  .number-repeater-widget .number-repeater-slider-counter {
    display: block;
    visibility: visible;
    color: #77C2A1;
    position: absolute;
    right: 0;
    bottom: 17px;
  }
}
@media (max-width: 576px) {
  .number-repeater-widget {
    width: 100%;
  }
  .number-repeater-widget .number-repeater-list .number-repeater-item-warp {
    width: 305px;
  }
  .number-repeater-widget .number-repeater-list .owl-stage-outer {
    margin-right: -20px;
  }
}

.widget_image-gallery-carousel-theme-widget .so-widget-image-gallery-carousel-theme-widget {
  width: 420px;
  max-width: 100%;
  position: absolute;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .so-widget-image-gallery-carousel-theme-widget {
    max-width: 100%;
    left: 0;
    right: 0;
    bottom: -400px;
    margin: 0 auto;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel {
  position: relative;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-stage-outer {
    height: 350px;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav {
  margin: 0px;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev,
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next {
  height: 90%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  margin: 0;
  background: transparent;
  border: 0px;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev,
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next {
    height: auto;
    width: auto;
    top: unset;
    bottom: 0;
    transform: unset;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span,
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span {
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(104, 194, 159, 0.6);
  border-radius: 100%;
  opacity: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span,
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span {
    opacity: 1;
    background-color: rgba(104, 194, 159, 0.2);
    bottom: 0px;
    top: unset;
    transform: unset;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span::after,
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span::after {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0;
  background-repeat: no-repeat;
  background-position: center;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev:hover,
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next:hover {
  background: transparent;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev:hover span,
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next:hover span {
  opacity: 1;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev {
  width: 50%;
  left: 0;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span {
  left: 15px;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span {
    left: 0;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span::after {
  background-image: url("../images/arrow-left-white.svg");
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-prev span::after {
    background-image: url("../images/arrow-left-green.svg");
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next {
  width: 50%;
  right: 0;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span {
  right: 15px;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span {
    right: 0;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span::after {
  background-image: url("../images/arrow-right-white.svg");
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-nav .owl-next span::after {
    background-image: url("../images/arrow-right-green.svg");
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots {
  margin-top: 20px;
}
@media screen and (max-width: 991px) {
  .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots {
    margin-top: 48px;
    padding-bottom: 10px;
  }
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots .owl-dot {
  border: 0px;
  background-color: transparent;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots .owl-dot span {
  background-color: #E4F3EC;
}
.widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots .owl-dot.active span, .widget_image-gallery-carousel-theme-widget .image-gallery-owl-carousel.owl-theme .owl-dots .owl-dot:hover span {
  background-color: #77C2A1;
}

.rmp-results-widget {
  font-size: 11px;
}
.rmp-results-widget .rmp-results-widget__visual-rating {
  margin-bottom: 20px;
}
.rmp-results-widget .rmp-results-widget__visual-rating .js-rmp-results-icon::before {
  content: '';
  width: 24px;
  height: 24px;
  background: url("../images/star.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  display: inline-block;
  margin-right: 6px;
}
.rmp-results-widget .rmp-results-widget__visual-rating .js-rmp-results-icon:not(.rmp-icon--full-highlight) {
  opacity: 0.2;
}

@media screen and (max-width: 600px) {
  .panel-grid-cell {
    position: relative;
  }
}

@media screen and (max-width: 990.9px) and (min-width: 768px) {
  .tablet-50 {
    width: 50%;
  }
}

@media screen and (min-width: 991px) {
  .hidden-desktop {
    display: none !important;
  }
}

@media screen and (max-width: 991px) {
  .hidden-mobile {
    display: none;
  }
}

.modal .modal-dialog .modal-content iframe {
  max-width: 100%;
}

.read-more-section .text-wrapper .description {
  column-gap: 40px;
}

#usercentrics-root {
  position: absolute;
  z-index: 1;
}

@media (max-width: 1199px) and (min-width: 992px) {
  .panel-grid-cell {
    -webkit-flex: unset !important;
    flex: unset !important;
  }
}
@media screen and (min-width: 992px) {
  ul.primary-menu .dropdown-toggle::after {
    display: none;
  }

  .addreadmore .readmore,
  .addreadmore .readless {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .site-header ul.primary-menu > li > a {
    padding: 10px 0px;
  }

  .navbar-light .navbar-toggler {
    border: 0px;
    color: #000000;
    padding-left: 0px;
    padding-right: 0px;
    margin-right: -3px;
  }

  .navbar-toggler-icon {
    width: 32px;
    height: 32px;
    background-image: url("../images/navbar-toggler-icon.svg") !important;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-right: -3px;
  }

  .site-footer .site-footer-info {
    padding-top: 84px;
    padding-bottom: 24px;
    padding-left: 0px;
    padding-right: 0px;
  }
  .site-footer .widget-title.toggle:before {
    font-family: 'FontAwesome';
    margin-right: 10px;
  }
  .site-footer .widget-title.toggle.collapsed:before {
    content: '\f105';
  }
  .site-footer .widget-title.toggle:not(.collapsed):before {
    content: '\f107';
  }
  .site-footer .footer-sitemap {
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
    display: -webkit-inline-box !important;
  }
  .site-footer .footer-sitemap .panel-grid-cell:last-child {
    padding-top: 38px !important;
  }
  .site-footer .lsow-icon-list .lsow-icon-list-item {
    margin-left: 22px;
  }
  .site-footer .footer-subscribe {
    padding-top: 42px;
    padding-bottom: 42px;
    border-top: 0px;
  }
  .site-footer .footer-subscribe .widget_mc4wp_form_widget {
    position: relative;
  }
  .site-footer .footer-subscribe .widget_mc4wp_form_widget::before {
    content: '';
    height: 1px;
    width: 100%;
    background-color: #FFFFFF;
    position: absolute;
    top: -42px;
  }
  .site-footer .footer-subscribe h3 {
    line-height: 28px;
    position: relative;
    margin-bottom: 22px;
    padding-left: 37px;
    padding-right: 37px;
    display: inline-block;
  }
  .site-footer .footer-subscribe h3::before, .site-footer .footer-subscribe h3::after {
    content: '';
    width: 15px;
    height: 66px;
    position: absolute;
    top: -6px;
  }
  .site-footer .footer-subscribe h3::before {
    background-image: url("../images/footer-subscribe-vector-left.svg");
    left: 0;
  }
  .site-footer .footer-subscribe h3::after {
    background-image: url("../images/footer-subscribe-vector-right.svg");
    right: 0;
  }
  .site-footer .footer-subscribe .widget {
    padding-left: 0px;
    padding-right: 0px;
  }
  .site-footer .footer-subscribe .form-wrap {
    padding-left: 0px;
    padding-right: 0px;
  }
  .site-footer .footer-subscribe .form-wrap > div:first-child::before,
  .site-footer .footer-subscribe .form-wrap > div:last-child::after {
    display: none;
    visibility: hidden;
  }
  .site-footer .site-footer-copyright {
    border-top: 0px;
  }
  .site-footer .site-footer-copyright ul {
    position: relative;
    padding: 0;
  }
  .site-footer .site-footer-copyright ul::before {
    content: '';
    height: 1px;
    width: 100%;
    background-color: #FFFFFF;
    position: absolute;
    top: -15px;
    left: 0;
  }
  .site-footer .totop {
    display: none;
    visibility: hidden;
  }

  .addreadmore .secsec {
    margin-bottom: 15px;
  }
  .addreadmore.showlesscontent .secsec,
  .addreadmore.showlesscontent .readless {
    display: none;
  }
  .addreadmore.showmorecontent .readmore {
    display: none;
  }
  .addreadmore.button1 .readmore,
  .addreadmore.button1 .readless {
    width: 159px;
    height: 40px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
    background: #77C2A1;
    border: 2px solid #77C2A1;
    margin-top: 20px;
  }
  .addreadmore.button1 .readmore:hover,
  .addreadmore.button1 .readless:hover {
    color: #77C2A1;
    background: #ffffff;
  }
  .addreadmore.button2 .readmore,
  .addreadmore.button2 .readless {
    color: #000;
    text-decoration: none;
    position: relative;
    transition: all 0.2s linear;
    background: none;
    border: none;
    padding-left: 0px;
    padding-right: 0px;
  }
  .addreadmore.button2 .readmore::before, .addreadmore.button2 .readmore::after,
  .addreadmore.button2 .readless::before,
  .addreadmore.button2 .readless::after {
    content: '';
    width: 7px;
    height: 23px;
    transition: all 0.2s linear;
    display: inline-block;
    vertical-align: middle;
  }
  .addreadmore.button2 .readmore:visited,
  .addreadmore.button2 .readless:visited {
    color: #000 !important;
  }
  .addreadmore.button2 .readmore::before,
  .addreadmore.button2 .readless::before {
    content: '';
    background: url("../images/menu-hover-left-black-thin.svg") no-repeat left center;
    margin-right: 10px;
  }
  .addreadmore.button2 .readmore::after,
  .addreadmore.button2 .readless::after {
    background: url("../images/menu-hover-right-black-thin.svg") no-repeat right center;
    margin-left: 10px;
  }
  .addreadmore.button2 .readmore:hover,
  .addreadmore.button2 .readless:hover {
    color: #000 !important;
    -webkit-text-fill-color: #000;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #000;
  }
  .addreadmore.button2 .readmore:hover::before,
  .addreadmore.button2 .readless:hover::before {
    background: url("../images/menu-hover-left-black.svg") no-repeat left center;
  }
  .addreadmore.button2 .readmore:hover::after,
  .addreadmore.button2 .readless:hover::after {
    background: url("../images/menu-hover-right-black.svg") no-repeat right center;
  }

  .addreadmorewraptext.showmorecontent .secsec,
  .addreadmorewraptext.showmorecontent .readless {
    display: block;
  }

    /*
    .addreadmore.showlesscontent .secsec,
    .addreadmore.showlesscontent .readless {
        display: none;
    }

    .addreadmore.showmorecontent .readmore {
        display: none;
    }

    .addreadmore .readmore,
    .addreadmore .readless {
        font-weight: bold;
        margin-left: 2px;
        color: blue;
        cursor: pointer;
    }

    .addreadmorewraptext.showmorecontent .secsec,
    .addreadmorewraptext.showmorecontent .readless {
        display: block;
    }
    */
}
@media screen and (max-width: 600px) {
  #wpadminbar {
    position: fixed;
  }
}
@media screen and (max-width: 576px) {
  .totop {
    right: 15px;
    bottom: 45px;
    font-size: 0px;
  }

  .site-content .testimony-single .content-warp {
    padding: 0 55px;
    font-size: 16px;
  }
  .site-content .testimony-single .content-warp::before {
    left: 0;
  }
  .site-content .testimony-single .content-warp::after {
    right: 0;
  }
  .site-content .testimony-single .person {
    bottom: -70px;
    right: unset;
    left: 50px;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1230px) {
  .testimony-slider .testimony-name {
    display: none;
    visibility: hidden;
  }
}

.sub-segment .sow-image-container {
  width: 100%;
  overflow: hidden;
}
@media screen and (max-width: 810px) {
  .sub-segment .sow-image-container {
    max-width: unset;
    max-height: unset;
  }
}
.sub-segment .sow-image-container .so-widget-image {
  width: auto;
  object-fit: cover;
  object-position: top center;
  border-radius: 0;
  transition: transform .5s ease;
}
.sub-segment .segment-body {
  padding: 45px;
  color: #000;
  background-color: #FFF;
  transition: background-color 0.3s ease;
}
.sub-segment .segment-body h3 {
  color: #000;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 15px;
}
.sub-segment .segment-body p {
  font-size: 14px;
  line-height: 1.6;
}
.sub-segment .segment-body a.arrow-link {
  font-size: 0;
  background: transparent;
  border: 0;
}
.sub-segment .segment-body a.arrow-link:after {
  content: '';
  display: inline-flex;
  margin-left: 0;
  width: 36px;
  height: 36px;
  background: transparent;
  background-image: url(../images/arrow-right-white.svg);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  transition: background-image 0.3s linear;
}
@media screen and (max-width: 600px) {
  .sub-segment .segment-body {
    padding: 22px 19px 26px 19px;
    background-color: #F2F2F2;
  }
  .sub-segment .segment-body h3 {
    font-size: 18px;
    font-weight: 700 !important;
    margin-bottom: 10px;
  }
  .sub-segment .segment-body p {
    font-size: 11px;
  }
  .sub-segment .segment-body .arrow-link {
    background: transparent !important;
    border: 0;
  }
  .sub-segment .segment-body .arrow-link::after {
    background-image: url(../images/arrow-right-green.svg) !important;
  }
}
.sub-segment:hover .sow-image-container .so-widget-image {
  transform: scale(1.05);
}
.sub-segment:hover .segment-body {
  background-color: #F2F2F2;
}
.sub-segment:hover .segment-body h3 {
  font-weight: 700 !important;
}
.sub-segment:hover .segment-body .arrow-link {
  background: transparent !important;
  border: 0;
}
.sub-segment:hover .segment-body .arrow-link::after {
  background-image: url(../images/arrow-right-green.svg);
}

.img-background {
  position: relative;
}
.img-background .sow-image-container img {
  width: auto;
}
.img-background.parallax-image {
  overflow: hidden;
}
.img-background.parallax-image .sow-image-container img {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
}
@media screen and (max-width: 625px) {
  .img-background.parallax-image .sow-image-container img {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.card-icon-wrapper {
  -webkit-align-items: initial !important;
  align-items: initial !important;
}
.card-icon-wrapper.bt .card-icon {
  border-top: 1px solid rgba(135, 135, 135, 0.2);
}
.card-icon-wrapper.bb .card-icon {
  border-bottom: 1px solid rgba(135, 135, 135, 0.2);
}
.card-icon-wrapper .card-icon {
  /*
  border-top: 1px solid rgba(135,135,135,0.2);
  border-bottom: 1px solid rgba(135,135,135,0.2);
  */
  padding: 14px 25px 20px 25px;
  transition: all 0.3s linear;
  position: relative;
}
@media screen and (max-width: 992px) {
  .card-icon-wrapper .card-icon {
    padding: 20px 0px 40px 0px;
  }
}
.card-icon-wrapper .card-icon .panel-first-child {
  margin-bottom: 0 !important;
}
.card-icon-wrapper .card-icon a.absolute-link {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
  top: 0;
  left: 0;
}
.card-icon-wrapper .card-icon .text-body h4 {
  color: #000;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.6;
  margin-bottom: 13px;
}
.card-icon-wrapper .card-icon .text-body a.arrow-link {
  font-size: 0;
  background: transparent;
  border: 0;
}
@media screen and (max-width: 992px) {
  .card-icon-wrapper .card-icon .text-body a.arrow-link {
    float: left;
  }
}
.card-icon-wrapper .card-icon .text-body a.arrow-link:after {
  content: '';
  display: inline-flex;
  margin-left: 0;
  width: 36px;
  height: 36px;
  background: transparent;
  background-image: url(../images/arrow-right-white.svg);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  transition: background-image 0.3s linear;
}
@media screen and (max-width: 991px) {
  .card-icon-wrapper .card-icon .text-body a.arrow-link:after {
    background-image: url(../images/arrow-right-black.svg);
    transition: background-image 0s linear;
  }
}
.card-icon-wrapper .card-icon:hover {
  background-color: #E4F3EC;
}
.card-icon-wrapper .card-icon:hover .text-body .arrow-link {
  background: transparent !important;
  border: 0;
}
.card-icon-wrapper .card-icon:hover .text-body .arrow-link::after {
  background-image: url(../images/arrow-right-green.svg);
}
@media screen and (max-width: 991px) {
  .card-icon-wrapper .card-icon:hover .text-body .arrow-link::after {
    transition: background-image 0s linear;
  }
}

.childTabMenu {
  background-color: #77C2A1;
  position: relative;
  opacity: 1;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.childTabMenu .nav {
  border: 0px;
  align-items: center;
}
.childTabMenu .nav .nav-item {
  margin-bottom: 0px;
  padding: 0 15px;
  max-width: 245px;
  text-align: center;
  line-height: 1.4;
}
.childTabMenu .nav .nav-item .nav-link {
  font-size: 14px;
  font-weight: 400;
  color: #FFF;
  line-height: 1.4 !important;
  text-decoration: none;
  padding: 17px 0px;
  background: none;
  border: 0px;
  position: relative;
  display: block;
  transition: all 0.3s ease;
}
.childTabMenu .nav .nav-item .nav-link span {
  display: block;
}
.childTabMenu .nav .nav-item .nav-link span::before {
  display: block;
  content: attr(data-text);
  font-weight: bold;
  height: 0px;
  color: transparent;
  overflow: hidden;
  visibility: hidden;
  letter-spacing: 0.6px;
}
.childTabMenu .nav .nav-item .nav-link:hover::before, .childTabMenu .nav .nav-item .nav-link:hover::after, .childTabMenu .nav .nav-item .nav-link.active::before, .childTabMenu .nav .nav-item .nav-link.active::after {
  opacity: 1;
}
.childTabMenu .nav .nav-item .nav-link:hover span, .childTabMenu .nav .nav-item .nav-link.active span {
  font-weight: bold;
}
.childTabMenu .nav .nav-item .nav-link::before, .childTabMenu .nav .nav-item .nav-link::after {
  content: '';
  width: 8px;
  height: 23px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.childTabMenu .nav .nav-item .nav-link::before {
  background: url("../images/menu-hover-left-white.svg") no-repeat left center;
  left: -10px;
}
.childTabMenu .nav .nav-item .nav-link::after {
  background: url("../images/menu-hover-right-white.svg") no-repeat right center;
  right: -10px;
}
.childTabMenu .nav.application-areas .nav-item {
  max-width: 198px;
}
.childTabMenu .nav-mobile {
  display: none;
}
.childTabMenu .nav-mobile-button {
  display: none;
}
@media screen and (max-width: 992px) {
  .childTabMenu {
    position: relative;
    width: 100%;
    z-index: 15;
    margin-top: -2px;
    transition: all 0.3s ease;
  }
  .childTabMenu.show {
    position: fixed;
  }
  .childTabMenu .container {
    position: relative;
    margin: auto;
    padding: 0;
  }
  .childTabMenu .container .nav-mobile-button {
    display: block;
    width: 100%;
    height: 16px;
    background: url(../images/arrow-down-white.svg) no-repeat;
    background-size: auto;
    background-position: center right;
    border: 0;
    position: absolute;
    right: 15px;
    top: 19px;
  }
  .childTabMenu .container .nav-mobile-button.up {
    background: url(../images/arrow-up-white.svg) no-repeat;
    background-size: auto;
    background-position: center right;
  }
  .childTabMenu .container ul.nav {
    display: none;
  }
  .childTabMenu .container ul.nav-mobile {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
  }
  .childTabMenu .container ul.nav-mobile li.nav-item {
    margin-bottom: 0px;
    text-align: left;
    line-height: 1.4;
    transition: all 0.3s ease;
  }
  .childTabMenu .container ul.nav-mobile li.nav-item .nav-link {
    display: inline-block;
    font-weight: 400;
    color: #FFF;
    text-decoration: none;
    background: none;
    border: 0px;
    position: relative;
    letter-spacing: 1px;
    transition: all 0.3s ease;
  }
  .childTabMenu ul.nav-mobile li.nav-item:first-child {
    height: 53px;
  }
  .childTabMenu ul.nav-mobile li.nav-item:first-child .nav-link {
    font-size: 14px;
    font-weight: 700;
    line-height: 1.4;
    padding: 17px 15px;
  }
  .childTabMenu ul.nav-mobile li.nav-item {
    height: 0;
  }
  .childTabMenu ul.nav-mobile li.nav-item .nav-link {
    font-size: 0;
    line-height: 0;
  }
  .childTabMenu ul.nav-mobile.show li.nav-item {
    height: 53px;
    border-bottom: 1px solid #FFF;
  }
  .childTabMenu ul.nav-mobile.show li.nav-item .nav-link {
    font-size: 14px;
    line-height: 1.4;
    padding: 17px 15px;
    display: block;
  }
}
@media screen and (max-width: 330px) {
  .childTabMenu ul.nav-mobile li.nav-item:first-child .nav-link {
    font-size: 11px;
  }
  .childTabMenu ul.nav-mobile.show li.nav-item .nav-link {
    font-size: 11px;
  }
}

.wpcf7 .validate strong.wpcf7-not-valid,
.salesforce-form .validate strong.wpcf7-not-valid {
  color: #DE4668;
}
.wpcf7 input[type="text"], .wpcf7 input[type="email"], .wpcf7 input[type="url"], .wpcf7 input[type="tel"],
.salesforce-form input[type="text"],
.salesforce-form input[type="email"],
.salesforce-form input[type="url"],
.salesforce-form input[type="tel"] {
  width: 100%;
  max-width: 100%;
  color: #000;
  padding: 0 12px;
  height: 40px;
  border: 1px solid #878787;
  border-radius: 0px;
  background: transparent;
  font-size: 14px;
  transition: all 0.3s ease;
  background-image: unset !important;
  background-size: 16px !important;
  background-position: right 12px center !important;
  background-repeat: no-repeat !important;
}
.wpcf7 input[type="text"]:focus, .wpcf7 input[type="text"]:focus-visible, .wpcf7 input[type="email"]:focus, .wpcf7 input[type="email"]:focus-visible, .wpcf7 input[type="url"]:focus, .wpcf7 input[type="url"]:focus-visible, .wpcf7 input[type="tel"]:focus, .wpcf7 input[type="tel"]:focus-visible,
.salesforce-form input[type="text"]:focus,
.salesforce-form input[type="text"]:focus-visible,
.salesforce-form input[type="email"]:focus,
.salesforce-form input[type="email"]:focus-visible,
.salesforce-form input[type="url"]:focus,
.salesforce-form input[type="url"]:focus-visible,
.salesforce-form input[type="tel"]:focus,
.salesforce-form input[type="tel"]:focus-visible {
  outline: none;
  border-radius: 0px;
  box-shadow: 0px 0px 0px;
  border: 3px solid #ADDAC7;
}
.wpcf7 input[type="text"]::-webkit-input-placeholder, .wpcf7 input[type="email"]::-webkit-input-placeholder, .wpcf7 input[type="url"]::-webkit-input-placeholder, .wpcf7 input[type="tel"]::-webkit-input-placeholder,
.salesforce-form input[type="text"]::-webkit-input-placeholder,
.salesforce-form input[type="email"]::-webkit-input-placeholder,
.salesforce-form input[type="url"]::-webkit-input-placeholder,
.salesforce-form input[type="tel"]::-webkit-input-placeholder {
  /* Edge */
  color: #CFCFCF;
}
.wpcf7 input[type="text"]:-ms-input-placeholder, .wpcf7 input[type="email"]:-ms-input-placeholder, .wpcf7 input[type="url"]:-ms-input-placeholder, .wpcf7 input[type="tel"]:-ms-input-placeholder,
.salesforce-form input[type="text"]:-ms-input-placeholder,
.salesforce-form input[type="email"]:-ms-input-placeholder,
.salesforce-form input[type="url"]:-ms-input-placeholder,
.salesforce-form input[type="tel"]:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #CFCFCF;
}
.wpcf7 input[type="text"]::placeholder, .wpcf7 input[type="email"]::placeholder, .wpcf7 input[type="url"]::placeholder, .wpcf7 input[type="tel"]::placeholder,
.salesforce-form input[type="text"]::placeholder,
.salesforce-form input[type="email"]::placeholder,
.salesforce-form input[type="url"]::placeholder,
.salesforce-form input[type="tel"]::placeholder {
  color: #CFCFCF;
}
.wpcf7 input[type="text"].wpcf7-not-valid, .wpcf7 input[type="email"].wpcf7-not-valid, .wpcf7 input[type="url"].wpcf7-not-valid, .wpcf7 input[type="tel"].wpcf7-not-valid,
.salesforce-form input[type="text"].wpcf7-not-valid,
.salesforce-form input[type="email"].wpcf7-not-valid,
.salesforce-form input[type="url"].wpcf7-not-valid,
.salesforce-form input[type="tel"].wpcf7-not-valid {
  color: #DE4668 !important;
  border: 1px solid #DE4668 !important;
  background-image: url(../images/warning_circle.svg) !important;
}
.wpcf7 input[type="text"].wpcf7-not-valid::-webkit-input-placeholder, .wpcf7 input[type="email"].wpcf7-not-valid::-webkit-input-placeholder, .wpcf7 input[type="url"].wpcf7-not-valid::-webkit-input-placeholder, .wpcf7 input[type="tel"].wpcf7-not-valid::-webkit-input-placeholder,
.salesforce-form input[type="text"].wpcf7-not-valid::-webkit-input-placeholder,
.salesforce-form input[type="email"].wpcf7-not-valid::-webkit-input-placeholder,
.salesforce-form input[type="url"].wpcf7-not-valid::-webkit-input-placeholder,
.salesforce-form input[type="tel"].wpcf7-not-valid::-webkit-input-placeholder {
  color: #CFCFCF !important;
}
.wpcf7 input[type="text"].wpcf7-not-valid:-ms-input-placeholder, .wpcf7 input[type="email"].wpcf7-not-valid:-ms-input-placeholder, .wpcf7 input[type="url"].wpcf7-not-valid:-ms-input-placeholder, .wpcf7 input[type="tel"].wpcf7-not-valid:-ms-input-placeholder,
.salesforce-form input[type="text"].wpcf7-not-valid:-ms-input-placeholder,
.salesforce-form input[type="email"].wpcf7-not-valid:-ms-input-placeholder,
.salesforce-form input[type="url"].wpcf7-not-valid:-ms-input-placeholder,
.salesforce-form input[type="tel"].wpcf7-not-valid:-ms-input-placeholder {
  color: #CFCFCF !important;
}
.wpcf7 input[type="text"].wpcf7-not-valid::placeholder, .wpcf7 input[type="email"].wpcf7-not-valid::placeholder, .wpcf7 input[type="url"].wpcf7-not-valid::placeholder, .wpcf7 input[type="tel"].wpcf7-not-valid::placeholder,
.salesforce-form input[type="text"].wpcf7-not-valid::placeholder,
.salesforce-form input[type="email"].wpcf7-not-valid::placeholder,
.salesforce-form input[type="url"].wpcf7-not-valid::placeholder,
.salesforce-form input[type="tel"].wpcf7-not-valid::placeholder {
  color: #CFCFCF !important;
}
.wpcf7 input[type="text"].wpcf7-valid, .wpcf7 input[type="email"].wpcf7-valid, .wpcf7 input[type="url"].wpcf7-valid, .wpcf7 input[type="tel"].wpcf7-valid,
.salesforce-form input[type="text"].wpcf7-valid,
.salesforce-form input[type="email"].wpcf7-valid,
.salesforce-form input[type="url"].wpcf7-valid,
.salesforce-form input[type="tel"].wpcf7-valid {
  font-weight: 100 !important;
  border: 1px solid #77C2A1 !important;
}
.wpcf7 input[type="text"]:-webkit-autofill, .wpcf7 input[type="text"]:-webkit-autofill:hover, .wpcf7 input[type="text"]:-webkit-autofill:focus, .wpcf7 input[type="text"]:-webkit-autofill:active, .wpcf7 input[type="email"]:-webkit-autofill, .wpcf7 input[type="email"]:-webkit-autofill:hover, .wpcf7 input[type="email"]:-webkit-autofill:focus, .wpcf7 input[type="email"]:-webkit-autofill:active, .wpcf7 input[type="url"]:-webkit-autofill, .wpcf7 input[type="url"]:-webkit-autofill:hover, .wpcf7 input[type="url"]:-webkit-autofill:focus, .wpcf7 input[type="url"]:-webkit-autofill:active, .wpcf7 input[type="tel"]:-webkit-autofill, .wpcf7 input[type="tel"]:-webkit-autofill:hover, .wpcf7 input[type="tel"]:-webkit-autofill:focus, .wpcf7 input[type="tel"]:-webkit-autofill:active,
.salesforce-form input[type="text"]:-webkit-autofill,
.salesforce-form input[type="text"]:-webkit-autofill:hover,
.salesforce-form input[type="text"]:-webkit-autofill:focus,
.salesforce-form input[type="text"]:-webkit-autofill:active,
.salesforce-form input[type="email"]:-webkit-autofill,
.salesforce-form input[type="email"]:-webkit-autofill:hover,
.salesforce-form input[type="email"]:-webkit-autofill:focus,
.salesforce-form input[type="email"]:-webkit-autofill:active,
.salesforce-form input[type="url"]:-webkit-autofill,
.salesforce-form input[type="url"]:-webkit-autofill:hover,
.salesforce-form input[type="url"]:-webkit-autofill:focus,
.salesforce-form input[type="url"]:-webkit-autofill:active,
.salesforce-form input[type="tel"]:-webkit-autofill,
.salesforce-form input[type="tel"]:-webkit-autofill:hover,
.salesforce-form input[type="tel"]:-webkit-autofill:focus,
.salesforce-form input[type="tel"]:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}
.wpcf7 .wpcf7-not-valid-tip,
.salesforce-form .wpcf7-not-valid-tip {
  display: block !important;
  position: relative;
  top: 5px;
  left: 0;
  text-align: left;
  font-size: 11px;
  line-height: 1;
  z-index: 100;
  border: 0;
  background: transparent;
  padding: 0;
  width: 100%;
}
.wpcf7 .dnd-upload-status .dnd-upload-details .name,
.salesforce-form .dnd-upload-status .dnd-upload-details .name {
  color: #77C2A1;
}
.wpcf7 .dnd-upload-status .dnd-upload-details .remove-file span:after,
.salesforce-form .dnd-upload-status .dnd-upload-details .remove-file span:after {
  margin-top: 7px;
}
.wpcf7 .dnd-upload-status .dnd-progress-bar span,
.salesforce-form .dnd-upload-status .dnd-progress-bar span {
  background: #77C2A1;
}
.wpcf7 .dnd-upload-status.complete .dnd-progress-bar,
.salesforce-form .dnd-upload-status.complete .dnd-progress-bar {
  display: none;
}
.wpcf7 .bottom-section,
.salesforce-form .bottom-section {
  margin: 30px 0 0 20px;
  /* Customize the label (the container) */
  /* Hide the browser's default checkbox */
  /* Create a custom checkbox */
  /* On mouse-over, add a grey background color */
  /* When the checkbox is checked, add a blue background */
  /* Create the checkmark/indicator (hidden when not checked) */
  /* Show the checkmark when checked */
  /* Style the checkmark/indicator */
}
@media screen and (max-width: 560px) {
  .wpcf7 .bottom-section,
  .salesforce-form .bottom-section {
    margin: 30px 0 0 5px;
  }
}
.wpcf7 .bottom-section .acceptance .wpcf7-list-item,
.salesforce-form .bottom-section .acceptance .wpcf7-list-item {
  margin: 0 0 0 30px;
  font-size: 14px;
  color: #000;
}
.wpcf7 .bottom-section .acceptance .wpcf7-list-item a,
.salesforce-form .bottom-section .acceptance .wpcf7-list-item a {
  text-decoration: underline;
  color: #000;
}
.wpcf7 .bottom-section .mc4wp-checkbox label,
.wpcf7 .bottom-section .wpcf7-list-item label,
.salesforce-form .bottom-section .mc4wp-checkbox label,
.salesforce-form .bottom-section .wpcf7-list-item label {
  display: block;
  position: relative;
  margin-bottom: 0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.wpcf7 .bottom-section .mc4wp-checkbox label a,
.wpcf7 .bottom-section .wpcf7-list-item label a,
.salesforce-form .bottom-section .mc4wp-checkbox label a,
.salesforce-form .bottom-section .wpcf7-list-item label a {
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.wpcf7 .bottom-section .mc4wp-checkbox label a:hover,
.wpcf7 .bottom-section .wpcf7-list-item label a:hover,
.salesforce-form .bottom-section .mc4wp-checkbox label a:hover,
.salesforce-form .bottom-section .wpcf7-list-item label a:hover {
  color: #000;
}
.wpcf7 .bottom-section .mc4wp-checkbox label input,
.wpcf7 .bottom-section .wpcf7-list-item label input,
.salesforce-form .bottom-section .mc4wp-checkbox label input,
.salesforce-form .bottom-section .wpcf7-list-item label input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.wpcf7 .bottom-section .mc4wp-checkbox label span::before,
.wpcf7 .bottom-section .wpcf7-list-item-label::before,
.salesforce-form .bottom-section .mc4wp-checkbox label span::before,
.salesforce-form .bottom-section .wpcf7-list-item-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -30px;
  height: 18px;
  width: 18px;
  background-color: transparent;
  border: 1px solid #000;
  border-radius: 2px;
}
.wpcf7 .bottom-section .mc4wp-checkbox label:hover input ~ span,
.wpcf7 .bottom-section .wpcf7-list-item label:hover input ~ .wpcf7-list-item-label,
.salesforce-form .bottom-section .mc4wp-checkbox label:hover input ~ span,
.salesforce-form .bottom-section .wpcf7-list-item label:hover input ~ .wpcf7-list-item-label {
  background-color: transparent;
}
.wpcf7 .bottom-section .mc4wp-checkbox label input:checked ~ span::before,
.wpcf7 .bottom-section .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::before,
.salesforce-form .bottom-section .mc4wp-checkbox label input:checked ~ span::before,
.salesforce-form .bottom-section .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::before {
  background-color: transparent;
  border: 1px solid #000;
}
.wpcf7 .bottom-section .mc4wp-checkbox span:after,
.wpcf7 .bottom-section .wpcf7-list-item-label:after,
.salesforce-form .bottom-section .mc4wp-checkbox span:after,
.salesforce-form .bottom-section .wpcf7-list-item-label:after {
  content: "";
  position: absolute;
  display: none;
}
.wpcf7 .bottom-section .mc4wp-checkbox label input:checked ~ span::after,
.wpcf7 .bottom-section .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::after,
.salesforce-form .bottom-section .mc4wp-checkbox label input:checked ~ span::after,
.salesforce-form .bottom-section .wpcf7-list-item label input:checked ~ .wpcf7-list-item-label::after {
  display: block;
}
.wpcf7 .bottom-section .mc4wp-checkbox label span:after,
.wpcf7 .bottom-section .wpcf7-list-item label .wpcf7-list-item-label:after,
.salesforce-form .bottom-section .mc4wp-checkbox label span:after,
.salesforce-form .bottom-section .wpcf7-list-item label .wpcf7-list-item-label:after {
  left: -24px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #000;
  border-width: 0 1px 1px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.wpcf7 .bottom-section .btn-primary,
.salesforce-form .bottom-section .btn-primary {
  margin-top: 30px;
}
.wpcf7 .bottom-section .wpcf7-spinner,
.salesforce-form .bottom-section .wpcf7-spinner {
  display: none;
}
.wpcf7 textarea,
.salesforce-form textarea {
  width: 100%;
  max-width: 100%;
  color: #000;
  padding: 0 12px;
  height: 74px;
  border: 1px solid #878787;
  border-radius: 0px;
  background: transparent;
  font-size: 14px;
}
.wpcf7 textarea:focus, .wpcf7 textarea:focus-visible,
.salesforce-form textarea:focus,
.salesforce-form textarea:focus-visible {
  outline: none;
  border-radius: 0px;
  box-shadow: 0px 0px 0px;
}
.wpcf7 textarea::-webkit-input-placeholder,
.salesforce-form textarea::-webkit-input-placeholder {
  /* Edge */
  color: #CFCFCF;
}
.wpcf7 textarea:-ms-input-placeholder,
.salesforce-form textarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #CFCFCF;
}
.wpcf7 textarea::placeholder,
.salesforce-form textarea::placeholder {
  color: #CFCFCF;
}
.wpcf7 textarea.wpcf7-valid,
.salesforce-form textarea.wpcf7-valid {
  font-weight: 100 !important;
  border: 1px solid #77C2A1 !important;
}
.wpcf7 .success-message,
.salesforce-form .success-message {
  margin: -1rem -3rem;
}
@media screen and (max-width: 560px) {
  .wpcf7 .success-message,
  .salesforce-form .success-message {
    margin: -1rem -1rem;
  }
}
.wpcf7 .success-message p,
.salesforce-form .success-message p {
  font-size: 24px;
  line-height: 1.2;
}
@media screen and (max-width: 560px) {
  .wpcf7 .success-message p,
  .salesforce-form .success-message p {
    font-size: 20px;
  }
}
.wpcf7 .success-message .tn-secondary.close-popup,
.salesforce-form .success-message .tn-secondary.close-popup {
  margin-top: 40px;
  justify-content: center;
}
@media screen and (max-width: 560px) {
  .wpcf7 .success-message .tn-secondary.close-popup,
  .salesforce-form .success-message .tn-secondary.close-popup {
    margin-top: 20px;
  }
}
.wpcf7 .success-message .green-line,
.salesforce-form .success-message .green-line {
  width: 100%;
  height: 1px;
  background: linear-gradient(151deg, rgba(255, 255, 255, 0) 0%, #29c17a 100%);
  margin: 20px 0px 60px 0px;
}
.wpcf7 .text-box,
.salesforce-form .text-box {
  margin-bottom: 42px;
}
.wpcf7 .text-box .wpcf7-form-control-wrap input,
.salesforce-form .text-box .wpcf7-form-control-wrap input {
  width: 100%;
  max-width: 100%;
  color: #000;
  padding: 0 12px;
  height: 40px;
  border: 1px solid #878787;
  border-radius: 0px;
  background: transparent;
  font-size: 14px;
}
.wpcf7 .text-box .wpcf7-form-control-wrap input:focus, .wpcf7 .text-box .wpcf7-form-control-wrap input:focus-visible,
.salesforce-form .text-box .wpcf7-form-control-wrap input:focus,
.salesforce-form .text-box .wpcf7-form-control-wrap input:focus-visible {
  outline: none;
  border-radius: 0px;
  box-shadow: 0px 0px 0px;
}
.wpcf7 .text-box .wpcf7-form-control-wrap input::-webkit-input-placeholder,
.salesforce-form .text-box .wpcf7-form-control-wrap input::-webkit-input-placeholder {
  /* Edge */
  color: #CFCFCF;
}
.wpcf7 .text-box .wpcf7-form-control-wrap input:-ms-input-placeholder,
.salesforce-form .text-box .wpcf7-form-control-wrap input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #CFCFCF;
}
.wpcf7 .text-box .wpcf7-form-control-wrap input::placeholder,
.salesforce-form .text-box .wpcf7-form-control-wrap input::placeholder {
  color: #CFCFCF;
}
.wpcf7 .select-box,
.salesforce-form .select-box {
  margin-bottom: 42px;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container {
  width: 100% !important;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection {
  display: block;
  height: 40px;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection {
  height: 40px;
  border: 1px solid #878787;
  border-radius: 0;
  transition: all 0.3s ease;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection.wpcf7-valid,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection.wpcf7-valid {
  border: 1px solid #77C2A1 !important;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection.wpcf7-valid .select2-selection__rendered,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection.wpcf7-valid .select2-selection__rendered {
  color: #000;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__rendered,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__rendered {
  height: 100%;
  line-height: 1.4;
  align-items: center;
  display: flex;
  color: #CFCFCF;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow {
  height: 100%;
  line-height: 1.4;
  align-items: center;
  display: flex;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow b,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow b {
  background-color: transparent;
  border: 0;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow:after,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container .selection .select2-selection .select2-selection__arrow:after {
  content: '';
  display: block;
  position: absolute;
  top: 50%;
  right: 20%;
  transform: translate(-50%, -50%);
  width: 14px;
  height: 13px;
  background-image: url(../images/dropdown-arrow.svg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.1s ease;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container.select2-container--open .selection .select2-selection,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container.select2-container--open .selection .select2-selection {
  height: 40px;
  border: 3px solid #ADDAC7;
  border-radius: 0;
}
.wpcf7 .select-box .wpcf7-form-control-wrap .select2-container.select2-container--open .selection .select2-selection .select2-selection__arrow:after,
.salesforce-form .select-box .wpcf7-form-control-wrap .select2-container.select2-container--open .selection .select2-selection .select2-selection__arrow:after {
  background-image: url(../images/dropdown-arrow-up.svg);
}
.wpcf7 .codedropz-upload-handler,
.salesforce-form .codedropz-upload-handler {
  cursor: pointer;
  padding: 0;
  background-color: transparent;
  border: 1px dashed #000;
  border-radius: 0;
}
.wpcf7 .codedropz-upload-handler .codedropz-upload-container,
.salesforce-form .codedropz-upload-handler .codedropz-upload-container {
  display: none;
}
.wpcf7 .codedropz-upload-handler .codedropz-upload-container .codedropz-btn-wrap,
.salesforce-form .codedropz-upload-handler .codedropz-upload-container .codedropz-btn-wrap {
  display: none;
}
.wpcf7 .codedropz-upload-handler .dnd-upload-counter,
.salesforce-form .codedropz-upload-handler .dnd-upload-counter {
  display: none;
}
.wpcf7 .codedropz-upload-handler .dummy-text,
.salesforce-form .codedropz-upload-handler .dummy-text {
  height: 72px;
  text-align: left;
  padding: 10px;
}
.wpcf7 .codedropz-upload-handler .dummy-text h3,
.salesforce-form .codedropz-upload-handler .dummy-text h3 {
  font-size: 18px;
  font-weight: 400;
  color: #3D4764;
  line-height: 1.2;
}
.wpcf7 .codedropz-upload-handler .dummy-text p,
.salesforce-form .codedropz-upload-handler .dummy-text p {
  font-size: 14px;
  color: #C4BFC0;
  line-height: 1.2;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item {
  margin: 0;
  font-size: 11px;
  color: #3D4763;
  padding-top: 24px;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"],
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:before,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:before {
  background-color: #77C2A1;
  border: 1px solid #77C2A1;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:after,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:after {
  display: block;
  position: absolute;
  left: 6px;
  top: 0px;
  width: 6px;
  height: 10px;
  border: solid #FFF;
  border-width: 0 1px 1px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label {
  position: relative;
  padding: 0 0 0 30px;
  align-items: center;
  display: inline-block;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label:before,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label:before {
  cursor: pointer;
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translate(0, -50%);
  height: 18px;
  width: 18px;
  background-color: transparent;
  border: 1px solid #878787;
  border-radius: 2px;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label .readmore,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item label span.wpcf7-list-item-label .readmore {
  padding-top: 10px;
  padding-bottom: 15px;
  display: block;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item a,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item a {
  text-decoration: none;
  color: #000;
  transition: all 0.2s ease;
}
.wpcf7 .wpcf7-acceptance .wpcf7-list-item a:hover,
.salesforce-form .wpcf7-acceptance .wpcf7-list-item a:hover {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.wpcf7 .btn-primary,
.salesforce-form .btn-primary {
  min-width: 120px;
  margin-top: 4px;
}
@media screen and (max-width: 991.9px) {
  .wpcf7 .subscription-form .text-box,
  .salesforce-form .subscription-form .text-box {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 15px;
  }
}
.wpcf7 .subscription-form .wpcf7-acceptance .wpcf7-list-item,
.salesforce-form .subscription-form .wpcf7-acceptance .wpcf7-list-item {
  padding-top: 0;
}

.select2-container--open .select2-dropdown--below, .select2-container--open .select2-dropdown--above {
  padding-top: 32px;
  background: transparent;
  border: 0;
}
.select2-container--open .select2-dropdown--below .select2-results, .select2-container--open .select2-dropdown--above .select2-results {
  background-color: #FFF;
}
.select2-container--open .select2-dropdown--below .select2-results ul.select2-results__options li, .select2-container--open .select2-dropdown--above .select2-results ul.select2-results__options li {
  padding-left: 12px;
  border-bottom: 1px solid #878787;
}
.select2-container--open .select2-dropdown--below .select2-results ul.select2-results__options li.select2-results__option--highlighted, .select2-container--open .select2-dropdown--above .select2-results ul.select2-results__options li.select2-results__option--highlighted {
  background-color: #E4F3EC;
  color: #000;
}
.select2-container--open .select2-dropdown--below .select2-results ul.select2-results__options li.select2-results__option--selected, .select2-container--open .select2-dropdown--above .select2-results ul.select2-results__options li.select2-results__option--selected {
  background-color: #E4F3EC;
}
.select2-container--open .select2-dropdown--below .select2-results ul.select2-results__options li.select2-results__option--selected:after, .select2-container--open .select2-dropdown--above .select2-results ul.select2-results__options li.select2-results__option--selected:after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 13px;
  background-image: url(../images/checked.svg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  margin-left: 25px;
}

.subscription-button {
  min-width: 256px;
  min-height: 58px;
}
@media screen and (max-width: 501px) {
  .subscription-button {
    max-width: 60%;
    min-width: unset !important;
  }
}

form.searchandfilter ul {
  padding: 0;
}
form.searchandfilter ul > li {
  border: 0 !important;
  padding: 0;
}
form.searchandfilter ul > li > ul {
  display: flex;
  width: 100% !important;
  position: initial !important;
  visibility: unset !important;
  height: auto !important;
  border: 0 !important;
  background: transparent !important;
  padding: 0;
}
@media screen and (max-width: 768px) {
  form.searchandfilter ul > li > ul {
    display: block;
  }
}
form.searchandfilter ul > li > ul li {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  position: relative;
  padding-left: 30px !important;
  padding-right: 30px !important;
}
@media screen and (max-width: 768px) {
  form.searchandfilter ul > li > ul li {
    display: inline-block;
  }
}
@media screen and (max-width: 485px) {
  form.searchandfilter ul > li > ul li {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-right: 20px;
  }
}
form.searchandfilter ul > li > ul li:first-child {
  padding-left: 0px;
  display: none;
  visibility: hidden;
}
form.searchandfilter ul > li > ul li.sf-item-0:first-child {
  /*font-weight: 700;*/
  font-weight: normal !important;
}
form.searchandfilter ul > li > ul li.sf-item-0:first-child .sf-label-radio {
  font-weight: normal;
  -webkit-text-stroke-width: 0px;
}
form.searchandfilter ul > li > ul li.sf-item-0:first-child:not(.sf-option-active):hover .sf-label-radio {
  -webkit-text-stroke-width: 1px;
}
form.searchandfilter ul > li > ul li.sf-option-active {
  /*padding: 0;*/
  background: transparent;
}
form.searchandfilter ul > li > ul li.sf-option-active .sf-label-radio {
  /*font-weight: 700;*/
  font-weight: normal;
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
form.searchandfilter ul > li > ul li.sf-option-active .sf-label-radio:after {
  transform: scaleX(1);
}
form.searchandfilter ul > li > ul li.sf-option-active:after {
  transform: scaleX(1);
}
form.searchandfilter ul > li > ul li.sf-option-active:after:hover {
  display: none;
}
form.searchandfilter ul > li > ul li .sf-input-radio {
  display: none;
}
form.searchandfilter ul > li > ul li .sf-label-radio {
  padding: 0;
  font-size: 14px;
  font-weight: 100;
  line-height: 1.2;
  color: #000;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  transition: all 0.2s ease;
}
@media screen and (max-width: 485px) {
  form.searchandfilter ul > li > ul li .sf-label-radio {
    font-size: 14px;
  }
}
form.searchandfilter ul > li > ul li .sf-label-radio:before {
  content: attr(data-text);
  content: attr(data-text)/"";
  height: 0;
  visibility: hidden;
  overflow: hidden;
  user-select: none;
  pointer-events: none;
  font-weight: 700;
}
form.searchandfilter ul > li > ul li .sf-label-radio:after:hover {
  font-weight: 700;
  text-decoration: none;
}
form.searchandfilter ul > li > ul li:after {
  content: '';
}
form.searchandfilter ul > li > ul li:hover .sf-label-radio {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
  text-decoration: none;
}
form.searchandfilter ul > li > ul li:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}
form.searchandfilter ul > li:after {
  display: none;
}

.search-filter-results .no-results-text {
  margin-top: 60px;
  margin-bottom: 60px;
}

.partners-items {
  margin-top: 95px;
}
@media screen and (max-width: 991px) {
  .partners-items {
    margin-top: 50px;
  }
}
.partners-items .partners-item {
  display: inline-flex;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
  flex: 0 0 calc(25% - 30px);
  margin: 0 15px -1px 15px;
  padding: 30px 25px;
  background-color: #FFF;
  transition: all 0.3s linear;
}
@media screen and (max-width: 1199.9px) {
  .partners-items .partners-item {
    padding: 30px 15px;
  }
}
@media screen and (max-width: 991.9px) {
  .partners-items .partners-item {
    flex: 0 0 calc(33.333% - 30px);
    max-width: calc(33.333% - 30px);
  }
}
@media screen and (max-width: 767.9px) {
  .partners-items .partners-item {
    flex: 0 0 calc(100% - 30px);
    max-width: 100%;
    padding: 30px 30px;
  }
  .partners-items .partners-item.hidden-item-mobile {
    opacity: 0;
    display: none;
    -moz-transition-property: opacity;
    -o-transition-property: opacity;
    -webkit-transition-property: opacity;
    transition-property: opacity;
    -moz-transition-duration: 1s;
    -o-transition-duration: 1s;
    -webkit-transition-duration: 1s;
    transition-duration: 1s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .partners-items .partners-item.hidden-item-mobile.active {
    display: inline-block;
    -moz-transition-property: all;
    -o-transition-property: all;
    -webkit-transition-property: all;
    transition-property: all;
    -moz-transition-duration: 0.5s;
    -o-transition-duration: 0.5s;
    -webkit-transition-duration: 0.5s;
    transition-duration: 0.5s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .partners-items .partners-item.hidden-item-mobile.is-open {
    opacity: 1;
  }
}
.partners-items .partners-item .card {
  width: 100%;
  border: 0;
  background-color: transparent;
  padding: 0;
}
.partners-items .partners-item .card .card-photo {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: start;
  margin-bottom: 15px;
}
.partners-items .partners-item .card .card-photo img {
  width: 100%;
  /*max-height: 70px;*/
  object-fit: contain;
  object-position: center;
  filter: grayscale(1);
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -moz-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  -webkit-transition-timing-function: linear;
  transition-timing-function: linear;
}
.partners-items .partners-item .card .card-body {
  padding: 0;
  font-size: 14px;
}
.partners-items .partners-item .card .card-body h3 {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 0;
}
.partners-items .partners-item .card .card-body p {
  font-size: 11px;
}
.partners-items .partners-item .card .card-body a {
  color: #000;
  text-decoration: none;
  transition: all 0.3s linear;
}
.partners-items .partners-item .card .card-body a:hover {
  color: #77C2A1;
}
.partners-items .partners-item:hover .card .card-photo img {
  filter: grayscale(0);
}
.partners-items .partners-item:hover .card .card-body a.text-link {
  color: #77C2A1;
}

.team-items {
  margin-top: 60px;
}
.team-items .team-item {
  display: inline-flex;
  flex: 0 0 calc(25% - 30px);
  margin: 0 15px -1px 15px;
  padding: 0;
  background-color: transparent;
  transition: all 0.3s linear;
}
@media screen and (max-width: 1199.9px) {
  .team-items .team-item {
    flex: 0 0 calc(33.33% - 30px);
    max-width: calc(33.33% - 30px);
  }
}
@media screen and (max-width: 767.9px) {
  .team-items .team-item {
    flex: 0 0 calc(50% - 30px);
    max-width: calc(50% - 30px);
  }
}
@media screen and (max-width: 450.9px) {
  .team-items .team-item {
    	  /*
          flex: 0 0 calc(100% - 30px);
          max-width: calc(100% - 30px);
    	  */
  }
}
.team-items .team-item .card {
  border: 0;
  background-color: transparent;
  padding: 0;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card {
    display: block;
  }
}
@media screen and (max-width: 450.9px) {
  .team-items .team-item .card {
    width: 100%;
  }
}
.team-items .team-item .card .card-photo {
  display: flex;
  width: 100%;
  height: 170px;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
@media screen and (max-width: 450.9px) {
  .team-items .team-item .card .card-photo {
    height: auto;
  }
}
.team-items .team-item .card .card-photo img {
  height: 170px;
  object-fit: cover;
  object-position: center;
  transition: all 0.5s ease;
}
@media screen and (max-width: 450.9px) {
  .team-items .team-item .card .card-photo img {
    width: 100%;
    height: auto !important;
  }
}
.team-items .team-item .card .card-body {
  padding: 20px;
  padding-bottom: 0;
  font-size: 14px;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card .card-body {
    padding: 10px 0px;
    float: left;
    width: 80%;
  }
}
.team-items .team-item .card .card-body h3 {
  font-size: 14px;
  line-height: 120% !important;
  font-weight: bold;
  margin-bottom: 10px;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card .card-body h3 {
    word-spacing: 100vw;
    font-size: 11px;
  }
}
.team-items .team-item .card .card-body p {
  margin-bottom: 20px;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card .card-body p {
    font-size: 11px;
  }
}
.team-items .team-item .card .card-body p .sow-icon-fontawesome {
  font-size: 25px;
  line-height: 120%;
  color: #878787;
  margin-right: 10px;
  transition: all 0.3s linear;
}
.team-items .team-item .card .card-body a {
  color: #000;
  text-decoration: none;
  transition: all 0.3s linear;
}
.team-items .team-item .card .card-body a:hover {
  color: #77C2A1;
}
.team-items .team-item .card .card-footer {
  padding: 20px;
  padding-top: 0px;
  padding-bottom: 40px;
  background: none;
  border: 0px;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card .card-footer {
    float: right;
    width: 19%;
    padding: 10px 0px;
    text-align: right;
  }
}
.team-items .team-item .card .card-footer .sow-icon-fontawesome {
  font-size: 25px;
  line-height: 120%;
  color: #878787;
  margin-right: 10px;
  transition: all 0.3s linear;
}
@media screen and (max-width: 991px) {
  .team-items .team-item .card .card-footer .sow-icon-fontawesome {
    margin-right: 0px;
  }
}
.team-items .team-item .card .card-footer a:hover .sow-icon-fontawesome {
  color: #77C2A1;
}
.team-items .team-item:hover .card .card-photo img {
  transform: scale(1.05);
}
.team-items .team-item:hover .card .card-body a.text-link {
  color: #77C2A1;
}
.team-items .team-item:hover .card .card-body p .sow-icon-fontawesome {
  color: #77C2A1;
}

form.searchandfilter .sf-field-taxonomy-news_categories,
form.searchandfilter .sf-field-taxonomy-team_categories,
form.searchandfilter .sf-field-taxonomy-distributor_categories,
form.searchandfilter .sf-field-taxonomy-partners_categories {
  padding-left: 0 !important;
}
form.searchandfilter .sf-field-taxonomy-news_categories:before,
form.searchandfilter .sf-field-taxonomy-team_categories:before,
form.searchandfilter .sf-field-taxonomy-distributor_categories:before,
form.searchandfilter .sf-field-taxonomy-partners_categories:before {
  display: none;
}
form.searchandfilter .sf-field-taxonomy-news_categories > h4,
form.searchandfilter .sf-field-taxonomy-team_categories > h4,
form.searchandfilter .sf-field-taxonomy-distributor_categories > h4,
form.searchandfilter .sf-field-taxonomy-partners_categories > h4 {
  display: inline-flex;
  vertical-align: bottom;
  padding: 0;
  margin: 0;
  font-size: 14px;
  line-height: 1.2 !important;
  font-weight: bold;
  margin-right: 30px;
}
@media screen and (max-width: 485px) {
  form.searchandfilter .sf-field-taxonomy-news_categories > h4,
  form.searchandfilter .sf-field-taxonomy-team_categories > h4,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > h4,
  form.searchandfilter .sf-field-taxonomy-partners_categories > h4 {
    margin-right: 20px;
  }
}
form.searchandfilter .sf-field-taxonomy-news_categories > ul,
form.searchandfilter .sf-field-taxonomy-team_categories > ul,
form.searchandfilter .sf-field-taxonomy-distributor_categories > ul,
form.searchandfilter .sf-field-taxonomy-partners_categories > ul {
  display: inline;
  vertical-align: bottom;
}
form.searchandfilter .sf-field-taxonomy-news_categories ul li:before,
form.searchandfilter .sf-field-taxonomy-team_categories ul li:before,
form.searchandfilter .sf-field-taxonomy-distributor_categories ul li:before,
form.searchandfilter .sf-field-taxonomy-partners_categories ul li:before {
  display: none;
}
form.searchandfilter .sf-field-taxonomy-news_categories ul li:first-child,
form.searchandfilter .sf-field-taxonomy-team_categories ul li:first-child,
form.searchandfilter .sf-field-taxonomy-distributor_categories ul li:first-child,
form.searchandfilter .sf-field-taxonomy-partners_categories ul li:first-child {
  display: inline-flex !important;
  visibility: visible !important;
}
form.searchandfilter .sf-field-taxonomy-news_categories ul li:first-child.sf-option-active .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-team_categories ul li:first-child.sf-option-active .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-distributor_categories ul li:first-child.sf-option-active .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-partners_categories ul li:first-child.sf-option-active .sf-label-radio {
  /*
  font-weight: 700;
  color: #000;
  -webkit-text-fill-color: #000;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #000;
  text-decoration: none;
  */
}
form.searchandfilter .sf-field-taxonomy-news_categories ul li:first-child .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-team_categories ul li:first-child .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-distributor_categories ul li:first-child .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-partners_categories ul li:first-child .sf-label-radio {
  /*font-weight: 700;*/
  color: #000;
  -webkit-text-fill-color: #000;
  -webkit-text-stroke-width: 0px;
  -webkit-text-stroke-color: #000;
  text-decoration: none;
}
form.searchandfilter .sf-field-taxonomy-news_categories ul li:first-child:hover .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-team_categories ul li:first-child:hover .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-distributor_categories ul li:first-child:hover .sf-label-radio,
form.searchandfilter .sf-field-taxonomy-partners_categories ul li:first-child:hover .sf-label-radio {
  -webkit-text-stroke-width: 0px !important;
}
@media screen and (max-width: 991px) {
  form.searchandfilter .sf-field-taxonomy-news_categories > h4,
  form.searchandfilter .sf-field-taxonomy-team_categories > h4,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > h4,
  form.searchandfilter .sf-field-taxonomy-partners_categories > h4 {
    width: 100%;
    padding: 15px 10px;
    margin-right: 0px;
    display: block;
    position: relative;
    border: 1px solid #878787 !important;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > h4::after,
  form.searchandfilter .sf-field-taxonomy-team_categories > h4::after,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > h4::after,
  form.searchandfilter .sf-field-taxonomy-partners_categories > h4::after {
    content: '';
    width: 10px;
    height: 6px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    background-image: url("../images/dropdown-arrow.svg");
    background-repeat: no-repeat;
    background-size: contain;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul {
    display: none;
    /*position: absolute !important;*/
        /*
        visibility: hidden;
        opacity: 0;
        height: 0;
        @include transition-property(all);
				@include transition-duration(0.2s);
				@include transition-timing-function(ease-in-out);
        */
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0 {
    display: block !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-right: 0px !important;
    border-bottom: 1px solid #878787 !important;
    border-left: 1px solid #878787 !important;
    border-right: 1px solid #878787 !important;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0 .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0 .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0 .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0 .sf-label-radio {
    display: block;
    padding-left: 10px !important;
    padding-right: 0px !important;
    padding-top: 15px;
    padding-bottom: 15px !important;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0:first-child,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0:first-child,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0:first-child,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0:first-child {
    /*border-top: 1px solid #878787 !important;*/
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0.sf-option-active,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0.sf-option-active,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0.sf-option-active,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0.sf-option-active {
    background-color: #E4F3EC;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio {
    color: #000000;
    -webkit-text-fill-color: unset;
    -webkit-text-stroke-width: 0px;
    -webkit-text-stroke-color: unset;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio::after,
  form.searchandfilter .sf-field-taxonomy-team_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio::after,
  form.searchandfilter .sf-field-taxonomy-distributor_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio::after,
  form.searchandfilter .sf-field-taxonomy-partners_categories > ul > li.sf-level-0.sf-option-active .sf-label-radio::after {
    content: '';
    width: 13px;
    height: 13px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 14px;
    background-image: url("../images/checked.svg");
    background-repeat: no-repeat;
    background-size: contain;
  }
  form.searchandfilter .sf-field-taxonomy-news_categories.active h4::after,
  form.searchandfilter .sf-field-taxonomy-team_categories.active h4::after,
  form.searchandfilter .sf-field-taxonomy-distributor_categories.active h4::after,
  form.searchandfilter .sf-field-taxonomy-partners_categories.active h4::after {
    transform: rotate(180deg);
  }
  form.searchandfilter .sf-field-taxonomy-news_categories.active > ul,
  form.searchandfilter .sf-field-taxonomy-team_categories.active > ul,
  form.searchandfilter .sf-field-taxonomy-distributor_categories.active > ul,
  form.searchandfilter .sf-field-taxonomy-partners_categories.active > ul {
    display: block;
    /*
    visibility: visible;
    opacity: 1;
    height: auto;
    */
  }
}
form.searchandfilter .sf-field-taxonomy-team_categories ul li:first-child .sf-label-radio {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 0px;
  -webkit-text-stroke-color: #77C2A1;
}

.post-list-news {
  width: calc(100% + 30px);
  column-count: 3;
  column-gap: 0;
  orphans: 1;
  widows: 1;
  margin-top: 65px;
  /*
  .owl-stage{
    columns: 3;
    -webkit-columns: 3;
    -moz-columns: 3;
    .owl-item{
      
    }
  }
  */
}
@media screen and (max-width: 768px) {
  .post-list-news {
    width: 100%;
    margin: auto;
    margin-top: 50px;
  }
  .post-list-news.post-list-owl, .post-list-news.owl-theme {
    width: unset;
    column-count: unset;
    column-gap: unset;
    orphans: unset;
    widows: unset;
    text-align: left;
    overflow: hidden;
  }
  .post-list-news.post-list-owl .owl-stage-outer, .post-list-news.owl-theme .owl-stage-outer {
    margin-left: -40px;
    margin-bottom: 0px;
  }
  .post-list-news.post-list-owl .owl-stage, .post-list-news.owl-theme .owl-stage {
    display: flex;
    justify-content: start;
  }
  .post-list-news.post-list-owl .owl-item, .post-list-news.owl-theme .owl-item {
    display: inline-flex;
  }
  .post-list-news.post-list-owl .owl-item .card-body, .post-list-news.owl-theme .owl-item .card-body {
    padding: 25px 20px 15px 20px;
  }
  .post-list-news.post-list-owl .owl-nav, .post-list-news.owl-theme .owl-nav {
    margin-top: 0px;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-], .post-list-news.owl-theme .owl-nav [class*=owl-] {
    border: 0;
    border-radius: 50%;
    background-color: #d6e8e1;
    display: inline-flex;
    width: 50px;
    height: 50px;
    transition: all 0.3s ease;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-]:hover, .post-list-news.owl-theme .owl-nav [class*=owl-]:hover {
    background-color: #77C2A1;
  }
}
@media screen and (max-width: 768px) and (max-width: 769px) {
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-prev, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-prev {
    float: left;
  }
}
@media screen and (max-width: 768px) {
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-prev:before, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-prev:before {
    content: '';
    display: block;
    background-image: url("../images/arrow-left-green.svg");
    width: 30px;
    height: 30px;
    background-size: 30px 30px;
    margin: auto;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-prev:hover:before, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-prev:hover:before {
    background-image: url("../images/arrow-left-white.svg");
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-prev > span, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-prev > span {
    display: none !important;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-next, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-next {
    margin-left: 8px;
  }
}
@media screen and (max-width: 768px) and (max-width: 769px) {
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-next, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-next {
    float: right;
    margin-right: 0px;
  }
}
@media screen and (max-width: 768px) {
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-next:before, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-next:before {
    content: '';
    display: block;
    background-image: url("../images/arrow-right-green.svg");
    width: 30px;
    height: 30px;
    background-size: 30px 30px;
    margin: auto;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-next:hover:before, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-next:hover:before {
    background-image: url("../images/arrow-right-white.svg");
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-].owl-next > span, .post-list-news.owl-theme .owl-nav [class*=owl-].owl-next > span {
    display: none !important;
  }
  .post-list-news.post-list-owl .owl-nav [class*=owl-] span, .post-list-news.owl-theme .owl-nav [class*=owl-] span {
    font-size: 0;
  }
}
.post-list-news .card {
  border: 0;
  background-color: transparent;
  display: none;
  width: 100%;
  padding: 0 15px;
  margin-bottom: 42px;
  border-radius: 0px;
  overflow: hidden;
  -moz-transition-property: opacity;
  -o-transition-property: opacity;
  -webkit-transition-property: opacity;
  transition-property: opacity;
  -moz-transition-duration: 1s;
  -o-transition-duration: 1s;
  -webkit-transition-duration: 1s;
  transition-duration: 1s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
@media screen and (max-width: 767px) {
  .post-list-news .card {
    padding: 0;
  }
}
.post-list-news .card:not(.is-open) {
  opacity: 0;
}
.post-list-news .card.is-visible {
  display: inline-block;
}
.post-list-news .card.is-open {
  margin-top: 0;
  opacity: 1;
}
.post-list-news .card a {
  text-decoration: none;
  color: #000000;
}
.post-list-news .card .image-wrapper {
  max-width: 390px;
  height: 227px;
  overflow: hidden;
}
@media screen and (max-width: 768px) {
  .post-list-news .card .image-wrapper {
    padding: 0;
    width: 100%;
    max-width: unset;
  }
}
@media screen and (max-width: 767px) {
  .post-list-news .card .image-wrapper {
    height: auto;
  }
}
.post-list-news .card .image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top center;
  transition: all 0.5s ease;
}
.post-list-news .card .card-body {
  padding: 25px 40px;
  transition: all 0.3s linear;
  height: 100%;
}
@media screen and (max-width: 992px) {
  .post-list-news .card .card-body {
    background-color: #FFF;
    height: auto;
  }
}
.post-list-news .card .card-body .post-date {
  font-size: 11px;
  color: #77C2A1;
  margin-bottom: 15px;
}
.post-list-news .card .card-body .card-title {
  font-weight: 700;
  line-height: 1.6 !important;
  max-height: 60px;
  margin-bottom: 15px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
@media screen and (max-width: 768px) {
  .post-list-news .card .card-body .card-title {
    font-size: 14px;
  }
}
.post-list-news .card .card-body .card-text {
  font-size: 14px;
  font-weight: normal;
  line-height: 2 !important;
  max-height: 56px;
  margin-bottom: 18px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
@media screen and (max-width: 768px) {
  .post-list-news .card .card-body .card-text {
    -webkit-appearance: none;
    font-size: 11px;
    max-height: 40px;
  }
}
.post-list-news .card .card-body .arrow-link {
  font-size: 0;
  background: transparent;
  border: 0;
}
@media screen and (max-width: 992px) {
  .post-list-news .card .card-body .arrow-link:after {
    opacity: 1 !important;
  }
}
.post-list-news .card .card-body .arrow-link:after {
  content: '';
  display: inline-flex;
  margin-left: 0;
  width: 30px;
  height: 24px;
  background: transparent;
  background-image: url(../images/arrow-right-green.svg);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  opacity: 0;
  transition: all 0.3s linear;
}
.post-list-news .card:hover .image-wrapper img {
  transform: scale(1.05);
}
.post-list-news .card:hover .card-body {
  background-color: #F2F2F2;
}
.post-list-news .card:hover .card-body .arrow-link {
  background: transparent !important;
  border: 0;
}
.post-list-news .card:hover .card-body .arrow-link::after {
  opacity: 1;
}

@media (min-width: 1199px) {
  .post-list-owl .owl-carousel {
    display: block;
  }
  .post-list-owl .owl-stage {
    width: 100% !important;
    transform: unset !important;
    columns: 3;
    -webkit-columns: 3;
    -moz-columns: 3;
  }
  .post-list-owl .owl-nav,
  .post-list-owl .owl-dots {
    display: none;
    visibility: hidden;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .post-list-owl .owl-stage {
    width: 100% !important;
    transform: unset !important;
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
  }
  .post-list-owl .owl-nav,
  .post-list-owl .owl-dots {
    display: none;
    visibility: hidden;
  }
}
@media (max-width: 991px) {
  .post-list-owl .number-repeater-item-warp {
    width: 253px;
    margin-bottom: 0px;
  }
  .post-list-owl .owl-stage-outer {
    margin-bottom: 39px;
  }
  .post-list-owl .owl-nav {
    display: block;
    visibility: visible;
    text-align: left;
    margin: 0;
    padding: 0;
  }
  .post-list-owl .owl-nav .owl-prev,
  .post-list-owl .owl-nav .owl-next {
    width: 50px;
    height: 50px;
    background-color: rgba(104, 194, 159, 0.2);
    border-radius: 100%;
    opacity: 1;
    position: relative;
    padding: 0;
    margin-right: 8px;
  }
  .post-list-owl .owl-nav .owl-prev span,
  .post-list-owl .owl-nav .owl-next span {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .post-list-owl .owl-nav .owl-prev span {
    background-image: url("../images/arrow-left-green.svg");
  }
  .post-list-owl .owl-nav .owl-next span {
    background-image: url("../images/arrow-right-green.svg");
  }
  .post-list-owl .owl-nav [class*=owl-]:hover {
    background-color: rgba(104, 194, 159, 0.2);
  }
}
.news-result .button-area {
  padding-top: 80px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .news-result .button-area {
    padding-top: 0px;
  }
}

.single-partners .gobacktoblog, .single-synchropatch .gobacktoblog {
  margin: 90px 0 40px 0;
}
.single-partners .gobacktoblog a.btn-back, .single-synchropatch .gobacktoblog a.btn-back {
  margin-left: -5px;
}
@media screen and (max-width: 767px) {
  .single-partners .gobacktoblog a.btn-back, .single-synchropatch .gobacktoblog a.btn-back {
    justify-content: left;
  }
}
.single-partners .header-wrapper, .single-synchropatch .header-wrapper {
  display: table;
  align-items: center;
  position: relative;
}
.single-partners .header-wrapper::before, .single-synchropatch .header-wrapper::before {
  content: '';
  display: table-cell;
  background: url("../images/subscribe-vector-left.svg") no-repeat;
  background-position: left;
  background-size: contain;
  width: 15px;
  height: 100%;
  left: -10px;
}
.single-partners .header-wrapper::after, .single-synchropatch .header-wrapper::after {
  content: '';
  display: table-cell;
  background: url("../images/subscribe-vector-right.svg") no-repeat;
  background-position: right;
  background-size: contain;
  width: 15px;
  height: 100%;
  right: -10px;
}
.single-partners .header-wrapper h1, .single-partners .header-wrapper h3, .single-synchropatch .header-wrapper h1, .single-synchropatch .header-wrapper h3 {
  display: inline-block;
  font-size: 32px;
  font-weight: 700;
  line-height: 42px;
  color: #77C2A1;
  margin: 0;
  padding-left: 5px;
  padding-right: 5px;
}
@media screen and (max-width: 767px) {
  .single-partners .header-wrapper h1, .single-partners .header-wrapper h3, .single-synchropatch .header-wrapper h1, .single-synchropatch .header-wrapper h3 {
    padding: 0 10px;
  }
}
.single-partners .entry-content, .single-synchropatch .entry-content {
  margin-top: 105px;
  padding-left: 100px;
}
@media screen and (max-width: 991px) {
  .single-partners .entry-content, .single-synchropatch .entry-content {
    padding-left: 0px;
  }
}
@media screen and (max-width: 768px) {
  .single-partners .entry-content, .single-synchropatch .entry-content {
    margin-top: 50px;
  }
}
.single-partners .entry-content h4, .single-synchropatch .entry-content h4 {
  font-weight: 700;
  line-height: 1.2;
  color: #77C2A1;
}

.products-list .products-item a {
  color: #000000;
  text-decoration: none;
  display: block;
  position: relative;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.products-list .products-item a::after {
  width: 27px;
  height: 28px;
  content: '';
  position: absolute;
  background: url("../images/arrow-right-black.svg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  top: 50%;
  transform: translateY(-50%);
  right: 10px;
  opacity: 0;
  visibility: hidden;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
@media screen and (max-width: 576px) {
  .products-list .products-item a::after {
    right: 0px;
  }
}
.products-list .products-item a:hover::after {
  opacity: 1;
  visibility: visible;
}
.products-list .products-item .card {
  border: 0px;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
  border-radius: 0px;
  background: none;
}
.products-list .products-item .card .thumb {
  padding-left: 15px;
  padding-right: 15px;
}
.products-list .products-item .card .card-body {
  width: 214px;
  max-width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-right: 15px;
  padding-left: 15px;
}
.products-list .products-item .card .card-title {
  font-weight: bold;
  margin-bottom: 0px;
}
@media screen and (min-width: 992px) {
  .products-list .products-item:nth-child(n+4) .card {
    border-top: 0px;
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .products-list .products-item:nth-child(n+3) .card {
    border-top: 0px;
  }
}
@media screen and (max-width: 767px) {
  .products-list .products-item:nth-child(n+2) .card {
    border-top: 0px;
  }
}
@media screen and (max-width: 768px) {
  .products-list .products-item.port-a-patch {
    order: 1;
  }
  .products-list .products-item.patchliner {
    order: 3;
  }
  .products-list .products-item.syncropatch-384 {
    order: 4;
  }
  .products-list .products-item.port-a-patch-mini {
    order: 2;
  }
  .products-list .products-item.buffer-solutions {
    order: 5;
  }
}
.products-list.automated-patch-clamp .products-item a:hover {
  background-color: #D2D9E8;
}
.products-list.membrane-biophysics .products-item a:hover {
  background-color: #F3F2F7;
}
.products-list.cell-monitoring .products-item a:hover {
  background-color: #E9F5F5;
}

.single-products .productTabMenu {
  background-color: #66BBC1;
  position: relative;
  opacity: 1;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  transition-duration: 0.5s;
  -moz-transition-timing-function: ease;
  -o-transition-timing-function: ease;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
@media screen and (max-width: 991px) {
  .single-products .productTabMenu {
    position: absolute;
    width: 100%;
    z-index: 9;
  }
}
.single-products .productTabMenu .nav {
  border: 0px;
}
.single-products .productTabMenu .nav .nav-item {
  margin-bottom: 0px;
}
.single-products .productTabMenu .nav .nav-link {
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 28px;
  text-decoration: none;
  padding: 17px 0px;
  background: none;
  border: 0px;
  position: relative;
}
.single-products .productTabMenu .nav .nav-link span {
  display: block;
}
.single-products .productTabMenu .nav .nav-link span::before {
  display: block;
  content: attr(data-text);
  font-weight: bold;
  height: 0px;
  color: transparent;
  overflow: hidden;
  visibility: hidden;
}
.single-products .productTabMenu .nav .nav-link:hover::before, .single-products .productTabMenu .nav .nav-link:hover::after, .single-products .productTabMenu .nav .nav-link.active::before, .single-products .productTabMenu .nav .nav-link.active::after {
  opacity: 1;
}
.single-products .productTabMenu .nav .nav-link:hover span, .single-products .productTabMenu .nav .nav-link.active span {
  font-weight: bold;
}
.single-products .productTabMenu .nav .nav-link::before, .single-products .productTabMenu .nav .nav-link::after {
  content: '';
  width: 8px;
  height: 23px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.single-products .productTabMenu .nav .nav-link::before {
  background: url("../images/product-menu-hover-left.svg") no-repeat left center;
  left: -10px;
}
.single-products .productTabMenu .nav .nav-link::after {
  background: url("../images/product-menu-hover-right.svg") no-repeat right center;
  right: -10px;
}
@media screen and (max-width: 991px) {
  .single-products .productTabMenu .nav .nav-item:first-child a .toggle-arrow {
    width: 100%;
    height: 100%;
    display: inline-block;
    visibility: visible;
    position: absolute;
    top: 0;
    right: 0px;
    background: url("../images/arrow-down-black.svg") no-repeat right center;
    -moz-transition-property: all;
    -o-transition-property: all;
    -webkit-transition-property: all;
    transition-property: all;
    -moz-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .single-products .productTabMenu .nav .nav-item:not(:first-child) {
    visibility: hidden;
    opacity: 0;
    z-index: 0;
    position: absolute;
    -moz-transition-property: all;
    -o-transition-property: all;
    -webkit-transition-property: all;
    transition-property: all;
    -moz-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .single-products .productTabMenu .nav .nav-item:not(:first-child) a {
    visibility: hidden;
  }
  .single-products .productTabMenu .nav .nav-link {
    padding-top: 6px;
    padding-bottom: 6px;
  }
  .single-products .productTabMenu .nav .nav-link::before, .single-products .productTabMenu .nav .nav-link::after {
    display: none;
    visibility: hidden;
  }
  .single-products .productTabMenu .nav .nav-link:hover {
    -webkit-text-stroke-width: 0px;
  }
  .single-products .productTabMenu .nav .nav-link.active {
    -webkit-text-stroke-width: 0px;
    font-weight: 700;
  }
  .single-products .productTabMenu .nav.active .nav-item:first-child a .toggle-arrow {
    -moz-transform: rotateX(180deg);
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
  }
  .single-products .productTabMenu .nav.active .nav-item:not(:first-child) {
    visibility: visible;
    opacity: 1;
    z-index: 1;
    position: relative;
  }
  .single-products .productTabMenu .nav.active .nav-item:not(:first-child) a {
    visibility: visible;
  }
  .single-products .productTabMenu .nav.active .nav-item:not(:first-child)::before {
    content: '';
    height: 1px;
    width: 9999em;
    position: absolute;
    left: -999em;
    right: -999em;
    background-color: #000000;
  }
  .single-products .productTabMenu .nav.active .nav-item:last-child::after {
    content: '';
    height: 1px;
    width: 9999em;
    position: absolute;
    left: -999em;
    right: -999em;
    background-color: #000000;
  }
}
.single-products .product-content .product-content-head {
  padding-bottom: 110px;
}
@media screen and (max-width: 767px) {
  .single-products .product-content .product-content-head {
    padding-bottom: 50px;
  }
}
.single-products .product-content .add_space {
  padding-top: 87px;
}
.single-products .product-content .back-overview {
  font-size: 14px;
  line-height: 32px;
  color: #878787;
  padding: 0;
  margin-left: -5px;
  margin-bottom: 30px;
  text-decoration: none;
}
.single-products .product-content .back-overview::before {
  content: '';
  width: 32px;
  height: 32px;
  margin-right: 6px;
  display: inline-block;
  vertical-align: middle;
  background: url("../images/arrow-product-left-back.svg") no-repeat center;
}
.single-products .product-content .back-overview:hover {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.single-products .product-content .back-overview:hover::before {
  background: url("../images/arrow-product-left-green.svg");
}
.single-products .product-content .product-subtitle {
  font-weight: 700;
  color: #008D97;
  padding-left: 15px;
}
@media screen and (max-width: 991px) {
  .single-products .product-content .product-subtitle {
    margin-bottom: 55px;
  }
}
.single-products .product-content .product-subtitle span {
  position: relative;
}
.single-products .product-content .product-subtitle span::before, .single-products .product-content .product-subtitle span::after {
  content: '';
  width: 9px;
  height: 36px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-size: cover;
}
.single-products .product-content .product-subtitle span::before {
  background-color: #008D97;
  -webkit-mask-image: url("../images/product-sub-title-left.svg");
  mask-image: url("../images/product-sub-title-left.svg");
  left: -15px;
}
.single-products .product-content .product-subtitle span::after {
  background-color: #008D97;
  -webkit-mask-image: url("../images/product-sub-title-right.svg");
  mask-image: url("../images/product-sub-title-right.svg");
  right: -15px;
}
.single-products .product-content .product-subtitle span.blue::before {
  background-color: #1D418E;
}
.single-products .product-content .product-subtitle span.blue::after {
  background-color: #1D418E;
}
.single-products .product-content .product-subtitle span.purple::before {
  background-color: #8F7FB6;
}
.single-products .product-content .product-subtitle span.purple::after {
  background-color: #8F7FB6;
}
.single-products .product-content .rating-section > .panel-grid-cell:nth-child(2) {
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  overflow: hidden;
}
@media (min-width: 991px) and (max-width: 1199px) {
  .single-products .product-content .rating-section > .panel-grid-cell {
    /*
    &.panel-grid-cell-empty{
        display: none;
        visibility: hidden;
    }
    */
  }
}
@media screen and (max-width: 991px) {
  .single-products .product-content .rating-section > .panel-grid-cell:nth-child(2) {
    /*border-bottom: 0px;*/
  }
  .single-products .product-content .rating-section > .panel-grid-cell:nth-child(3) {
    border-top: 0px;
  }
}
.single-products .product-content .rating-section #selectscience-reviews {
  margin-top: -1.7em;
}
@media screen and (max-width: 767px) {
  .single-products .product-content .rating-section #selectscience-reviews {
    height: 220px;
  }
}
@media screen and (max-width: 991px) {
  .single-products .product-content h1 {
    font-size: 32px;
  }
  .single-products .product-content .characters-max-content p {
    font-size: 14px !important;
  }
}
.single-products .product-contact .container {
  background-color: #f2f2f2;
  position: relative;
}
.single-products .product-contact .container::after {
  left: -999em;
  content: '';
  display: block;
  position: absolute;
  width: 999em;
  top: 0;
  bottom: 0;
  background-color: inherit;
}

.single-news .top-menu .news-menu a {
  transition-timing-function: ease-in-out;
  color: #77C2A1;
}

.news-singlepage {
  padding-top: 87px;
  margin-bottom: 210px;
}
@media screen and (max-width: 767px) {
  .news-singlepage {
    margin-bottom: 60px;
  }
}
.news-singlepage .entry-title {
  font-weight: bold;
  margin-bottom: 63px;
}
.news-singlepage .section-headline {
  color: #77C2A1;
  font-weight: bold;
}
.news-singlepage .pd-left-col {
  padding-left: 35%;
}
@media screen and (max-width: 991px) {
  .news-singlepage .pd-left-col {
    padding-left: 0%;
  }
}
.news-singlepage .caption {
  color: #77C2A1;
  font-size: 14px;
  line-height: 26px;
  font-weight: 700;
}
.news-singlepage .toppage .backbutton a {
  font-size: 14px;
  color: #878787;
  text-decoration: none;
  margin-left: -5px;
  justify-content: left;
}
@media screen and (max-width: 600px) {
  .news-singlepage .toppage .backbutton {
    margin-top: 15px;
  }
}
.news-singlepage .toppage h3 {
  color: #77C2A1;
  font-weight: bold;
}
.news-singlepage .toppage.last {
  margin-top: 36px;
  margin-bottom: 100px;
}
@media screen and (max-width: 600px) {
  .news-singlepage .toppage.last {
    margin-top: 65px;
    margin-bottom: 80px;
  }
}
.news-singlepage .postthumbnail {
  margin-top: 67px;
}
@media screen and (max-width: 768px) {
  .news-singlepage .postthumbnail img {
    margin-bottom: 15px;
  }
}
.news-singlepage .posttestinomy {
  margin-top: 103px;
  margin-bottom: 103px;
}
.news-singlepage .posttestinomy .entry-testinomy {
  display: flex;
  flex-direction: row;
  flex-grow: 0;
}
@media screen and (max-width: 991px) {
  .news-singlepage .posttestinomy .entry-testinomy {
    margin-bottom: 20px;
  }
}
.news-singlepage .posttestinomy .entry-testinomy .boxtestinomy {
  align-items: center;
  justify-content: center;
}
.news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.testinomy-bar {
  height: 160px;
  flex: 0 0 40px;
}
.news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.testinomy-bar.left {
  background: url(../images/testinomy_bar.svg) no-repeat;
}
.news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.testinomy-bar.right {
  background: url(../images/testinomy_bar.svg) no-repeat;
  transform: rotate(180deg);
}
@media screen and (max-width: 991px) {
  .news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.testinomy-bar {
    height: 148px;
    background-size: contain !important;
  }
}
.news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.quote {
  display: flex;
  flex: 0 0 475px;
  align-items: center;
  justify-content: center;
  padding: 5px 31px;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
}
@media screen and (max-width: 1200px) {
  .news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.quote {
    flex: unset;
  }
}
@media screen and (max-width: 991px) {
  .news-singlepage .posttestinomy .entry-testinomy .boxtestinomy.quote {
    font-size: 11px;
    line-height: 16px;
    padding-left: 5px;
    padding-right: 5px;
  }
}
.news-singlepage .posttestinomy div.lastcol {
  display: flex;
  align-items: end;
}
@media screen and (max-width: 991px) {
  .news-singlepage .posttestinomy .entry-testinomy-name {
    font-size: 11px;
    line-height: 16px;
    padding-left: 45px;
    padding-right: 45px;
  }
}
.news-singlepage .postvideo h1.entry-title {
  margin-bottom: 44px;
}
@media screen and (max-width: 768px) {
  .news-singlepage .postvideo .videocontainer {
    margin-bottom: 15px;
  }
}
.news-singlepage .postpodcast {
  margin-top: 105px;
}
.news-singlepage .postpodcast .audio-player {
  background: white;
  border: 1px solid #dfdfdf;
  width: 50vw;
  max-width: 420px;
  text-align: left;
  display: flex;
  flex-flow: row;
}
@media screen and (max-width: 767px) {
  .news-singlepage .postpodcast .audio-player {
    width: 100%;
    max-width: unset;
  }
}
.news-singlepage .postpodcast .audio-player .album-image {
  height: 106.5px;
  width: 106.5px;
  background-size: cover;
}
.news-singlepage .postpodcast .audio-player .player-controls {
  align-items: center;
  justify-content: center;
  flex: 3;
  position: relative;
}
.news-singlepage .postpodcast .audio-player .player-controls p {
  line-height: 16px;
  padding-top: 15px;
  padding-left: 20px;
  margin-bottom: 0;
}
.news-singlepage .postpodcast .audio-player .player-controls .audiotitle {
  font-size: 14px;
  font-weight: 700;
  display: block;
  max-height: 34.2px;
  overflow: hidden;
}
.news-singlepage .postpodcast .audio-player .player-controls .audiosubtitle {
  font-size: 11px;
  display: block;
  padding-top: 3px;
}
.news-singlepage .postpodcast .audio-player .player-controls progress {
  width: 100%;
  position: absolute;
  bottom: 0;
}
.news-singlepage .postpodcast .audio-player .player-controls progress[value] {
  -webkit-appearance: none;
  appearance: none;
  background-color: #ADDAC7;
  color: #6ead93;
  height: 10px;
}
.news-singlepage .postpodcast .audio-player .player-controls progress[value]::-webkit-progress-bar {
  background-color: #ADDAC7;
  border-radius: 2px;
  color: #6ead93;
}
.news-singlepage .postpodcast .audio-player .player-controls progress::-webkit-progress-value {
  background-color: #6ead93;
}
.news-singlepage .postpodcast .audio-player .player-controls p {
  font-size: 1.6rem;
}
.news-singlepage .postpodcast .audio-player #play-btn {
  background-image: url("../images/audio-playicon.png");
  background-size: cover;
  width: 50px;
  height: 50px;
  position: absolute;
  left: 43px;
  top: 30px;
  cursor: pointer;
  transition: 200ms;
  box-shadow: 0 0 5px gray;
  border-radius: 50px;
  background-color: #77C2A1;
}
.news-singlepage .postpodcast .audio-player #play-btn.pause {
  background-image: url("../images/audio-pauseicon.png");
}
.news-singlepage .postpodcast .audio-player #play-btn:hover {
  transform: scale(1.05);
}
.news-singlepage .postdownload {
  margin-top: 105px;
}
.news-singlepage .postdownload .download-items {
  display: flex;
  flex-wrap: wrap;
}
.news-singlepage .postdownload .download-items .download-item {
  /*
  flex: 0 0 47%;
  margin-right: 3%;
  */
  width: 380px;
  max-width: 100%;
  margin-right: 30px;
}
@media screen and (max-width: 991px) {
  .news-singlepage .postdownload .download-items .download-item {
    flex: 1 0 100%;
    margin-right: 0;
  }
}
.news-singlepage .postdownload .download-items .download-item .itembox {
  width: 100%;
  display: block;
  padding: 17.5px 53px 17.5px 10px;
  font-size: 14px;
  line-height: 28px;
  border-bottom: 1px solid #878787;
  color: #000;
  text-decoration: none;
  background-image: url("../images/download-icon-back.svg");
  background-repeat: no-repeat;
  background-position: right 10px center;
}
.news-singlepage .postdownload .download-items .download-item .itembox:hover {
  color: #77C2A1;
  background-image: url("../images/download-icon-green.svg");
}
.news-singlepage .postdownload .download-items .download-item:nth-child(1) .itembox, .news-singlepage .postdownload .download-items .download-item:nth-child(2) .itembox {
  border-top: 1px solid #878787;
}
@media screen and (max-width: 1199px) {
  .news-singlepage .postdownload .download-items .download-item:nth-child(2) .itembox {
    border-top: none;
  }
}
.news-singlepage #video-overlay {
  display: inline-block;
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  max-width: 600px;
  height: auto;
  min-height: 371px;
}
@media (max-width: 1200px) {
  .news-singlepage #video-overlay {
    min-height: 254px;
  }
}
@media (max-width: 767px) {
  .news-singlepage #video-overlay {
    -webkit-appearance: none;
    min-height: 290px;
    display: block;
  }
}
@media (max-width: 470px) {
  .news-singlepage #video-overlay {
    -webkit-appearance: none;
    min-height: 240px;
    display: block;
  }
}
.news-singlepage #video-overlay::before {
  content: "";
  width: 45px;
  height: 46px;
  position: absolute;
  display: block;
  background-image: url(../images/videoplay-icon.png);
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 50px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  transition: 200ms;
  box-shadow: 0 0 5px gray;
}
.news-singlepage #video-overlay:hover::before {
  transform: scale(1.05);
}
@media (min-width: 1024px) {
  .news-singlepage #VideoModal iframe {
    width: 800px !important;
    height: 450px !important;
  }
}
.news-singlepage .modal .modal-content {
  border: none;
  border-radius: none;
  background: none;
}
.news-singlepage .modal .modal-body {
  padding: 0 !important;
}

form ul {
  padding: 0;
}
form ul .sf-field-taxonomy-distributor_categories {
  width: 100% !important;
  border: 0 !important;
  padding: 0;
}
form ul .sf-field-taxonomy-distributor_categories > ul {
  display: flex;
  width: 100% !important;
  position: initial !important;
  visibility: unset !important;
  height: auto !important;
  border: 0 !important;
  background: transparent !important;
  padding: 0;
}
@media screen and (max-width: 768px) {
  form ul .sf-field-taxonomy-distributor_categories > ul {
    display: block;
  }
}
form ul .sf-field-taxonomy-distributor_categories > ul li {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  position: relative;
}
@media screen and (max-width: 768px) {
  form ul .sf-field-taxonomy-distributor_categories > ul li {
    display: inline-block;
  }
}
@media screen and (max-width: 485px) {
  form ul .sf-field-taxonomy-distributor_categories > ul li {
    margin-right: 15px;
  }
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-item-0:first-child {
  font-weight: 700;
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-option-active {
  padding: 0;
  background: transparent;
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-option-active .sf-label-radio {
  font-weight: 700;
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-option-active .sf-label-radio:after {
  transform: scaleX(1);
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-option-active:after {
  transform: scaleX(1);
}
form ul .sf-field-taxonomy-distributor_categories > ul li.sf-option-active:after:hover {
  display: none;
}
form ul .sf-field-taxonomy-distributor_categories > ul li .sf-input-radio {
  display: none;
}
form ul .sf-field-taxonomy-distributor_categories > ul li .sf-label-radio {
  padding: 0;
  font-size: 14px;
  font-weight: 100;
  line-height: 1.2;
  color: #000;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  transition: all 0.2s ease;
}
@media screen and (max-width: 485px) {
  form ul .sf-field-taxonomy-distributor_categories > ul li .sf-label-radio {
    font-size: 20px;
  }
}
form ul .sf-field-taxonomy-distributor_categories > ul li .sf-label-radio:before {
  content: attr(data-text);
  content: attr(data-text)/"";
  height: 0;
  visibility: hidden;
  overflow: hidden;
  user-select: none;
  pointer-events: none;
  font-weight: 700;
}
form ul .sf-field-taxonomy-distributor_categories > ul li .sf-label-radio:after:hover {
  font-weight: 700;
  text-decoration: none;
}
form ul .sf-field-taxonomy-distributor_categories > ul li:after {
  content: '';
}
form ul .sf-field-taxonomy-distributor_categories > ul li:hover .sf-label-radio {
  color: #000;
  -webkit-text-fill-color: #000;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #000;
  text-decoration: none;
}
form ul .sf-field-taxonomy-distributor_categories > ul li:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}
form ul .sf-field-taxonomy-distributor_categories:after {
  display: none;
}

.single-jobpage {
  padding-top: 87px;
}
.single-jobpage .header-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 54px;
}
.single-jobpage .header-wrapper::before {
  content: '';
  display: inline-block;
  background: url("../images/subscribe-vector-left.svg") no-repeat;
  background-position: left;
  background-size: contain;
  width: 15px;
  height: 35px;
  left: -10px;
}
.single-jobpage .header-wrapper::after {
  content: '';
  display: inline-block;
  background: url("../images/subscribe-vector-right.svg") no-repeat;
  background-position: right;
  background-size: contain;
  width: 15px;
  height: 35px;
  right: -10px;
}
.single-jobpage .header-wrapper h3 {
  display: inline-block;
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: #77C2A1;
  margin: 0;
}
.single-jobpage .entry-title {
  /*
  font-size: 40px;
  line-height: 48px;
  */
  font-weight: 700;
  margin-bottom: 54px;
  /*padding-bottom: 54px;*/
}
@media screen and (max-width: 991px) {
  .single-jobpage .entry-title {
    /*font-size: 18px;*/
  }
}
@media screen and (max-width: 428px) {
  .single-jobpage .entry-title {
    word-break: break-word;
  }
}
.single-jobpage .entry-title span {
  font-weight: 400;
}
.single-jobpage .section-headline {
  color: #77C2A1;
  /*
  font-size: 18px;
  line-height: 28px;
  */
  font-weight: 700;
}
@media screen and (max-width: 991px) {
  .single-jobpage .section-headline {
    /*font-size: 14px;*/
  }
}
@media screen and (max-width: 767px) {
  .single-jobpage .section-headline br {
    display: none;
  }
}
.single-jobpage .pd-left-col {
  padding-left: 35%;
}
@media screen and (max-width: 991px) {
  .single-jobpage .pd-left-col {
    padding-left: 0;
  }
}
.single-jobpage .toppage h3 {
  color: #77C2A1;
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
}
.single-jobpage .toppage.last {
  margin-top: 36px;
  margin-bottom: 100px;
}
@media screen and (max-width: 768px) {
  .single-jobpage .toppage.last {
    margin-bottom: 0px;
  }
}
.single-jobpage .entry-content ul {
  list-style: none;
  line-height: 2;
}
.single-jobpage .entry-content ul li::before {
  content: "\2022";
  color: #77C2A1;
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}
.single-jobpage .postcontent.job-response {
  padding-top: 30px;
  /*
  ul {
      list-style: none;
      line-height: 2;
  }
    
  ul li::before {
      content: "\2022";
      color: #77C2A1;
      font-weight: bold;
      display: inline-block; 
      width: 1em;
      margin-left: -1em;
  }
  */
}
.single-jobpage .postcontent.job-response ul {
  padding-left: 15px;
}
.single-jobpage .jobdownload {
  margin-top: 30px;
  margin-bottom: 170px;
}
@media screen and (max-width: 767px) {
  .single-jobpage .jobdownload {
    margin-bottom: 80px;
  }
}
.single-jobpage .jobdownload .download-content {
  display: flex;
  flex-direction: row;
}
@media screen and (max-width: 810px) {
  .single-jobpage .jobdownload .download-content {
    flex-direction: column;
  }
}
.single-jobpage .jobdownload .download-content .download-list {
  box-sizing: border-box;
  flex: 0 0 50%;
  margin-right: 5%;
  flex-grow: inherit;
}
@media screen and (max-width: 575px) {
  .single-jobpage .jobdownload .download-content .download-list {
    margin-right: 3%;
  }
}
.single-jobpage .jobdownload .download-content .download-item {
  position: relative;
  margin-bottom: -1px;
}
.single-jobpage .jobdownload .download-content .download-item a {
  display: block;
  padding: 19px 0px;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
  font-size: 14px;
  font-weight: 400;
  text-decoration: none !important;
  color: #000000;
}
.single-jobpage .jobdownload .download-content .download-item a::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  display: block;
  width: 33px;
  height: 32px;
  background-image: url(../images/dw-icon.svg);
  background-repeat: no-repeat;
  transition: all 0.2s ease;
  background-position: center;
  transform: translateY(-50%);
}
.single-jobpage .jobdownload .download-content .download-item:hover a {
  color: #77C2A1;
  font-weight: 700;
}
.single-jobpage .jobdownload .download-content .download-item:hover a::after {
  background-image: url(../images/dw-icon-hover.svg);
}
.single-jobpage .jobdownload .download-content .download-item:hover .no-file {
  color: #77C2A1;
  font-weight: 700;
}
.single-jobpage .jobdownload .download-content .download-item:hover .no-file::after {
  background-image: url(../images/dw-icon-hover.svg);
}
.single-jobpage .jobdownload .download-content .download-item .no-file {
  display: block;
  padding: 19px 0px;
  border-top: 1px solid #878787;
  border-bottom: 1px solid #878787;
  font-size: 14px;
  font-weight: 400;
  text-decoration: none !important;
  color: #000000;
}
.single-jobpage .jobdownload .download-content .download-item .no-file::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  display: block;
  width: 33px;
  height: 32px;
  background-image: url(../images/dw-icon.svg);
  background-repeat: no-repeat;
  transition: all 0.2s ease;
  background-position: center;
  transform: translateY(-50%);
}
.single-jobpage .gobacktoblog a.btn-back {
  justify-content: left;
  margin-left: -5px;
}

@media screen and (max-width: 770px) {
  .site-content .container.single-jobpage {
    padding: 60px 20px 0px 20px;
  }
}
.testimony-list .testimony-item .card {
  padding-top: 42px;
  padding-bottom: 42px;
  border: 0px;
  border-bottom: 1px solid #878787;
  border-radius: 0px;
}
.testimony-list .testimony-item:first-child .card {
  border-top: 1px solid #878787;
}
.testimony-list .testimony-item .thumb {
  width: 181px;
  margin-bottom: 20px;
}
.testimony-list .testimony-item .thumb-empty {
  width: 181px;
  height: 174px;
  margin-bottom: 21px;
  background: transparent;
}
.testimony-list .testimony-item .name {
  font-size: 15px;
  font-weight: 700;
  color: #77C2A1;
  margin-bottom: 10px;
}
.testimony-list .testimony-item .name::before, .testimony-list .testimony-item .name::after {
  content: '';
  width: 5px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.testimony-list .testimony-item .name::before {
  content: '';
  background: url("../images/testimony-name-left.svg") no-repeat left center;
  margin-right: 5px;
}
.testimony-list .testimony-item .name::after {
  background: url("../images/testimony-name-right.svg") no-repeat right center;
  margin-left: 5px;
}
.testimony-list .testimony-item .link-in a {
  font-size: 29px;
  color: #878787;
}
.testimony-list .testimony-item .link-in a:hover {
  color: #77C2A1;
}
.testimony-list .testimony-item .card-body {
  width: 809px;
  max-width: 100%;
  padding: 0px;
}
@media screen and (max-width: 991px) {
  .testimony-list .testimony-item .card-body {
    padding-right: 0px;
    padding-left: 40px;
  }
}
@media screen and (max-width: 767px) {
  .testimony-list .testimony-item .card-body {
    padding-right: 0px;
    padding-left: 0px;
  }
}
.testimony-list .testimony-item .card-body .card-title {
  margin-bottom: 0px;
  font-weight: 700;
}
.testimony-list .testimony-item .card-body .card-text {
  margin-bottom: 30px;
}

.loadmore-section {
  margin-top: 100px;
}
@media screen and (max-width: 991px) {
  .loadmore-section {
    margin-top: 50px;
    margin-bottom: 40px;
  }
}
.loadmore-section .spinner-border {
  margin-left: 10px;
  display: none;
}

body.resource-library .childTabMenu,
body.single-products .childTabMenu {
  background-color: #1D418E;
}
body.resource-library .childTabMenu .nav,
body.single-products .childTabMenu .nav {
  justify-content: center !important;
}
body.resource-library .childTabMenu .nav .nav-item,
body.single-products .childTabMenu .nav .nav-item {
  width: auto;
  max-width: unset;
  margin-left: 110px;
  margin-right: 110px;
}
body.resource-library .resource-search,
body.single-products .resource-search {
  align-items: flex-start;
}
body.resource-library .resource-search .form-group,
body.single-products .resource-search .form-group {
  max-width: 100%;
  margin-bottom: 15px;
  position: relative;
}
body.resource-library .resource-search input[type=text],
body.single-products .resource-search input[type=text] {
  /*width: 1028px;*/
  height: 40px;
  max-width: 100%;
  margin-right: 23px;
  background: transparent;
  border: 1px solid #878787;
  border-radius: 0px;
}
body.resource-library .resource-search input[type=text]::-webkit-input-placeholder,
body.single-products .resource-search input[type=text]::-webkit-input-placeholder {
  /* Edge */
  color: #C4BFC0;
  opacity: 1;
}
body.resource-library .resource-search input[type=text]:-ms-input-placeholder,
body.single-products .resource-search input[type=text]:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #C4BFC0;
  opacity: 1;
}
body.resource-library .resource-search input[type=text]::placeholder,
body.single-products .resource-search input[type=text]::placeholder {
  color: #C4BFC0;
  opacity: 1;
}
body.resource-library .resource-search .icon-search::after,
body.single-products .resource-search .icon-search::after {
  content: '';
  width: 16px;
  height: 16px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url("../images/search-icon.svg");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
body.resource-library .resource-search button,
body.single-products .resource-search button {
  border-radius: 0px;
}
body.resource-library .searchandfilter,
body.single-products .searchandfilter {
  position: relative;
}
body.resource-library .searchandfilter ul,
body.single-products .searchandfilter ul {
  width: 100%;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  display: flex;
  flex-flow: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  /*
  li.sf-field-taxonomy-targets{
      ul li label{
          font-size: 11px;
          &::after{
              width: 10px !important;
              height: 10px !important;
          }
      }
  }
  */
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul,
  body.single-products .searchandfilter ul {
    display: block;
  }
}
body.resource-library .searchandfilter ul li,
body.single-products .searchandfilter ul li {
  padding: 0;
  margin: 0;
}
body.resource-library .searchandfilter ul li::before,
body.single-products .searchandfilter ul li::before {
  display: none;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li,
  body.single-products .searchandfilter ul li {
    width: 100%;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox],
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox],
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] {
  flex: 1 1 0px;
  margin-right: 30px;
  position: relative;
  cursor: pointer;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox],
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio],
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio],
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox],
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox],
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio],
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio],
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] {
    /*
    border-left: 1px solid #878787 !important;
    border-right: 1px solid #878787 !important;
    */
    /*
    &:first-child{
        border-top: 1px solid #878787 !important;;
    }
    */
    position: relative;
  }
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox]::after,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio]::after,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio]::after,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox]::after,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox]::after,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio]::after,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio]::after,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox]::after {
    content: '\f107';
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #77C2A1;
    position: absolute;
    right: 0px;
    top: 38px;
    display: block;
    z-index: 99;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] h4 {
  font-size: 14px;
  font-weight: bold;
  color: #77C2A1;
  margin: 0;
  padding-bottom: 10px !important;
  padding: 0;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] h4,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] h4,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] h4,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] h4,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] h4,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] h4,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] h4,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] h4 {
    padding-top: 10px;
    padding-bottom: 0px !important;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul {
  max-height: 160px;
  overflow-y: scroll;
  scrollbar-color: #68C29F transparent;
  scrollbar-width: thin;
  width: 100%;
  margin: 0;
  padding: 0px;
  padding-top: 1px;
  border-radius: 0px;
  display: block;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul {
    /*
    height: 0 !important;
    visibility: hidden !important;
    display: none !important;
    */
    width: 100% !important;
    height: 41px !important;
    padding-top: 0px;
    border: 1px solid #878787 !important;
    overflow: hidden;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar {
  width: 8px;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar-track,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar-track,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar-track,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar-track,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar-track,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar-track,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar-track,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar-track {
  background-color: transparent;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar-thumb,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar-thumb,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar-thumb,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar-thumb,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul::-webkit-scrollbar-thumb,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul::-webkit-scrollbar-thumb,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul::-webkit-scrollbar-thumb,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul::-webkit-scrollbar-thumb {
  background-color: #68C29F;
  border-radius: 2000px;
  height: 21px;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li {
  line-height: 24px !important;
  color: #000000;
  position: relative;
  padding: 7px 10px !important;
  font-weight: 500;
  display: block;
  border-top: 1px solid #878787 !important;
  border-bottom: 1px solid #878787 !important;
  margin-top: -1px;
  margin-right: 0px;
  background: #f2f2f2;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li {
    /*
    border-top: 1px solid #878787 !important;
    border-left: 1px solid #878787 !important;
    border-right: 1px solid #878787 !important;
    border-bottom: 0px !important;
    */
    margin-top: 0px;
    line-height: 40px !important;
    border: 0px !important;
    width: 100%;
    display: none;
  }
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:first-child,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li:first-child,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:first-child,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:first-child,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:first-child,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li:first-child,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:first-child,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:first-child {
    display: list-item;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li::before,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li::before,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li::before,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li::before,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li::before,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li::before,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li::before,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li::before {
  display: none;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:first-child,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li:first-child,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:first-child,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:first-child,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:first-child,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li:first-child,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:first-child,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:first-child {
  visibility: visible;
  /*border-top: 1px solid #878787 !important;*/
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li input[type="radio"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li input[type="checkbox"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li input[type="radio"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li input[type="checkbox"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li input[type="radio"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li input[type="checkbox"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li input[type="radio"],
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li input[type="checkbox"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li input[type="radio"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li input[type="checkbox"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li input[type="radio"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li input[type="checkbox"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li input[type="radio"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li input[type="checkbox"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li input[type="radio"],
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li input[type="checkbox"] {
  width: 0px;
  position: absolute;
  top: 10px;
  visibility: hidden;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li label,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li label,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li label,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li label,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li label,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li label,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li label,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li label {
  color: #000000;
  -webkit-text-fill-color: unset;
  -webkit-text-stroke-width: 0px !important;
  -webkit-text-stroke-color: unset;
  display: block;
  padding-left: 0px;
  position: relative;
  cursor: pointer;
  line-height: 24px !important;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li label,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li label,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li label,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li label,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li label,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li label,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li label,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li label {
    color: #C4BFC0;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover, body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active {
  background-color: #E4F3EC;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover, body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active {
    background-color: transparent;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover label::after, body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover label::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li:hover label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox] ul li.sf-option-active label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li:hover label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio] ul li.sf-option-active label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li:hover label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio] ul li.sf-option-active label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li:hover label::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox] ul li.sf-option-active label::after {
  content: '';
  width: 13px;
  height: 13px;
  display: inline-block;
  vertical-align: middle;
  margin-left: 8px;
  background-image: url("../images/checked.svg");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  -webkit-text-stroke-width: 0px;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox].active h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio].active h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio].active h4,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox].active h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio].active h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio].active h4,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active h4 {
  color: #77C2A1;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox].active::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio].active::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio].active::after,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox].active::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio].active::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio].active::after,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active::after {
  content: '\f106';
  color: #77C2A1;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio].active ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul,
body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=radio].active ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul,
body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul {
  visibility: visible !important;
  height: auto !important;
  display: block !important;
  max-height: unset;
  overflow: unset;
}
@media screen and (max-width: 991px) {
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio].active ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio].active ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul {
    position: relative;
  }
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=radio].active ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul li,
  body.resource-library .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=checkbox].active ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=radio].active ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-radio].active ul li,
  body.single-products .searchandfilter ul li[data-sf-field-input-type=range-checkbox].active ul li {
    display: list-item;
  }
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=select] label,
body.single-products .searchandfilter ul li[data-sf-field-input-type=select] label {
  width: 100%;
}
body.resource-library .searchandfilter ul li[data-sf-field-input-type=select] select,
body.single-products .searchandfilter ul li[data-sf-field-input-type=select] select {
  width: 100%;
  border-radius: 0;
  border: 1px solid #636363;
}
body.resource-library .searchandfilter ul li.sf-field-reset,
body.single-products .searchandfilter ul li.sf-field-reset {
  width: 100%;
  margin-top: 10px;
}
body.resource-library .searchandfilter ul li.sf-field-reset a,
body.single-products .searchandfilter ul li.sf-field-reset a {
  font-weight: 700;
  color: #000000;
  position: relative;
  text-decoration: none;
}
body.resource-library .searchandfilter ul li.sf-field-reset a::after,
body.single-products .searchandfilter ul li.sf-field-reset a::after {
  content: '';
  width: 15px;
  height: 15px;
  margin-left: 8px;
  display: inline-block;
  vertical-align: middle;
  background-image: url("../images/close-icon.svg");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
body.resource-library .searchandfilter ul li:nth-child(4),
body.single-products .searchandfilter ul li:nth-child(4) {
  margin-right: 0px;
}
body.resource-library .searchandfilter .spinner-warp,
body.single-products .searchandfilter .spinner-warp {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
  background: rgba(255, 255, 255, 0.5);
}
body.resource-library .searchandfilter .spinner-warp .spinner,
body.single-products .searchandfilter .spinner-warp .spinner {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner,
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner {
  width: 100%;
  height: 100%;
  display: inline-block;
  -webkit-animation: rs-revealer-6 1.4s linear infinite;
  animation: rs-revealer-6 1.4s linear infinite;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span,
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span {
  position: absolute;
  vertical-align: top;
  border-radius: 100%;
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-left: 16px;
  transform-origin: center 20px;
  -webkit-transform-origin: center 20px;
  background: #77c2a1;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(2),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(2) {
  transform: rotate(36deg);
  -webkit-transform: rotate(36deg);
  opacity: .1;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(3),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(3) {
  transform: rotate(72deg);
  -webkit-transform: rotate(72deg);
  opacity: .2;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(4),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(4) {
  transform: rotate(108deg);
  -webkit-transform: rotate(108deg);
  opacity: .3;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(5),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(5) {
  transform: rotate(144deg);
  -webkit-transform: rotate(144deg);
  opacity: .4;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(6),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(6) {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  opacity: .5;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(7),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(7) {
  transform: rotate(216deg);
  -webkit-transform: rotate(216deg);
  opacity: .6;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(8),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(8) {
  transform: rotate(252deg);
  -webkit-transform: rotate(252deg);
  opacity: .7;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(9),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(9) {
  transform: rotate(288deg);
  -webkit-transform: rotate(288deg);
  opacity: .8;
}
body.resource-library .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(10),
body.single-products .searchandfilter .spinner-warp .spinner .spinner-inner span:nth-child(10) {
  transform: rotate(324deg);
  -webkit-transform: rotate(324deg);
  opacity: .9;
}
@keyframes rs-revealer-6 {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes rs-revealer-6 {
  from {
    -webkit-transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}
.resource-library-result-count {
  font-size: 32px;
  line-height: 42px;
  font-weight: bold;
  margin-bottom: 36px;
}
.resource-library-result-count span {
  font-size: 18px;
  line-height: 28px;
  color: #ADDAC7;
  margin-left: 28px;
  vertical-align: middle;
  display: inline-block;
  position: relative;
}
.resource-library-result-count span::before, .resource-library-result-count span::after {
  content: '';
  width: 5px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: contain;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.resource-library-result-count span::before {
  background-image: url("../images/resource-library-search-left.svg");
  left: -7px;
}
.resource-library-result-count span::after {
  background-image: url("../images/resource-library-search-right.svg");
  right: -7px;
}

.resource-library-result .button-area {
  padding-top: 80px;
  text-align: center;
}

.resource-library-list {
  padding-top: 39px;
  padding-bottom: 42px;
  border-bottom: 1px solid #878787;
  display: none;
  margin-top: 30px;
  opacity: 0;
  -moz-transition-property: all;
  -o-transition-property: all;
  -webkit-transition-property: all;
  transition-property: all;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -moz-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.resource-library-list.is-visible {
  display: flex;
}
.resource-library-list.is-open {
  margin-top: 0;
  opacity: 1;
}
.resource-library-list:first-child {
  border-top: 1px solid #878787;
}
.resource-library-list:hover {
  background-color: #E4F3EC;
}
.resource-library-list .thumb {
  max-width: 140px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .resource-library-list .thumb {
    margin-bottom: 30px;
  }
}
.resource-library-list .thumb .text-after-thumb {
  font-size: 11px;
  padding: 0px 10px;
}
@media screen and (max-width: 1199px) {
  .resource-library-list .thumb .text-after-thumb {
    padding: 0px;
  }
}
@media screen and (max-width: 767px) {
  .resource-library-list .thumb .text-after-thumb {
    font-size: 14px;
  }
}
@media screen and (max-width: 767px) {
  .resource-library-list .content {
    padding-left: 0;
    padding-right: 0;
  }
}
.resource-library-list .content .title {
  font-weight: bold;
  color: #77C2A1;
  line-height: 28px;
}
.resource-library-list .content .text-after-title {
  font-style: italic;
  line-height: 26px;
}
.resource-library-list .content .short-description {
  margin-top: 30px;
  margin-bottom: 24px;
  padding-right: 95px;
  line-height: 26px;
}
@media screen and (max-width: 1199px) {
  .resource-library-list .content .short-description {
    padding-right: 0px;
  }
}
.resource-library-list .content .short-description .more-link,
.resource-library-list .content .short-description .less-link {
  display: block;
  text-decoration: none;
}
.resource-library-list .content .short-description .more-link {
  margin-top: 1rem;
}
.resource-library-list .content .action {
  display: flex;
}
.resource-library-list .content .action > div {
  margin-right: 66px;
}
.resource-library-list .content .action > div:last-child {
  margin-right: 0px;
}
.resource-library-list .content .action a {
  color: #000000;
  text-decoration: none;
  display: inline-block;
  vertical-align: middle;
}
.resource-library-list .content .action .view a::before {
  content: '';
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 13px;
  background-image: url("../images/preview-icon.svg");
  background-repeat: no-repeat;
  background-size: contain;
}
.resource-library-list .content .action .view a:hover {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.resource-library-list .content .action .view a:hover::before {
  background-image: url("../images/preview-icon-hover.svg");
}
.resource-library-list .content .action .download a::before {
  content: '';
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 13px;
  background-image: url("../images/dw-icon.svg");
  background-repeat: no-repeat;
  background-size: contain;
}
.resource-library-list .content .action .download a:hover {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.resource-library-list .content .action .download a:hover::before {
  background-image: url("../images/dw-icon-hover.svg");
}
.resource-library-list .content .action .external-url a::before {
  content: '';
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 13px;
  background-image: url("../images/arrow-right-black.svg");
  background-repeat: no-repeat;
  background-size: contain;
}
.resource-library-list .content .action .external-url a:hover {
  color: #77C2A1;
  -webkit-text-fill-color: #77C2A1;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #77C2A1;
}
.resource-library-list .content .action .external-url a:hover::before {
  background-image: url("../images/arrow-right-green.svg");
}

@media screen and (max-width: 992px) {
  .site-header {
    -moz-transition-property: all;
    -o-transition-property: all;
    -webkit-transition-property: all;
    transition-property: all;
    -moz-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    -webkit-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .site-header.active-navbar {
    background-color: #1D418E;
    z-index: 9999999999;
  }
  .site-header.active-navbar .navbar-brand img {
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
  }
  .site-header.active-navbar .navbar-toggler-icon {
    background-image: url("../images/navbar-toggler-icon-close.svg") !important;
  }

  #mobile-menu.show {
    position: relative;
  }
  #mobile-menu.show::before {
    content: '';
    width: 100%;
    height: 1px;
    position: absolute;
    box-shadow: 0px 2px 5px 2px rgba(0, 0, 0, 0.06);
  }

  .navbar-mobile {
    padding-top: 43px;
    padding-bottom: 45px;
    background-color: #1D418E;
  }
  .navbar-mobile ul.primary-menu {
    margin-bottom: 22px;
  }
  .navbar-mobile ul.primary-menu li {
    margin-bottom: 10px;
  }
  .navbar-mobile ul.primary-menu li a {
   font-size: 14px; line-height: 26px; color: #ffffff; display: inline-block; width: 100%; text-decoration: none;
  }
	.navbar-mobile ul.primary-menu li::after { right: unset; margin-left: 2px; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-toggle { position: relative; padding-right: 30px !important; display: flex; justify-content: space-between; align-items: center; width: 100%; }
  /* .navbar-mobile ul.primary-menu li.dropdown .dropdown-toggle::after { content: "\f078"; font-family: "Font Awesome 6 Free"; font-weight: 900; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); transition: transform 0.3s ease; color: #ffffff; font-size: 12px; border-top: unset; } */
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-toggle::after { content: url('../images/arrow-down-white.svg');  position: absolute; right: 10px; top: 50%; transform: translateY(-50%); transition: transform 0.3s ease; color: #ffffff; font-size: 12px; border-top: unset; width: 20px; height: 20px; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-toggle.active::after { transform: translateY(-50%) rotate(180deg); }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu { background-color: #1D418E; border: none; border-radius: 0; box-shadow: none; display: none; margin-top: 5px; padding: 0px 10px; position: static; width: 100%; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu.show { display: block; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu .dropdown-item { color: #ffffff; font-size: 13px; line-height: 24px; padding: 8px 20px; background: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.1); display: block; width: 100%; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu .dropdown-item:last-child { border-bottom: none; }
  .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu .dropdown-item:hover, .navbar-mobile ul.primary-menu li.dropdown .dropdown-menu .dropdown-item:focus { background-color: rgba(255, 255, 255, 0.1); color: #ffffff; text-decoration: none; }
  .navbar-mobile ul.top-menu {
    padding-top: 11px;
    padding-bottom: 11px;
    margin-bottom: 43px;
    border-top: 1px solid #ffffff;
    border-bottom: 1px solid #ffffff;
    display: block;
    columns: 2;
    -webkit-columns: 2;
    -moz-columns: 2;
  }
  .navbar-mobile ul.top-menu li a {
    font-size: 14px;
    line-height: 26px;
    color: #ffffff;
    padding: 10px 0px;
    display: block;
  }
  .navbar-mobile ul.top-menu li.wpml-ls-item {
    display: none;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click {
    width: auto;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click a {
    font-size: 14px;
    color: #ffffff;
    padding-left: 0px;
    padding-right: 25px;
    background: none;
    border: 0px;
    display: inline-block;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click .wpml-ls-sub-menu {
    max-width: 38px;
    border: 0px;
    position: relative;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click .wpml-ls-sub-menu > li {
    border-bottom: 1px solid #E4F3EC;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click .wpml-ls-sub-menu > li:last-child {
    border-bottom: 0px;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click .wpml-ls-sub-menu > li a {
    line-height: 26px;
    padding: 4px 0px;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click a.wpml-ls-item-toggle:after {
    width: 10px;
    height: 5px;
    border: 0px;
    background: url("../images/language-switch-arrow-down.svg");
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
    background-size: cover;
    background-repeat: no-repeat;
  }
  .navbar-mobile .wpml-ls-legacy-dropdown-click .wpml-ls-current-language:hover > a,
  .navbar-mobile .wpml-ls-legacy-dropdown-click a:focus,
  .navbar-mobile .wpml-ls-legacy-dropdown-click a:hover {
    background: none;
    color: #ffffff;
    text-decoration: none;
  }
	.navbar-mobile .dropdown .dropdown-menu .dropdown .dropdown-toggle { position: relative; padding-right: 30px !important; display: flex; justify-content: space-between; align-items: center; width: 100%; padding-left: 30px; }
  .navbar-mobile .dropdown .dropdown-menu .dropdown .dropdown-toggle::after { content:url('../images/arrow-down-white.svg');width: 20px; height: 20px; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); transition: transform 0.3s ease; color: #ffffff; font-size: 10px; }
  .navbar-mobile .dropdown .dropdown-menu .dropdown .dropdown-toggle.active::after { transform: translateY(-50%) rotate(180deg); }
  .navbar-mobile .dropdown .dropdown-menu .dropdown .dropdown-menu { background-color: #1D418E; margin-left: 10px; }
  .navbar-mobile .dropdown .dropdown-menu .dropdown .dropdown-menu .dropdown-item { font-size: 12px; padding: 20px 30px; }
  .navbar-mobile li.menu-item-has-children > a { position: relative; padding: 10px 0px !important; display: flex; justify-content: space-between; align-items: center; width: 100%; }
  .navbar-mobile li.menu-item-has-children > a::after { content: url('../images/arrow-down-white.svg'); width: 20px; height: 20px; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); transition: transform 0.3s ease; color: #ffffff; font-size: 12px; }
  .navbar-mobile li.menu-item-has-children > a.active::after { transform: translateY(-50%) rotate(180deg); }
  .navbar-mobile li.menu-item-has-children .sub-menu { background-color: rgba(255, 255, 255, 0.1); border: none; border-radius: 0; box-shadow: none; display: none; margin-top: 5px; padding: 0; position: static; width: 100%; }
  .navbar-mobile li.menu-item-has-children .sub-menu.show { display: block; }
  .navbar-mobile li.menu-item-has-children .sub-menu li a { color: #ffffff; font-size: 13px; line-height: 24px; padding: 8px 20px; background: transparent; border: none; border-bottom: 1px solid rgba(255, 255, 255, 0.1); display: block; width: 100%; }
  .navbar-mobile li.menu-item-has-children .sub-menu li a:hover, .navbar-mobile li.menu-item-has-children .sub-menu li a:focus { background-color: rgba(255, 255, 255, 0.1); color: #ffffff; text-decoration: none; }
  .navbar-mobile li.menu-item-has-children .sub-menu li:last-child a { border-bottom: none; }
  .navbar-mobile li.menu-item-has-children .sub-menu li.menu-item-has-children > a { position: relative; padding: 10px 0px !important; }
  .navbar-mobile li.menu-item-has-children .sub-menu li.menu-item-has-children > a::after { content: url('../images/arrow-down-white.svg');width: 20px; height: 20px; position: absolute; right: 10px; top: 50%; transform: translateY(-50%); transition: transform 0.3s ease; color: #ffffff; font-size: 10px; }
  .navbar-mobile li.menu-item-has-children .sub-menu li.menu-item-has-children > a.active::after { transform: translateY(-50%) rotate(180deg); }
  .navbar-mobile li.menu-item-has-children .sub-menu li.menu-item-has-children .sub-menu { background-color: rgba(255, 255, 255, 0.05); margin-left: 10px; }
  .navbar-mobile li.menu-item-has-children .sub-menu li.menu-item-has-children .sub-menu li a { font-size: 12px; padding: 6px 40px; }
  .navbar-mobile .header-search {
    line-height: 100%;
    position: relative;
  }
  .navbar-mobile .header-search .header-search-form-mobile-icon {
    font-size: 12px;
    line-height: 100%;
    display: inline-block;
    vertical-align: top;
    margin-left: 15px;
    color: #ffffff;
  }
  .navbar-mobile .header-search .header-search-form-mobile-icon img {
    width: 32px;
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(196deg) brightness(107%) contrast(101%);
  }
  .navbar-mobile .header-search .header-search-form {
    width: auto;
    max-width: 100%;
    opacity: 0;
    visibility: hidden;
    background: unset;
    border: 0px;
    position: relative;
    bottom: 0;
    -moz-transition-property: all;
    -o-transition-property: all;
    -webkit-transition-property: all;
    transition-property: all;
    -moz-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    -webkit-transition-duration: 0.2s;
    transition-duration: 0.2s;
    -moz-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    -webkit-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
  }
  .navbar-mobile .header-search .header-search-form form {
    padding: 0px;
  }
  .navbar-mobile .header-search .header-search-form .form-group {
    width: 100%;
    margin-bottom: 0px;
  }
  .navbar-mobile .header-search .header-search-form input[type="submit"] {
    display: none;
  }
  .navbar-mobile .header-search .header-search-form input[type="search"] {
    width: 100%;
    border: 0px;
    background-color: #ffffff;
    border: 1px solid #77c2a1;
    -moz-border-radius: 0px / 0px;
    -webkit-border-radius: 0px 0px;
    border-radius: 0px / 0px;
  }
  .navbar-mobile .header-search.active .header-search-form {
    opacity: 1;
    visibility: visible;
  }
}
/*************** SCROLLBAR BASE CSS ***************/
.scroll-wrapper {
  overflow: hidden !important;
  padding: 0 !important;
  position: relative;
}

.scroll-wrapper > .scroll-content {
  border: none !important;
  box-sizing: content-box !important;
  height: auto;
  left: 0;
  margin: 0;
  max-height: none;
  max-width: none !important;
  overflow: scroll !important;
  padding: 0;
  position: relative !important;
  top: 0;
  width: auto !important;
}

.scroll-wrapper > .scroll-content::-webkit-scrollbar {
  height: 0;
  width: 0;
}

.scroll-element {
  display: none;
}

.scroll-element, .scroll-element div {
  box-sizing: content-box;
}

.scroll-element.scroll-x.scroll-scrollx_visible,
.scroll-element.scroll-y.scroll-scrolly_visible {
  display: block;
}

.scroll-element .scroll-bar,
.scroll-element .scroll-arrow {
  cursor: default;
}

.scroll-textarea {
  border: 1px solid #cccccc;
  border-top-color: #999999;
}

.scroll-textarea > .scroll-content {
  overflow: hidden !important;
}

.scroll-textarea > .scroll-content > textarea {
  border: none !important;
  box-sizing: border-box;
  height: 100% !important;
  margin: 0;
  max-height: none !important;
  max-width: none !important;
  overflow: scroll !important;
  outline: none;
  padding: 2px;
  position: relative !important;
  top: 0;
  width: 100% !important;
}

.scroll-textarea > .scroll-content > textarea::-webkit-scrollbar {
  height: 0;
  width: 0;
}

/*************** SIMPLE INNER SCROLLBAR ***************/
.scrollbar-inner > .scroll-element,
.scrollbar-inner > .scroll-element div {
  border: none;
  margin: 0;
  padding: 0;
  position: absolute;
  z-index: 10;
}

.scrollbar-inner > .scroll-element div {
  display: block;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-x {
  bottom: 2px;
  height: 8px;
  left: 0;
  width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-y {
  height: 100%;
  right: 0px;
  top: 0;
  width: 8px;
}

.scrollbar-inner > .scroll-element .scroll-element_outer {
  overflow: hidden;
}

.scrollbar-inner > .scroll-element .scroll-element_outer,
.scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
}

.scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
  /*
  -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
  filter: alpha(opacity=40);
  opacity: 0.4;
  */
}

.scrollbar-inner > .scroll-element .scroll-element_track {
  background-color: transparent;
}

.scrollbar-inner > .scroll-element .scroll-bar {
  background-color: #68C29F;
  max-height: 21px;
  margin-top: 5px;
}

.scrollbar-inner > .scroll-element:hover .scroll-bar {
  background-color: #68C29F;
}

.scrollbar-inner > .scroll-element.scroll-draggable .scroll-bar {
  background-color: #68C29F;
}

/* update scrollbar offset if both scrolls are visible */
.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
  left: -12px;
}

.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
  top: -12px;
}

.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {
  left: -12px;
}

.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {
  top: -12px;
}

body.search-results .resource-library-list {
  display: flex;
  opacity: 1;
  margin-top: 0;
}
body.search-results .search-result-list a {
  text-decoration: none;
}
body.search-results .search-result-list .more-link {
  display: none !important;
}
body.search-results .wp-pagenavi {
  padding-top: 30px;
  padding-bottom: 30px;
}
body.search-results .wp-pagenavi a {
  text-decoration: none;
}
body.search-results .wp-pagenavi span.current {
  font-weight: normal;
}
body.search-results .wp-pagenavi a,
body.search-results .wp-pagenavi span {
  padding: 3px 8px;
  border: 1px solid #878787;
}

.simple-header-section-404 {
  max-height: 420px;
  min-height: 420px;
  display: flex;
  align-items: center;
  position: relative;
}
@media screen and (max-width: 600px) {
  .simple-header-section-404 {
    min-height: 280px;
  }
}
.simple-header-section-404 .bf-text {
  width: 36px;
  height: 168px;
  fill: #77c2a1;
}
.simple-header-section-404 .at-text {
  width: 36px;
  height: 168px;
  fill: #77c2a1;
}
.simple-header-section-404 .title-box {
  padding: 0px 42px 0px 42px;
  position: relative;
}
.simple-header-section-404 .titlewidget-section {
  display: flex;
  align-items: center;
}
.simple-header-section-404 .titlewidget-section .texttitle h1 {
  font-weight: 700;
  margin-bottom: 20px;
}
.simple-header-section-404 .titlewidget-section .texttitle h2 {
  font-weight: 700;
  margin-bottom: 10px;
}
.simple-header-section-404 .titlewidget-section .titledescription {
  width: 500px;
  max-width: 100%;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  overflow: hidden;
  margin-bottom: 0px;
}
.simple-header-section-404 .titlewidget-section a {
  font-weight: bold;
  text-decoration: none;
}
.simple-header-section-404 .titlewidget-section a:hover {
  text-decoration: underline;
}
@media screen and (max-width: 1199px) and (min-width: 768px) {
  .simple-header-section-404 .bf-text {
    width: 56px;
  }
  .simple-header-section-404 .at-text {
    width: 56px;
  }
}
@media screen and (max-width: 991px) and (min-width: 768px) {
  .simple-header-section-404 .title-box {
    padding: 0px 30px 0px 30px !important;
    max-width: 600px;
  }
}
@media screen and (max-width: 767px) {
  .simple-header-section-404 .titlewidget-section .texttitle h1 {
    /*font-size: 32px;*/
    margin-bottom: 0;
  }
  .simple-header-section-404 .titlewidget-section .texttitle h2 {
    /*font-size: 28px;*/
    margin-bottom: 0;
  }
  .simple-header-section-404 .titlewidget-section .titledescription {
    height: auto;
    width: 352px;
    max-width: 100%;
  }
}
@media screen and (max-width: 581px) {
  .simple-header-section-404 .titlewidget-section .texttitle h1 {
    /*font-size: 32px;*/
    margin-bottom: 0;
  }
  .simple-header-section-404 .titlewidget-section .texttitle h2 {
    /*font-size: 28px;*/
    margin-bottom: 0;
  }
  .simple-header-section-404 .titlewidget-section .titledescription {
    font-size: 14px;
    padding-top: 12px;
    width: unset;
  }
  .simple-header-section-404 .titledescription br {
    display: none;
  }
  .simple-header-section-404 .texttitle br {
    display: none;
  }
}
@media screen and (min-width: 576px) {
  .simple-header-section-404 .bf-text-mobile {
    display: none;
  }
  .simple-header-section-404 .at-text-mobile {
    display: none;
  }
}
@media screen and (max-width: 575px) {
  .simple-header-section-404 .titlewidget-section {
    padding-left: 27px;
    padding-right: 27px;
  }
  .simple-header-section-404 .titlewidget-section .bf-text-mobile {
    position: absolute;
    left: 20px;
  }
  .simple-header-section-404 .titlewidget-section .at-text-mobile {
    position: absolute;
    right: 20px;
  }
  .simple-header-section-404 .titlewidget-section {
    margin: auto;
    align-items: flex-start;
  }
  .simple-header-section-404 .title-box {
    /*padding: @padding-mobile !important;*/
    padding: 0px 40px !important;
  }
  .simple-header-section-404 .texttitle {
    min-height: 120px;
    display: flex;
    align-items: center;
  }
  .simple-header-section-404 .bf-text-mobile {
    display: block;
    fill: #77c2a1;
    /*width: 52px;*/
    width: 27px;
    height: 120px;
  }
  .simple-header-section-404 .at-text-mobile {
    display: block;
    fill: #77c2a1;
    /*width: 52px;*/
    width: 27px;
    height: 120px;
  }
  .simple-header-section-404 .bf-text {
    display: none;
  }
  .simple-header-section-404 .at-text {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  .simple-header-section-404 .title-box {
    padding: 0px 40px;
  }
}

body.modal-open {
  padding-right: 0px !important;
}

body.modal-open .site-header {
  z-index: 99;
}

body.page-template-page-landing {
  padding-top: 0px !important;
}
body.page-template-page-landing .site-footer .site-footer-info {
  padding-top: 44px;
  padding-bottom: 44px;
}
body.page-template-page-landing .site-footer .footer-logo {
  margin-bottom: 0px;
}
body.page-template-page-landing .site-footer .site-footer-copyright {
  border-top: 0px;
  padding-top: 0;
  padding-bottom: 0;
}
body.page-template-page-landing .site-footer .site-footer-copyright ul {
  padding-left: 0;
  padding-right: 0;
}
body.page-template-page-landing .site-footer .site-footer-copyright ul li:last-child {
  margin-right: 0px;
}
@media screen and (max-width: 991px) {
  body.page-template-page-landing .site-footer .site-footer-copyright ul::before {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  body.page-template-page-landing .site-footer .site-footer-copyright ul {
    margin-top: 15px;
  }
  body.page-template-page-landing .site-footer .site-footer-copyright ul li {
    margin-right: 0px;
    margin-bottom: 20px;
    display: block;
  }
}

.modal-sf-form .modal-dialog {
  width: 810px;
  max-width: 100%;
}
@media screen and (max-width: 991px) {
  .modal-sf-form .modal-dialog {
    width: 600px;
  }
}
.modal-sf-form .modal-dialog .modal-content {
  border-radius: 0px;
  box-shadow: 0px 2.7000000477px 6.75px 2.7000000477px rgba(0, 0, 0, 0.06);
  background-color: #fff;
}
.modal-sf-form .modal-dialog .modal-content .modal-body {
  padding: 48px 45px 45px 45px !important;
}
@media screen and (max-width: 576px) {
  .modal-sf-form .modal-dialog .modal-content .modal-body {
    padding-top: 55px;
  }
}
.modal-sf-form .modal-dialog .modal-content .modal-body .close {
  position: absolute;
  top: 16px;
  right: 10px;
  z-index: 9;
  opacity: 1;
}
.modal-sf-form .modal-dialog .modal-content .modal-body .close span {
  content: '';
  width: 34px;
  height: 33px;
  display: block;
  background-image: url("../images/close-modal.png");
  background-repeat: no-repeat;
  background-position: center;
}
.modal-sf-form .modal-dialog .modal-content .modal-body .headline-group {
  margin-bottom: 41px;
}

.modal-thankyou-popup .modal-dialog {
  width: 810px;
  max-width: 100%;
}
@media screen and (max-width: 991px) {
  .modal-thankyou-popup .modal-dialog {
    width: 600px;
  }
}
.modal-thankyou-popup .modal-dialog .modal-content {
  border-radius: 0px;
  box-shadow: 0px 2.7000000477px 6.75px 2.7000000477px rgba(0, 0, 0, 0.06);
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body {
  padding: 48px 45px 45px 45px;
}
@media screen and (max-width: 576px) {
  .modal-thankyou-popup .modal-dialog .modal-content .modal-body {
    padding-top: 55px;
  }
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .close {
  position: absolute;
  top: 16px;
  right: 10px;
  z-index: 9;
  opacity: 1;
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .close span {
  content: '';
  width: 34px;
  height: 33px;
  display: block;
  background-image: url("../images/close-modal.png");
  background-repeat: no-repeat;
  background-position: center;
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .headline-group {
  margin-bottom: 41px;
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .content-group {
  margin-bottom: 41px;
  padding-right: 150px;
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group .panel-grid-cell .panel-cell-style > div {
  display: inline-block;
  margin-bottom: 0px !important;
}
.modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group .panel-grid-cell .panel-cell-style > div:first-child {
  margin-right: 50px;
}
@media screen and (max-width: 576px) {
  .modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group {
    display: block;
  }
  .modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group .panel-grid-cell .panel-cell-style > div {
    display: block;
  }
  .modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group .panel-grid-cell .panel-cell-style > div:first-child {
    margin-right: 0px;
  }
  .modal-thankyou-popup .modal-dialog .modal-content .modal-body .button-group .panel-grid-cell .panel-cell-style > div a.btn-primary {
    width: 100%;
    display: block;
    margin-bottom: 48px;
  }
}

@media screen and (max-width: 991.9px) {
  .so-widget-saleforce-form-theme-widget .modal-content h2 {
    font-size: 40px;
  }
}
@media screen and (max-width: 501px) {
  .so-widget-saleforce-form-theme-widget .modal-content h2 {
    font-size: 28px;
  }
}
.so-widget-saleforce-form-theme-widget .salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"] {
  height: auto;
  width: auto;
}
.so-widget-saleforce-form-theme-widget .salesforce-form .wpcf7-acceptance .wpcf7-list-item label .wpcf7-list-item-label a {
  font-weight: 700;
}
.so-widget-saleforce-form-theme-widget .salesforce-form .wpcf7-acceptance .wpcf7-list-item label input[type="checkbox"]:checked ~ span.wpcf7-list-item-label:after {
  top: 2px;
}

.img-full-width {
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
  max-width: 1000%;
  width: auto;
}
.img-full-width .so-widget-image-out-container-theme-widget .img-out-container-warp {
  width: 100%;
}
.img-full-width .so-widget-image-out-container-theme-widget .img-out-container-warp img {
  width: 100%;
}


.d-nanion-none{display:none!important;}
.two-cards-row.owl-carousel .owl-nav {
	 position: absolute;
	 top: 50%;
	 width: 100%;
	 z-index: 10;
}
.two-cards-row.owl-carousel div.panel-grid-cell {width: 100%!important;}
 .two-cards-row.owl-carousel .owl-nav .owl-prev, .two-cards-row.owl-carousel .owl-nav .owl-next {
	 position: absolute;
	 top: -80px;
	 width: 50px;
	 height: 50px;
   padding: 0;
   margin: 0;
	 background: unset;
	 border: none;
	 border-radius: 50%;
	 display: flex;
	 align-items: center;
	 justify-content: center;
	 transition: all 0.3s ease;
	 cursor: pointer;
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev:hover, .two-cards-row.owl-carousel .owl-nav .owl-next:hover {
	 background: unset;
	 /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); */
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev::before, .two-cards-row.owl-carousel .owl-nav .owl-next::before {
	 content: '';
	 width: 50px;
	 height: 50px;
	 background-image: url('https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow.svg');
	 background-size: contain;
	 background-repeat: no-repeat;
	 background-position: center;
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev:hover::before{
  background: url('https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow-hover.svg');
 transform: rotate(180deg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
   width: 50px;
	 height: 50px;
}
.two-cards-row.owl-carousel .owl-nav .owl-next:hover::before{
  background: url('https://www.nanion.de/wp-content/uploads/2022/10/icon-arrow-hover.svg');
 
  transform: rotate(360deg);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
   width: 50px;
	 height: 50px;
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev span, .two-cards-row.owl-carousel .owl-nav .owl-next span {
	 display: none;
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev {
	 left: -25px;
}
 .two-cards-row.owl-carousel .owl-nav .owl-prev::before {
	 transform: rotate(360deg);
}
 .two-cards-row.owl-carousel .owl-nav .owl-next {
	 right: -25px;
}
 .two-cards-row.owl-carousel .owl-nav .owl-next::before {
	 transform: rotate(180deg);
}
 .two-cards-row.owl-carousel .owl-dots {
	 text-align: center;
	 margin-top: 30px;
}
 .two-cards-row.owl-carousel .owl-dots .owl-dot {
	 display: inline-block;
	 width: 12px;
	 height: 12px;
	 margin: 0 5px;
	 background: #ddd;
	 border: none;
	 border-radius: 50%;
	 transition: all 0.3s ease;
	 cursor: pointer;
}
 .two-cards-row.owl-carousel .owl-dots .owl-dot:hover {
	 background: #bbb;
}
 .two-cards-row.owl-carousel .owl-dots .owl-dot.active {
	 background: #77C2A1;
}
 .two-cards-row.owl-carousel .owl-dots .owl-dot span {
	 display: none;
}
 .two-cards-row.owl-carousel .owl-stage-outer {
	 position: relative;
}
 /* Desktop styles - ensure navigation is visible on large screens */
 @media (min-width: 992px) {
	 .two-cards-row.owl-carousel .owl-nav {
		 display: block !important;
		 visibility: visible !important;
	}
	 /* .two-cards-row.owl-carousel .owl-dots {
		 display: block !important;
		 visibility: visible !important;
	} */
	 .two-cards-row.owl-carousel .owl-nav .owl-prev {
		 left: -50px;
		 display: flex !important;
	}
	 .two-cards-row.owl-carousel .owl-nav .owl-next {
		 right: -50px;
		 display: flex !important;
	}
	 /* Force override of Owl's disabled state */
	  .two-cards-row.owl-carousel .owl-nav.disabled {
		 display: block !important;
		 visibility: visible !important;
	}
	 /* .two-cards-row.owl-carousel .owl-dots.disabled {
		 display: block !important;
		 visibility: visible !important;
	}  */
}

 @media (max-width: 991px) {
	 .two-cards-row.owl-carousel .owl-nav .owl-prev {
		 left: -20px;
	}
	 .two-cards-row.owl-carousel .owl-nav .owl-next {
		 right: -20px;
	}
	 .two-cards-row.owl-carousel .owl-nav .owl-prev, .two-cards-row.owl-carousel .owl-nav .owl-next {
		 width: 60px;
		 height: 60px;
	}
	 .two-cards-row.owl-carousel .owl-nav .owl-prev::before, .two-cards-row.owl-carousel .owl-nav .owl-next::before {
		 width: 40px;
		 height: 40px;
	}
	.bg-module-slider .tp-bullets { 
        bottom: -15px!important;
    }
}
 @media (max-width: 600px) {
	 .two-cards-row.owl-carousel .owl-nav .owl-prev {
		 left: -15px;
	}
	 .two-cards-row.owl-carousel .owl-nav .owl-next {
		 right: -15px;
	}
}
div.bg-module-slider rs-slides rs-slide rs-sbg-wrap rs-sbg.gingham:before {
      background-color: rgba(0, 0, 0, 0.25);
 }

/* ==========================================================================
   SLICK SLIDER CUSTOM STYLES - Two Cards Row
   ========================================================================== */

/* Two Cards Row Slick Slider Container */
.two-cards-row.slick-slider {
    position: relative;
    margin: 0;
}

.two-cards-row.slick-slider .slick-list {
    margin: 0;
    padding: 0;
}
.two-cards-row.slick-slider .slick-list.draggable {
    width: 100%;
    max-width: 100%;
}
.two-cards-row.slick-slider .slick-track {
    display: flex;
    align-items: stretch;
}

.two-cards-row.slick-slider .slick-slide {
    padding: 0 15px !important;
    box-sizing: border-box;
}

/* Custom Navigation Arrows - Matching your design */
.two-cards-row .custom-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    padding: 0;
}

.two-cards-row .custom-arrow:hover {
    background: #77C2A1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}
.two-cards-row .custom-arrow:hover img{
    filter: invert(100%) brightness(100);
}
.two-cards-row .custom-arrow:focus {
    outline: none;
}

/* Arrow Icons */
/* .two-cards-row .custom-arrow .arrow-icon {
    width: 20px;
    height: 20px;
    display: block;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
} */

/* .two-cards-row .custom-arrow-prev .arrow-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>');
}

.two-cards-row .custom-arrow-next .arrow-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>');
}

.two-cards-row .custom-arrow:hover .arrow-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>');
}

.two-cards-row .custom-arrow-next:hover .arrow-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>');
} */

/* Arrow Positioning */
.two-cards-row .custom-arrow-prev {
    left: -25px;
}

.two-cards-row .custom-arrow-next {
    right: -25px;
}

/* Custom Dots Navigation - Matching your design */
.two-cards-row .slick-dots {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    list-style: none;
    margin: 0;
    padding: 0;
    width: 20px;
    height: auto;
    bottom: auto;
}

.two-cards-row .slick-dots li {
    width: 12px;
    height: 12px;
    margin: 6px 0;
    display: block;
}

.two-cards-row .slick-dots li button {
    width: 12px;
    height: 12px;
    border: 2px solid #cccccc;
    border-radius: 50%;
    background: transparent;
    padding: 0;
    font-size: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.two-cards-row .slick-dots li button:hover,
.two-cards-row .slick-dots li button:focus {
    outline: none;
    border-color: #77C2A1;
}

.two-cards-row .slick-dots li.slick-active button {
    background: #77C2A1;
    border-color: #77C2A1;
}
/* @media (min-width: 992px) { */
    .two-cards-row .custom-arrow-prev {
        left: 0px;
        top: 55%;
        transform: rotate(270deg);
    }
    
    .two-cards-row .custom-arrow-next {
        right: unset;
        left: 0px;
        top: 43%;
        transform: rotate(-270deg);
    }
     .two-cards-row.slick-slider{
    justify-content: center!important;
    align-items: center!important;
  }
  .two-cards-row.slick-slider .slick-list.draggable {
    max-width: 90%;
}
    .two-cards-row .slick-dots {
        /* display: none !important;  */
    }
/* } */
/* Responsive Design */
@media (max-width: 1335px){
  /* .two-cards-row.slick-slider{
    justify-content: center!important;
    align-items: center!important;
  } */
  .two-cards-row.slick-slider .slick-list.draggable {
    max-width: 70%;
}
.two-cards-row .slick-dots {
    right: 50px;
}
.two-cards-row .custom-arrow-prev,
.two-cards-row .custom-arrow-next{
    left: 80px;
}
}

@media (max-width: 1280px){
  .two-cards-row.slick-slider .slick-list.draggable {
    max-width: 90%;
}
.two-cards-row .custom-arrow-prev,
.two-cards-row .custom-arrow-next{
    left: -10px;
}
}
@media (max-width: 991px) {
    .two-cards-row .custom-arrow-prev {
        left: -25px;
        top: 43%;
        transform: rotate(360deg);
    }
    
    .two-cards-row .custom-arrow-next {
        right: -30px;
        left: unset;
    }
    .two-cards-row .custom-arrow-next img{
        transform: rotate(90deg);
    }
    .two-cards-row .slick-dots {
        position: static;
        display: flex !important;
        flex-direction: row;
        justify-content: center;
        margin-top: 30px;
        transform: none;
        right: auto;
        top: auto;
        width: 100%;
        height: auto;
    }
    
    .two-cards-row .slick-dots li {
        margin: 0 6px;
    }
    div.two-cards-row.slick-slider .slick-list.draggable {
    max-width: 100%;
    }
}

@media (max-width: 600px) {
    .two-cards-row .custom-arrow {
        width: 40px;
        height: 40px;
    }
    
    .two-cards-row .custom-arrow .arrow-icon {
        width: 16px;
        height: 16px;
    }
    
    .two-cards-row .custom-arrow-prev {
        left: -20px;
    }
    
    .two-cards-row .custom-arrow-next {
        right: -20px;
    }
}